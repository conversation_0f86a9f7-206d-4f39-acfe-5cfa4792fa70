/**
 * Serviço de Rate Limit para votos normais (gratuitos).
 *
 * Regras:
 * - Rate limit por identidade: (restaurantId + clientSessionId) ou (restaurantId + IP)
 * - Janela e limite configuráveis por env.
 *
 * ENV:
 * - NORMAL_VOTE_RATE_LIMIT (default: 1)
 * - NORMAL_VOTE_RATE_WINDOW_SECONDS (default: 180)
 */
export interface VoteRateResult {
    allowed: boolean;
    remaining: number;
    retryAfter?: number;
}
export interface VoteRateCheckOptions {
    clientSessionId?: string;
    ip?: string;
}
declare class VoteRateLimiterService {
    /**
     * Aplica rate limit por (restaurantId + clientSessionId OU ip) para votos normais.
     */
    checkNormalVote(restaurantId: string, opts?: VoteRateCheckOptions): Promise<VoteRateResult>;
}
export declare const voteRateLimiter: VoteRateLimiterService;
export default voteRateLimiter;
//# sourceMappingURL=VoteRateLimiter.d.ts.map