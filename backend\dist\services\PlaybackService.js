"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.playbackService = exports.PlaybackService = exports.PlaybackError = exports.PlaylistTransitionDto = exports.VolumeControlDto = exports.PlaybackActionDto = exports.PlaybackAction = exports.PlaybackTransitionMode = void 0;
const database_1 = require("../config/database");
const CollaborativePlaylistService_1 = require("./CollaborativePlaylistService");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const Suggestion_1 = require("../models/Suggestion");
const PlayHistory_1 = require("../models/PlayHistory");
const Restaurant_1 = require("../models/Restaurant");
const Playlist_1 = require("../models/Playlist");
const PlaylistSchedule_1 = require("../models/PlaylistSchedule");
const WebSocketService_1 = require("./WebSocketService");
const PlaylistAnalyticsService_1 = require("./PlaylistAnalyticsService");
const logger_1 = require("../utils/logger");
/**
 * Enums para estados de reprodução
 */
var PlaybackTransitionMode;
(function (PlaybackTransitionMode) {
    PlaybackTransitionMode["MANUAL"] = "manual";
    PlaybackTransitionMode["AUTOMATIC"] = "automatic";
    PlaybackTransitionMode["SCHEDULED"] = "scheduled";
})(PlaybackTransitionMode = exports.PlaybackTransitionMode || (exports.PlaybackTransitionMode = {}));
var PlaybackAction;
(function (PlaybackAction) {
    PlaybackAction["PLAY"] = "play";
    PlaybackAction["PAUSE"] = "pause";
    PlaybackAction["SKIP"] = "skip";
    PlaybackAction["STOP"] = "stop";
    PlaybackAction["NEXT"] = "next";
})(PlaybackAction = exports.PlaybackAction || (exports.PlaybackAction = {}));
/**
 * Classes de validação
 */
class PlaybackActionDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaybackActionDto.prototype, "restaurantId", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(PlaybackAction),
    __metadata("design:type", String)
], PlaybackActionDto.prototype, "action", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaybackActionDto.prototype, "trackId", void 0);
exports.PlaybackActionDto = PlaybackActionDto;
class VolumeControlDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], VolumeControlDto.prototype, "restaurantId", void 0);
__decorate([
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], VolumeControlDto.prototype, "volume", void 0);
exports.VolumeControlDto = VolumeControlDto;
class PlaylistTransitionDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaylistTransitionDto.prototype, "restaurantId", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PlaylistTransitionDto.prototype, "playlistId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(PlaybackTransitionMode),
    __metadata("design:type", String)
], PlaylistTransitionDto.prototype, "mode", void 0);
exports.PlaylistTransitionDto = PlaylistTransitionDto;
/**
 * Classe de erro personalizada para reprodução
 */
class PlaybackError extends Error {
    constructor(message, code = "PLAYBACK_ERROR", statusCode = 500, isOperational = true, restaurantId, trackId) {
        super(message);
        this.name = "PlaybackError";
        this.code = code;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.restaurantId = restaurantId;
        this.trackId = trackId;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.PlaybackError = PlaybackError;
/**
 * Classe para gerenciar locks avançados com timeout
 */
class PlaybackMutex {
    constructor() {
        this.locks = new Map();
        this.lockTimeout = 30000; // 30 segundos
    }
    async acquire(key) {
        return new Promise((resolve) => {
            if (!this.locks.has(key)) {
                this.locks.set(key, { locked: false, queue: [] });
            }
            const lock = this.locks.get(key);
            if (!lock.locked) {
                lock.locked = true;
                this.setLockTimeout(key);
                resolve();
            }
            else {
                lock.queue.push(resolve);
            }
        });
    }
    release(key) {
        const lock = this.locks.get(key);
        if (!lock)
            return;
        if (lock.timeout) {
            clearTimeout(lock.timeout);
            lock.timeout = undefined;
        }
        if (lock.queue.length > 0) {
            const next = lock.queue.shift();
            this.setLockTimeout(key);
            next();
        }
        else {
            lock.locked = false;
        }
    }
    setLockTimeout(key) {
        const lock = this.locks.get(key);
        if (!lock)
            return;
        lock.timeout = setTimeout(() => {
            logger_1.logger.warn(`Lock timeout para ${key} - forçando liberação`);
            this.forceRelease(key);
        }, this.lockTimeout);
    }
    forceRelease(key) {
        const lock = this.locks.get(key);
        if (!lock)
            return;
        if (lock.timeout) {
            clearTimeout(lock.timeout);
            lock.timeout = undefined;
        }
        lock.locked = false;
        // Processar próximo na fila
        if (lock.queue.length > 0) {
            const next = lock.queue.shift();
            lock.locked = true;
            this.setLockTimeout(key);
            next();
        }
    }
}
/**
 * Serviço de reprodução com gerenciamento avançado de estado
 *
 * @class PlaybackService
 * @description Gerencia reprodução de música, filas, playlists e transições
 * com sistema de locks avançado para prevenir race conditions.
 */
class PlaybackService {
    get suggestionRepository() {
        if (!this._suggestionRepository) {
            this._suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        }
        return this._suggestionRepository;
    }
    get playHistoryRepository() {
        if (!this._playHistoryRepository) {
            this._playHistoryRepository = database_1.AppDataSource.getRepository(PlayHistory_1.PlayHistory);
        }
        return this._playHistoryRepository;
    }
    get restaurantRepository() {
        if (!this._restaurantRepository) {
            this._restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        }
        return this._restaurantRepository;
    }
    get playlistRepository() {
        if (!this._playlistRepository) {
            this._playlistRepository = database_1.AppDataSource.getRepository(Playlist_1.Playlist);
        }
        return this._playlistRepository;
    }
    get playlistScheduleRepository() {
        if (!this._playlistScheduleRepository) {
            this._playlistScheduleRepository = database_1.AppDataSource.getRepository(PlaylistSchedule_1.PlaylistSchedule);
        }
        return this._playlistScheduleRepository;
    }
    constructor() {
        this.playbackStates = new Map();
        this.playbackTimers = new Map();
        this.scheduleTimers = new Map();
        this.isShuttingDown = false;
        this.mutex = new PlaybackMutex();
        this.wsService = WebSocketService_1.WebSocketService.getInstance();
        this.analyticsService = PlaylistAnalyticsService_1.PlaylistAnalyticsService.getInstance();
        // Iniciar verificação de agendamentos a cada minuto
        this.scheduleInterval = setInterval(() => {
            if (this.isShuttingDown)
                return;
            this.checkScheduledPlaylists();
        }, 60000);
        // Limpeza periódica de estados inativos
        this.cleanupInterval = setInterval(() => {
            if (this.isShuttingDown)
                return;
            this.cleanupInactiveStates();
        }, 300000); // 5 minutos
        logger_1.logger.info("PlaybackService inicializado");
    }
    static getInstance() {
        if (!PlaybackService.instance) {
            PlaybackService.instance = new PlaybackService();
        }
        return PlaybackService.instance;
    }
    /**
     * Valida dados de entrada usando class-validator
     * @private
     */
    async validateInput(dto, data) {
        const instance = (0, class_transformer_1.plainToClass)(dto, data);
        const errors = await (0, class_validator_1.validate)(instance);
        if (errors.length > 0) {
            const errorMessages = errors
                .map((error) => Object.values(error.constraints || {}).join(", "))
                .join("; ");
            throw new PlaybackError(`Dados inválidos: ${errorMessages}`, "VALIDATION_ERROR", 400);
        }
        return instance;
    }
    /**
     * Limpa estados inativos para economizar memória
     * @private
     */
    cleanupInactiveStates() {
        const now = new Date();
        const inactiveThreshold = 30 * 60 * 1000; // 30 minutos
        for (const [restaurantId, state] of Array.from(this.playbackStates.entries())) {
            const timeSinceActivity = now.getTime() - state.lastActivity.getTime();
            if (timeSinceActivity > inactiveThreshold && !state.isPlaying) {
                logger_1.logger.info(`Limpando estado inativo para restaurante ${restaurantId}`);
                this.playbackStates.delete(restaurantId);
                // Limpar timers associados
                const timer = this.playbackTimers.get(restaurantId);
                if (timer) {
                    clearTimeout(timer);
                    this.playbackTimers.delete(restaurantId);
                }
                const scheduleTimer = this.scheduleTimers.get(restaurantId);
                if (scheduleTimer) {
                    clearTimeout(scheduleTimer);
                    this.scheduleTimers.delete(restaurantId);
                }
            }
        }
    }
    /**
     * Inicializa estado de reprodução para um restaurante
     * @param restaurantId - ID do restaurante
     * @returns Promise<IPlaybackState> - Estado inicial de reprodução
     * @throws PlaybackError - Quando há erro na inicialização
     */
    async initializePlayback(restaurantId) {
        try {
            await this.mutex.acquire(`init-${restaurantId}`);
            if (!this.playbackStates.has(restaurantId)) {
                // Validar se restaurante existe
                const restaurant = await this.restaurantRepository.findOne({
                    where: { id: restaurantId },
                });
                if (!restaurant) {
                    throw new PlaybackError("Restaurante não encontrado", "RESTAURANT_NOT_FOUND", 404, true, restaurantId);
                }
                // Obter playlist ativa atual
                const currentPlaylist = await this.getCurrentActivePlaylist(restaurantId);
                const nextScheduled = await this.getNextScheduledPlaylist(restaurantId);
                const initialState = {
                    currentTrack: null,
                    isPlaying: false,
                    currentTime: 0,
                    volume: 70,
                    queue: [],
                    priorityQueue: [],
                    normalQueue: [],
                    history: [],
                    currentPlaylist,
                    nextScheduledPlaylist: nextScheduled,
                    transitionMode: PlaybackTransitionMode.AUTOMATIC,
                    isTransitioning: false,
                    problematicTracks: [],
                    lastActivity: new Date(),
                };
                this.playbackStates.set(restaurantId, initialState);
                // Carregar fila inicial e músicas problemáticas
                await Promise.all([
                    this.refreshQueue(restaurantId),
                    this.loadProblematicTracks(restaurantId),
                ]);
                logger_1.logger.info(`Estado de reprodução inicializado para restaurante ${restaurantId}`);
            }
            const state = this.playbackStates.get(restaurantId);
            state.lastActivity = new Date();
            return state;
        }
        catch (error) {
            logger_1.logger.error(`Erro ao inicializar reprodução para restaurante ${restaurantId}:`, error);
            if (error instanceof PlaybackError) {
                throw error;
            }
            throw new PlaybackError(`Falha ao inicializar reprodução: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "INITIALIZATION_FAILED", 500, true, restaurantId);
        }
        finally {
            this.mutex.release(`init-${restaurantId}`);
        }
    }
    /**
     * Reproduz uma faixa específica
     * @param restaurantId ID do restaurante
     * @param trackId ID da faixa a ser reproduzida
     */
    async playTrack(restaurantId, trackId) {
        try {
            await this.mutex.acquire(`play-${restaurantId}`);
            // Inicializar estado se necessário
            await this.initializePlayback(restaurantId);
            const state = this.playbackStates.get(restaurantId);
            // Verificar transição de playlist
            if (state.isTransitioning) {
                logger_1.logger.warn(`Reprodução rejeitada: transição de playlist em andamento para ${restaurantId}`);
                throw new PlaybackError("Transição de playlist em andamento", "PLAYLIST_TRANSITION_IN_PROGRESS", 409, true, restaurantId);
            }
            // Buscar sugestão
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: trackId },
            });
            if (!suggestion) {
                throw new PlaybackError("Faixa não encontrada", "TRACK_NOT_FOUND", 404, true, restaurantId, trackId);
            }
            // Cancelar timer anterior se existir
            const existingTimer = this.playbackTimers.get(restaurantId);
            if (existingTimer) {
                clearTimeout(existingTimer);
                this.playbackTimers.delete(restaurantId);
            }
            // Se há música tocando, marcar como interrompida
            if (state.currentTrack) {
                await this.markAsSkipped(restaurantId, state.currentTrack);
            }
            // Converter para ITrack
            const track = {
                id: suggestion.id,
                title: suggestion.title,
                artist: suggestion.artist || "Artista Desconhecido",
                youtubeVideoId: suggestion.youtubeVideoId,
                duration: suggestion.duration || 180,
                thumbnailUrl: suggestion.thumbnailUrl || "",
                upvotes: suggestion.upvotes || 0,
                downvotes: suggestion.downvotes || 0,
                score: (suggestion.upvotes || 0) - (suggestion.downvotes || 0),
                suggestedBy: suggestion.suggestedBy?.name,
                createdAt: suggestion.createdAt,
            };
            // Atualizar estado
            state.currentTrack = track;
            state.isPlaying = true;
            state.currentTime = 0;
            state.lastActivity = new Date();
            // Refletir início de reprodução na Suggestion (status/playedAt)
            try {
                await this.suggestionRepository
                    .createQueryBuilder()
                    .update(Suggestion_1.Suggestion)
                    .set({ status: Suggestion_1.SuggestionStatus.PLAYING, playedAt: () => "NOW()" })
                    .where("id = :id", { id: trackId })
                    .execute();
            }
            catch (e) {
                logger_1.logger.warn("Falha ao atualizar Suggestion como PLAYING:", e);
            }
            // Remover da fila se estiver presente
            state.priorityQueue = state.priorityQueue.filter((t) => t.id !== track.id);
            state.normalQueue = state.normalQueue.filter((t) => t.id !== track.id);
            state.queue = [...state.priorityQueue, ...state.normalQueue];
            // Agendar fim da música
            this.scheduleTrackEnd(restaurantId, track.duration);
            // Salvar no histórico
            await this.saveToHistory(restaurantId, track);
            // Notificar via WebSocket
            await this.wsService.broadcastToRestaurant(restaurantId, "playback_started", {
                track: track,
                state: state,
            });
            logger_1.logger.info(`Reprodução iniciada: "${track.title}" - ${track.artist} para restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao reproduzir faixa ${trackId} para restaurante ${restaurantId}:`, error);
            if (error instanceof PlaybackError) {
                throw error;
            }
            throw new PlaybackError(`Falha ao reproduzir faixa: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "PLAYBACK_FAILED", 500, true, restaurantId, trackId);
        }
        finally {
            this.mutex.release(`play-${restaurantId}`);
        }
    }
    /**
     * Pausa a reprodução atual
     * @param restaurantId ID do restaurante
     */
    async pausePlayback(restaurantId) {
        try {
            await this.mutex.acquire(`pause-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state || !state.isPlaying || !state.currentTrack) {
                logger_1.logger.warn(`Tentativa de pausar reprodução em restaurante sem música ativa: ${restaurantId}`);
                return;
            }
            // Cancelar timer
            const timer = this.playbackTimers.get(restaurantId);
            if (timer) {
                clearTimeout(timer);
                this.playbackTimers.delete(restaurantId);
            }
            state.isPlaying = false;
            state.lastActivity = new Date();
            // Notificar clientes
            await this.wsService.broadcastToRestaurant(restaurantId, "playback_paused", {
                state: {
                    currentTrack: state.currentTrack,
                    currentTime: state.currentTime,
                    isPlaying: false,
                },
            });
            logger_1.logger.info(`Reprodução pausada para restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao pausar reprodução para restaurante ${restaurantId}:`, error);
            throw new PlaybackError(`Falha ao pausar reprodução: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "PAUSE_FAILED", 500, true, restaurantId);
        }
        finally {
            this.mutex.release(`pause-${restaurantId}`);
        }
    }
    /**
     * Retoma a reprodução pausada
     * @param restaurantId ID do restaurante
     */
    async resumePlayback(restaurantId) {
        try {
            await this.mutex.acquire(`resume-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state || state.isPlaying || !state.currentTrack) {
                logger_1.logger.warn(`Tentativa de retomar reprodução em estado inválido para restaurante ${restaurantId}`);
                return;
            }
            state.isPlaying = true;
            state.lastActivity = new Date();
            // Calcular tempo restante
            const remainingTime = state.currentTrack.duration - state.currentTime;
            if (remainingTime > 0) {
                this.scheduleTrackEnd(restaurantId, remainingTime);
            }
            else {
                // Se não sobrou tempo, finalize a música
                this.onTrackEnded(restaurantId);
                return;
            }
            // Notificar clientes
            await this.wsService.broadcastToRestaurant(restaurantId, "playback_resumed", {
                state: {
                    currentTrack: state.currentTrack,
                    currentTime: state.currentTime,
                    isPlaying: true,
                },
            });
            logger_1.logger.info(`Reprodução retomada para restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao retomar reprodução para restaurante ${restaurantId}:`, error);
            throw new PlaybackError(`Falha ao retomar reprodução: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "RESUME_FAILED", 500, true, restaurantId);
        }
        finally {
            this.mutex.release(`resume-${restaurantId}`);
        }
    }
    /**
     * Pula para a próxima faixa
     * @param restaurantId ID do restaurante
     */
    async skipToNext(restaurantId) {
        try {
            await this.mutex.acquire(`skip-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state) {
                logger_1.logger.warn(`Tentativa de pular faixa em restaurante sem estado: ${restaurantId}`);
                return;
            }
            // Cancelar timer atual
            const timer = this.playbackTimers.get(restaurantId);
            if (timer) {
                clearTimeout(timer);
                this.playbackTimers.delete(restaurantId);
            }
            // Marcar como pulada no histórico
            if (state.currentTrack) {
                await this.markAsSkipped(restaurantId, state.currentTrack);
                // Adicionar ao histórico local
                state.history.unshift(state.currentTrack);
                // Manter apenas últimas 50 músicas no histórico
                if (state.history.length > 50) {
                    state.history = state.history.slice(0, 50);
                }
            }
            // Parar reprodução atual
            state.isPlaying = false;
            state.currentTrack = null;
            state.currentTime = 0;
            state.lastActivity = new Date();
            // Notificar clientes
            await this.wsService.broadcastToRestaurant(restaurantId, "track_skipped", {
                message: "Música pulada manualmente",
                nextAction: "loading_next_track",
            });
            logger_1.logger.info(`Faixa pulada para restaurante ${restaurantId}`);
            // Iniciar próxima música com um pequeno intervalo
            setTimeout(() => {
                if (this.isShuttingDown)
                    return;
                this.startNextTrack(restaurantId);
            }, 1000);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao pular faixa para restaurante ${restaurantId}:`, error);
            throw new PlaybackError(`Falha ao pular faixa: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "SKIP_FAILED", 500, true, restaurantId);
        }
        finally {
            this.mutex.release(`skip-${restaurantId}`);
        }
    }
    /**
     * Inicia a próxima faixa da fila
     * @param restaurantId ID do restaurante
     */
    async startNextTrack(restaurantId) {
        try {
            const nextTrack = await this.getNextTrack(restaurantId);
            if (nextTrack) {
                await this.playTrack(restaurantId, nextTrack.id);
            }
            else {
                // Se não há próxima faixa, verificar se há transição de playlist pendente
                const state = this.playbackStates.get(restaurantId);
                if (state?.currentPlaylist) {
                    // Tentar atualizar a fila antes de desistir
                    await this.refreshQueue(restaurantId);
                    // Verificar novamente após atualização da fila
                    const updatedNextTrack = await this.getNextTrack(restaurantId);
                    if (updatedNextTrack) {
                        await this.playTrack(restaurantId, updatedNextTrack.id);
                    }
                    else {
                        logger_1.logger.warn(`Nenhuma faixa disponível para reprodução no restaurante ${restaurantId}`);
                        // Notificar clientes
                        await this.wsService.broadcastToRestaurant(restaurantId, "queue_empty", {
                            message: "Fila de reprodução vazia",
                            suggestAction: "add_tracks",
                        });
                    }
                }
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro ao iniciar próxima faixa para restaurante ${restaurantId}:`, error);
            // Não lançamos erro aqui para evitar interrupção do fluxo de reprodução
        }
    }
    /**
     * Agendar fim da música atual
     * @private
     */
    scheduleTrackEnd(restaurantId, duration) {
        // Garantir que a duração é um número positivo
        const safeDuration = Math.max(1, Math.min(duration || 180, 600)); // Entre 1s e 10min
        // Cancelar timer anterior se existir
        const existingTimer = this.playbackTimers.get(restaurantId);
        if (existingTimer) {
            clearTimeout(existingTimer);
        }
        const timer = setTimeout(() => {
            this.onTrackEnded(restaurantId);
        }, safeDuration * 1000);
        this.playbackTimers.set(restaurantId, timer);
        logger_1.logger.debug(`Fim de faixa agendado em ${safeDuration}s para restaurante ${restaurantId}`);
    }
    /**
     * Manipulador para quando uma música termina
     * @private
     */
    async onTrackEnded(restaurantId) {
        try {
            await this.mutex.acquire(`track-end-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state || !state.currentTrack) {
                logger_1.logger.warn(`onTrackEnded chamado sem faixa atual para restaurante ${restaurantId}`);
                return;
            }
            logger_1.logger.info(`Faixa terminou: "${state.currentTrack.title}" para restaurante ${restaurantId}`);
            // Marcar como completada no histórico
            await this.markAsCompleted(restaurantId, state.currentTrack);
            // Adicionar ao histórico local
            state.history.unshift(state.currentTrack);
            // Manter apenas últimas 50 músicas no histórico
            if (state.history.length > 50) {
                state.history = state.history.slice(0, 50);
            }
            // Verificar transição de playlist pendente
            const pendingTransition = state.isTransitioning;
            // Limpar estado atual
            state.currentTrack = null;
            state.isPlaying = false;
            state.currentTime = 0;
            state.lastActivity = new Date();
            // Limpar timer
            this.playbackTimers.delete(restaurantId);
            // Notificar fim da música
            await this.wsService.broadcastToRestaurant(restaurantId, "track_ended", {
                state: {
                    isPlaying: false,
                    currentTrack: null,
                    queueSize: state.priorityQueue.length + state.normalQueue.length,
                },
            });
            // Se há transição pendente, executá-la antes de iniciar próxima música
            if (pendingTransition && state.currentPlaylist) {
                await this.executePlaylistTransition(restaurantId, state.currentPlaylist.id);
            }
            else {
                // Iniciar próxima música automaticamente com um pequeno intervalo
                setTimeout(() => {
                    if (this.isShuttingDown)
                        return;
                    this.startNextTrack(restaurantId);
                }, 1000);
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro ao processar fim de faixa para restaurante ${restaurantId}:`, error);
        }
        finally {
            this.mutex.release(`track-end-${restaurantId}`);
        }
    }
    /**
     * Atualiza fila de reprodução com sugestões aprovadas
     * @private
     */
    async refreshQueue(restaurantId) {
        try {
            const state = this.playbackStates.get(restaurantId);
            if (!state)
                return;
            // Manter faixa atual se estiver tocando
            const currentTrackId = state.currentTrack?.id;
            // Buscar sugestões aprovadas ordenadas por score
            const suggestions = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.votes", "vote")
                .where("suggestion.restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.status = :status", { status: "approved" })
                .orderBy("suggestion.isPaid", "DESC") // Sugestões pagas primeiro
                .addOrderBy("suggestion.upvotes - suggestion.downvotes", "DESC")
                .addOrderBy("suggestion.createdAt", "ASC")
                .getMany();
            // Separar filas prioritária e normal
            const prioritySuggestions = suggestions.filter((s) => s.isPaid);
            const normalSuggestions = suggestions.filter((s) => !s.isPaid);
            // Converter para ITrack
            const convertToTrack = (suggestion) => ({
                id: suggestion.id,
                title: suggestion.title,
                artist: suggestion.artist || "Artista Desconhecido",
                youtubeVideoId: suggestion.youtubeVideoId,
                duration: suggestion.duration || 180,
                thumbnailUrl: suggestion.thumbnailUrl || "",
                upvotes: suggestion.upvotes || 0,
                downvotes: suggestion.downvotes || 0,
                score: (suggestion.upvotes || 0) - (suggestion.downvotes || 0),
                suggestedBy: suggestion.suggestedBy?.name,
                createdAt: suggestion.createdAt,
                metadata: {
                    genre: Array.isArray(suggestion.metadata?.genre)
                        ? suggestion.metadata.genre[0]
                        : suggestion.metadata?.genre,
                    mood: Array.isArray(suggestion.metadata?.mood)
                        ? suggestion.metadata.mood[0]
                        : suggestion.metadata?.mood,
                    language: suggestion.metadata?.language,
                    explicit: suggestion.metadata?.explicit,
                },
            });
            // Atualizar filas
            state.priorityQueue = prioritySuggestions.map(convertToTrack);
            state.normalQueue = normalSuggestions.map(convertToTrack);
            // Combinar para compatibilidade
            state.queue = [...state.priorityQueue, ...state.normalQueue];
            logger_1.logger.info(`Fila atualizada para restaurante ${restaurantId}: ${state.priorityQueue.length} prioritárias, ${state.normalQueue.length} normais`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao atualizar fila do restaurante ${restaurantId}:`, error);
        }
    }
    /**
     * Salva música no histórico de reprodução
     * @private
     */
    async saveToHistory(restaurantId, track) {
        try {
            const restaurant = await this.restaurantRepository.findOne({
                where: { id: restaurantId },
            });
            if (!restaurant) {
                logger_1.logger.warn(`Tentativa de salvar histórico para restaurante inexistente: ${restaurantId}`);
                return;
            }
            const historyEntry = this.playHistoryRepository.create({
                restaurant,
                youtubeVideoId: track.youtubeVideoId,
                title: track.title,
                artist: track.artist,
                duration: track.duration,
                playedAt: new Date(),
                playDuration: 0,
                status: PlayHistory_1.PlayStatus.PLAYING,
                metadata: {
                    source: "playlist",
                    volume: 100,
                },
            });
            await this.playHistoryRepository.save(historyEntry);
            logger_1.logger.debug(`Entrada de histórico criada para "${track.title}" no restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao salvar no histórico para restaurante ${restaurantId}:`, error);
        }
    }
    /**
     * Marca música como completada no histórico
     * @private
     */
    async markAsCompleted(restaurantId, track) {
        try {
            await this.playHistoryRepository
                .createQueryBuilder()
                .update(PlayHistory_1.PlayHistory)
                .set({
                status: PlayHistory_1.PlayStatus.COMPLETED,
                playDuration: track.duration,
            })
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("youtubeVideoId = :videoId", {
                videoId: track.youtubeVideoId,
            })
                .andWhere("status = :status", { status: "playing" })
                .orderBy("playedAt", "DESC")
                .limit(1)
                .execute();
            logger_1.logger.debug(`Faixa "${track.title}" marcada como completada para restaurante ${restaurantId}`);
            // Também refletir na Suggestion (COMPLETED/completedAt)
            try {
                await this.suggestionRepository
                    .createQueryBuilder()
                    .update(Suggestion_1.Suggestion)
                    .set({ status: Suggestion_1.SuggestionStatus.COMPLETED, completedAt: () => "NOW()" })
                    .where("id = :id", { id: track.id })
                    .execute();
            }
            catch (e) {
                logger_1.logger.warn("Falha ao atualizar Suggestion como COMPLETED:", e);
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro ao marcar faixa como completada para restaurante ${restaurantId}:`, error);
        }
    }
    /**
     * Marca música como pulada no histórico
     * @private
     */
    async markAsSkipped(restaurantId, track) {
        try {
            const state = this.playbackStates.get(restaurantId);
            await this.playHistoryRepository
                .createQueryBuilder()
                .update(PlayHistory_1.PlayHistory)
                .set({
                status: PlayHistory_1.PlayStatus.SKIPPED,
                playDuration: state?.currentTime || 0,
                skipReason: "Manual skip by admin",
            })
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("youtubeVideoId = :videoId", {
                videoId: track.youtubeVideoId,
            })
                .andWhere("status = :status", { status: "playing" })
                .orderBy("playedAt", "DESC")
                .limit(1)
                .execute();
            logger_1.logger.debug(`Faixa "${track.title}" marcada como pulada para restaurante ${restaurantId}`);
            // Refletir na Suggestion (SKIPPED)
            try {
                await this.suggestionRepository
                    .createQueryBuilder()
                    .update(Suggestion_1.Suggestion)
                    .set({ status: Suggestion_1.SuggestionStatus.SKIPPED })
                    .where("id = :id", { id: track.id })
                    .execute();
            }
            catch (e) {
                logger_1.logger.warn("Falha ao atualizar Suggestion como SKIPPED:", e);
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro ao marcar faixa como pulada para restaurante ${restaurantId}:`, error);
        }
    }
    /**
     * Ajusta o volume de reprodução
     * @param restaurantId ID do restaurante
     * @param volume Nível de volume (0-100)
     */
    async setVolume(restaurantId, volume) {
        try {
            await this.validateInput(VolumeControlDto, { restaurantId, volume });
            const state = this.playbackStates.get(restaurantId);
            if (!state) {
                await this.initializePlayback(restaurantId);
            }
            const safeVolume = Math.max(0, Math.min(100, volume));
            const updatedState = this.playbackStates.get(restaurantId);
            updatedState.volume = safeVolume;
            updatedState.lastActivity = new Date();
            await this.wsService.broadcastToRestaurant(restaurantId, "volume_changed", {
                volume: safeVolume,
            });
            logger_1.logger.info(`Volume ajustado para ${safeVolume} no restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao ajustar volume para restaurante ${restaurantId}:`, error);
            if (error instanceof PlaybackError) {
                throw error;
            }
            throw new PlaybackError(`Falha ao ajustar volume: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "VOLUME_CONTROL_FAILED", 500, true, restaurantId);
        }
    }
    /**
     * Adiciona música à fila
     * @param restaurantId ID do restaurante
     * @param track Faixa a ser adicionada
     * @param isPriority Se deve ser adicionada à fila prioritária
     */
    async addToQueue(restaurantId, track, isPriority = false) {
        try {
            await this.mutex.acquire(`queue-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state) {
                await this.initializePlayback(restaurantId);
            }
            const updatedState = this.playbackStates.get(restaurantId);
            if (isPriority) {
                updatedState.priorityQueue.push(track);
            }
            else {
                updatedState.normalQueue.push(track);
            }
            // Atualizar fila combinada
            updatedState.queue = [
                ...updatedState.priorityQueue,
                ...updatedState.normalQueue,
            ];
            updatedState.lastActivity = new Date();
            await this.wsService.broadcastToRestaurant(restaurantId, "queue_updated", {
                queue: updatedState.queue,
                added: track,
                message: `"${track.title}" adicionada à fila${isPriority ? " prioritária" : ""}`,
            });
            logger_1.logger.info(`"${track.title}" adicionada à fila${isPriority ? " prioritária" : ""} do restaurante ${restaurantId}`);
            // Iniciar reprodução se nada estiver tocando
            if (!updatedState.isPlaying && !updatedState.currentTrack) {
                setTimeout(() => {
                    if (this.isShuttingDown)
                        return;
                    this.startNextTrack(restaurantId);
                }, 1000);
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro ao adicionar faixa à fila do restaurante ${restaurantId}:`, error);
            throw new PlaybackError(`Falha ao adicionar à fila: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "QUEUE_ADD_FAILED", 500, true, restaurantId);
        }
        finally {
            this.mutex.release(`queue-${restaurantId}`);
        }
    }
    /**
     * Remove música da fila
     * @param restaurantId ID do restaurante
     * @param trackId ID da faixa a ser removida
     */
    async removeFromQueue(restaurantId, trackId) {
        try {
            await this.mutex.acquire(`queue-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state) {
                logger_1.logger.warn(`Tentativa de remover da fila em restaurante sem estado: ${restaurantId}`);
                return;
            }
            // Buscar detalhes da faixa antes de remover (para logging)
            const trackInPriority = state.priorityQueue.find((t) => t.id === trackId);
            const trackInNormal = state.normalQueue.find((t) => t.id === trackId);
            const track = trackInPriority || trackInNormal;
            // Remover da fila apropriada
            if (trackInPriority) {
                state.priorityQueue = state.priorityQueue.filter((t) => t.id !== trackId);
            }
            if (trackInNormal) {
                state.normalQueue = state.normalQueue.filter((t) => t.id !== trackId);
            }
            // Atualizar fila combinada
            state.queue = [...state.priorityQueue, ...state.normalQueue];
            state.lastActivity = new Date();
            // Notificar clientes
            await this.wsService.broadcastToRestaurant(restaurantId, "queue_updated", {
                queue: state.queue,
                removed: trackId,
                message: track
                    ? `"${track.title}" removida da fila`
                    : `Faixa removida da fila`,
            });
            logger_1.logger.info(`Faixa ${track ? `"${track.title}"` : trackId} removida da fila do restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao remover faixa da fila do restaurante ${restaurantId}:`, error);
            throw new PlaybackError(`Falha ao remover da fila: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "QUEUE_REMOVE_FAILED", 500, true, restaurantId, trackId);
        }
        finally {
            this.mutex.release(`queue-${restaurantId}`);
        }
    }
    /**
     * Obtém próxima música da fila
     * @param restaurantId ID do restaurante
     * @returns Próxima faixa ou null se a fila estiver vazia
     */
    async getNextTrack(restaurantId) {
        try {
            if (this.isShuttingDown)
                return null;
            await this.mutex.acquire(`next-track-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state) {
                await this.initializePlayback(restaurantId);
            }
            const updatedState = this.playbackStates.get(restaurantId);
            // Atualizar fila se estiver vazia
            if (updatedState.priorityQueue.length === 0 &&
                updatedState.normalQueue.length === 0) {
                await this.refreshQueue(restaurantId);
            }
            // Filtrar músicas problemáticas
            const availablePriorityTracks = updatedState.priorityQueue.filter((track) => !updatedState.problematicTracks.includes(track.id));
            const availableNormalTracks = updatedState.normalQueue.filter((track) => !updatedState.problematicTracks.includes(track.id));
            // Seleção única baseada no ranking ponderado (pagas e grátis com pesos)
            // 1) Combinar filas disponíveis
            const combinedAvailable = [
                ...availablePriorityTracks,
                ...availableNormalTracks,
            ];
            // 2) Respeitar cooldown autoritativo (10 min) — filtrar músicas em cooldown
            let filteredAvailable = [];
            if (combinedAvailable.length > 0) {
                try {
                    for (const t of combinedAvailable) {
                        const yid = t.youtubeVideoId || "";
                        if (!yid) {
                            filteredAvailable.push(t);
                            continue;
                        }
                        const inCooldown = await CollaborativePlaylistService_1.collaborativePlaylistService.isSongInCooldown(restaurantId, yid);
                        if (!inCooldown)
                            filteredAvailable.push(t);
                    }
                }
                catch (e) {
                    // Em caso de erro no Redis, seguir com a lista original para não travar reprodução
                    filteredAvailable = combinedAvailable;
                }
            }
            if (filteredAvailable.length > 0) {
                try {
                    if (this.isShuttingDown)
                        return filteredAvailable[0];
                    const ranking = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, 200);
                    if (ranking.success && ranking.data?.length) {
                        const orderMap = new Map(ranking.data.map((r, i) => [
                            r.youtubeVideoId,
                            i,
                        ]));
                        filteredAvailable.sort((a, b) => {
                            const av = orderMap.get(a.youtubeVideoId) ?? Number.MAX_SAFE_INTEGER;
                            const bv = orderMap.get(b.youtubeVideoId) ?? Number.MAX_SAFE_INTEGER;
                            return av - bv;
                        });
                    }
                }
                catch { }
                return filteredAvailable[0];
            }
            // Se todas as músicas são problemáticas ou em cooldown, ainda podemos tentar uma não em cooldown em cada fila
            if (updatedState.priorityQueue.length > 0) {
                logger_1.logger.warn(`Todas as músicas prioritárias são problemáticas para restaurante ${restaurantId}`);
                // tentar primeira fora de cooldown
                for (const t of updatedState.priorityQueue) {
                    const yid = t.youtubeVideoId || "";
                    try {
                        if (!yid || !(await CollaborativePlaylistService_1.collaborativePlaylistService.isSongInCooldown(restaurantId, yid))) {
                            return t;
                        }
                    }
                    catch {
                        return t; // em caso de erro, não bloquear
                    }
                }
            }
            if (updatedState.normalQueue.length > 0) {
                logger_1.logger.warn(`Todas as músicas normais são problemáticas para restaurante ${restaurantId}`);
                for (const t of updatedState.normalQueue) {
                    const yid = t.youtubeVideoId || "";
                    try {
                        if (!yid || !(await CollaborativePlaylistService_1.collaborativePlaylistService.isSongInCooldown(restaurantId, yid))) {
                            return t;
                        }
                    }
                    catch {
                        return t;
                    }
                }
            }
            logger_1.logger.info(`Nenhuma faixa disponível na fila do restaurante ${restaurantId}`);
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Erro ao obter próxima faixa para restaurante ${restaurantId}:`, error);
            return null;
        }
        finally {
            this.mutex.release(`next-track-${restaurantId}`);
        }
    }
    /**
     * Obtém estado atual de reprodução
     * @param restaurantId ID do restaurante
     */
    async getPlaybackState(restaurantId) {
        try {
            const state = this.playbackStates.get(restaurantId);
            if (!state) {
                return await this.initializePlayback(restaurantId);
            }
            // Atualizar hora da última atividade
            state.lastActivity = new Date();
            return state;
        }
        catch (error) {
            logger_1.logger.error(`Erro ao obter estado de reprodução para restaurante ${restaurantId}:`, error);
            return null;
        }
    }
    /**
     * Atualiza o tempo atual de reprodução
     * @param restaurantId ID do restaurante
     * @param currentTime Tempo atual em segundos
     */
    async updateCurrentTime(restaurantId, currentTime) {
        try {
            const state = this.playbackStates.get(restaurantId);
            if (!state || !state.currentTrack) {
                return;
            }
            // Validar tempo
            const safeTime = Math.max(0, Math.min(state.currentTrack.duration, currentTime));
            state.currentTime = safeTime;
            state.lastActivity = new Date();
            // Não precisamos notificar os clientes em cada atualização de tempo
        }
        catch (error) {
            logger_1.logger.error(`Erro ao atualizar tempo de reprodução para restaurante ${restaurantId}:`, error);
        }
    }
    /**
     * Obter playlist ativa atual baseada no agendamento
     * @private
     */
    async getCurrentActivePlaylist(restaurantId) {
        try {
            const schedule = await this.playlistScheduleRepository.findOne({
                where: { restaurantId, isActive: true },
                relations: ["slots", "fallbackPlaylist"],
            });
            if (schedule) {
                // Lógica para determinar a playlist atual com base no horário
                const now = new Date();
                const currentDay = now.getDay(); // 0 = Domingo, 1 = Segunda, etc.
                const currentHour = now.getHours();
                const currentMinute = now.getMinutes();
                // Verificar slots ativos
                const activeSlot = schedule.timeSlots?.find((slot) => {
                    const currentTime = `${currentHour
                        .toString()
                        .padStart(2, "0")}:${currentMinute.toString().padStart(2, "0")}`;
                    return (slot.days.includes(currentDay) &&
                        slot.isActive &&
                        currentTime >= slot.startTime &&
                        currentTime <= slot.endTime);
                });
                if (activeSlot) {
                    const playlist = await this.playlistRepository.findOne({
                        where: { id: activeSlot.playlistId },
                    });
                    if (playlist) {
                        return {
                            id: playlist.id,
                            name: playlist.name,
                            type: "scheduled",
                            description: playlist.description,
                        };
                    }
                }
                // Fallback playlist não está implementado no modelo atual
            }
            // Buscar playlist ativa do restaurante (priorizar padrão)
            const defaultPlaylist = await this.playlistRepository.findOne({
                where: {
                    restaurant: { id: restaurantId },
                    status: Playlist_1.PlaylistStatus.ACTIVE,
                },
                order: {
                    isDefault: "DESC",
                    createdAt: "DESC", // Mais recente primeiro
                },
            });
            if (defaultPlaylist) {
                return {
                    id: defaultPlaylist.id,
                    name: defaultPlaylist.name,
                    type: "default",
                    description: defaultPlaylist.description,
                };
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Erro ao obter playlist ativa para ${restaurantId}:`, error);
            return null;
        }
    }
    /**
     * Obter próxima playlist agendada
     * @private
     */
    async getNextScheduledPlaylist(restaurantId) {
        try {
            const schedule = await this.playlistScheduleRepository.findOne({
                where: { restaurantId, isActive: true },
                relations: ["slots"],
            });
            if (schedule && schedule.timeSlots && schedule.timeSlots.length > 0) {
                const now = new Date();
                const currentDay = now.getDay();
                const currentHour = now.getHours();
                const currentMinute = now.getMinutes();
                const currentTotalMinutes = currentHour * 60 + currentMinute;
                // Encontrar o próximo slot programado
                let nextSlot = null;
                let minWaitTime = Number.MAX_SAFE_INTEGER;
                for (const slot of schedule.timeSlots) {
                    // Parse startTime (HH:MM) to minutes
                    const [startHour, startMinute] = slot.startTime
                        .split(":")
                        .map(Number);
                    const slotStartTime = startHour * 60 + startMinute;
                    let waitTime = 0;
                    // Check if slot is active on any of its days
                    for (const dayOfWeek of slot.days) {
                        if (dayOfWeek === currentDay &&
                            slotStartTime > currentTotalMinutes) {
                            // Mesmo dia, mais tarde
                            waitTime = slotStartTime - currentTotalMinutes;
                        }
                        else if (dayOfWeek > currentDay) {
                            // Dias posteriores nesta semana
                            waitTime =
                                (dayOfWeek - currentDay) * 24 * 60 +
                                    slotStartTime -
                                    currentTotalMinutes;
                        }
                        else if (dayOfWeek < currentDay) {
                            // Dias da próxima semana
                            waitTime =
                                (7 - currentDay + dayOfWeek) * 24 * 60 +
                                    slotStartTime -
                                    currentTotalMinutes;
                        }
                        if (waitTime > 0 && waitTime < minWaitTime) {
                            minWaitTime = waitTime;
                            nextSlot = slot;
                            break; // Found a better slot, no need to check other days for this slot
                        }
                    }
                }
                if (nextSlot) {
                    const playlist = await this.playlistRepository.findOne({
                        where: { id: nextSlot.playlistId },
                    });
                    if (playlist) {
                        const startsAt = new Date();
                        startsAt.setMinutes(startsAt.getMinutes() + minWaitTime);
                        return {
                            id: playlist.id,
                            name: playlist.name,
                            startsIn: minWaitTime,
                            startsAt,
                            priority: nextSlot.priority || 1,
                        };
                    }
                }
            }
            return null;
        }
        catch (error) {
            logger_1.logger.error(`Erro ao obter próxima playlist para ${restaurantId}:`, error);
            return null;
        }
    }
    /**
     * Carregar músicas problemáticas
     * @private
     */
    async loadProblematicTracks(restaurantId) {
        try {
            const state = this.playbackStates.get(restaurantId);
            if (!state)
                return;
            const worstTracks = await this.analyticsService.getWorstRatedTracks(restaurantId);
            state.problematicTracks = worstTracks.map((track) => track.id);
            logger_1.logger.info(`${state.problematicTracks.length} músicas problemáticas identificadas para restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao carregar músicas problemáticas para ${restaurantId}:`, error);
        }
    }
    /**
     * Verificar agendamentos de playlist
     * @private
     */
    async checkScheduledPlaylists() {
        for (const [restaurantId, state] of Array.from(this.playbackStates.entries())) {
            try {
                const nextScheduled = await this.getNextScheduledPlaylist(restaurantId);
                // Atualizar estado
                state.nextScheduledPlaylist = nextScheduled;
                if (nextScheduled && nextScheduled.startsIn <= 1) {
                    // 1 minuto ou menos
                    await this.handlePlaylistTransition(restaurantId, nextScheduled.id);
                }
            }
            catch (error) {
                logger_1.logger.error(`Erro ao verificar agendamentos para ${restaurantId}:`, error);
            }
        }
    }
    /**
     * Lidar com transição de playlist
     * @private
     */
    async handlePlaylistTransition(restaurantId, newPlaylistId) {
        try {
            await this.mutex.acquire(`transition-${restaurantId}`);
            const state = this.playbackStates.get(restaurantId);
            if (!state)
                return;
            logger_1.logger.info(`Transição de playlist iniciada para restaurante ${restaurantId}`);
            state.isTransitioning = true;
            // Se há música tocando, aguardar terminar
            if (state.isPlaying && state.currentTrack) {
                logger_1.logger.info(`Aguardando música atual terminar para transição em ${restaurantId}`);
                // Notificar sobre transição pendente
                await this.wsService.broadcastToRestaurant(restaurantId, "playlist_transition_pending", {
                    currentTrack: state.currentTrack,
                    newPlaylistId,
                    message: "Transição de playlist será feita após a música atual",
                });
            }
            else {
                // Fazer transição imediatamente se não há música tocando
                await this.executePlaylistTransition(restaurantId, newPlaylistId);
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro na transição de playlist para ${restaurantId}:`, error);
        }
        finally {
            this.mutex.release(`transition-${restaurantId}`);
        }
    }
    /**
     * Executar transição de playlist
     * @private
     */
    async executePlaylistTransition(restaurantId, newPlaylistId) {
        try {
            const state = this.playbackStates.get(restaurantId);
            if (!state)
                return;
            // Atualizar playlist atual
            const newPlaylist = await this.playlistRepository.findOne({
                where: { id: newPlaylistId },
            });
            if (newPlaylist) {
                state.currentPlaylist = {
                    id: newPlaylist.id,
                    name: newPlaylist.name,
                    type: "scheduled",
                    description: newPlaylist.description,
                };
                // Recarregar fila com nova playlist
                await this.refreshQueue(restaurantId);
                logger_1.logger.info(`Transição concluída para playlist "${newPlaylist.name}" em ${restaurantId}`);
                // Notificar sobre transição concluída
                await this.wsService.broadcastToRestaurant(restaurantId, "playlist_transition_completed", {
                    newPlaylist: state.currentPlaylist,
                    queueSize: state.queue.length,
                    message: `Transição para "${newPlaylist.name}" concluída`,
                });
                // Iniciar próxima música se não há nada tocando
                if (!state.isPlaying && !state.currentTrack) {
                    setTimeout(() => {
                        this.startNextTrack(restaurantId);
                    }, 2000); // 2 segundos de pausa para transição suave
                }
            }
        }
        catch (error) {
            logger_1.logger.error(`Erro na execução da transição de playlist para ${restaurantId}:`, error);
        }
        finally {
            const state = this.playbackStates.get(restaurantId);
            if (state) {
                state.isTransitioning = false;
            }
        }
    }
    /**
     * Obter relatório de músicas problemáticas
     * @param restaurantId ID do restaurante
     */
    async getProblematicTracksReport(restaurantId) {
        try {
            const worstTracks = await this.analyticsService.getWorstRatedTracks(restaurantId);
            const tracksToRemove = await this.analyticsService.getTracksToRemove(restaurantId);
            const recommendations = [];
            if (worstTracks.length > 0) {
                recommendations.push(`${worstTracks.length} músicas com alta taxa de rejeição identificadas`);
            }
            if (tracksToRemove.length > 0) {
                recommendations.push(`${tracksToRemove.length} músicas recomendadas para remoção`);
            }
            if (worstTracks.length === 0) {
                recommendations.push("Playlist está saudável - nenhuma música problemática detectada");
            }
            const healthScore = Math.max(0, 100 - worstTracks.length * 10);
            logger_1.logger.info(`Relatório de saúde gerado para restaurante ${restaurantId}: score ${healthScore}`);
            return {
                problematicTracks: worstTracks,
                recommendations,
                healthScore,
            };
        }
        catch (error) {
            logger_1.logger.error(`Erro ao gerar relatório para ${restaurantId}:`, error);
            return {
                problematicTracks: [],
                recommendations: ["Erro ao analisar playlist"],
                healthScore: 50,
            };
        }
    }
    /**
     * Limpar recursos quando restaurante desconecta
     * @param restaurantId ID do restaurante
     */
    cleanup(restaurantId) {
        const playbackTimer = this.playbackTimers.get(restaurantId);
        if (playbackTimer) {
            clearTimeout(playbackTimer);
            this.playbackTimers.delete(restaurantId);
        }
        const scheduleTimer = this.scheduleTimers.get(restaurantId);
        if (scheduleTimer) {
            clearTimeout(scheduleTimer);
            this.scheduleTimers.delete(restaurantId);
        }
        this.playbackStates.delete(restaurantId);
        logger_1.logger.info(`Recursos de reprodução limpos para restaurante ${restaurantId}`);
    }
    /**
     * Realizar ação de reprodução
     * @param actionData Dados da ação
     */
    async performAction(actionData) {
        try {
            await this.validateInput(PlaybackActionDto, actionData);
            const { restaurantId, action, trackId } = actionData;
            switch (action) {
                case PlaybackAction.PLAY:
                    if (trackId) {
                        await this.playTrack(restaurantId, trackId);
                    }
                    else {
                        const state = await this.getPlaybackState(restaurantId);
                        if (state?.currentTrack && !state.isPlaying) {
                            await this.resumePlayback(restaurantId);
                        }
                        else {
                            await this.startNextTrack(restaurantId);
                        }
                    }
                    break;
                case PlaybackAction.PAUSE:
                    await this.pausePlayback(restaurantId);
                    break;
                case PlaybackAction.SKIP:
                    await this.skipToNext(restaurantId);
                    break;
                case PlaybackAction.NEXT:
                    await this.skipToNext(restaurantId);
                    break;
                case PlaybackAction.STOP:
                    const state = this.playbackStates.get(restaurantId);
                    if (state && state.currentTrack) {
                        await this.skipToNext(restaurantId);
                        await this.pausePlayback(restaurantId);
                    }
                    break;
                default:
                    throw new PlaybackError(`Ação de reprodução desconhecida: ${action}`, "UNKNOWN_ACTION", 400, true, restaurantId);
            }
            logger_1.logger.info(`Ação ${action} executada para restaurante ${restaurantId}`);
        }
        catch (error) {
            logger_1.logger.error(`Erro ao executar ação de reprodução:`, error);
            if (error instanceof PlaybackError) {
                throw error;
            }
            throw new PlaybackError(`Falha ao executar ação: ${error instanceof Error ? error.message : "Erro desconhecido"}`, "ACTION_FAILED", 500);
        }
    }
    /**
     * Encerra o serviço limpando timers e intervalos. Use em teardown de testes.
     */
    shutdown() {
        this.isShuttingDown = true;
        if (this.scheduleInterval) {
            clearInterval(this.scheduleInterval);
            this.scheduleInterval = undefined;
        }
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = undefined;
        }
        for (const [, t] of this.playbackTimers)
            clearTimeout(t);
        this.playbackTimers.clear();
        for (const [, t] of this.scheduleTimers)
            clearTimeout(t);
        this.scheduleTimers.clear();
    }
}
exports.PlaybackService = PlaybackService;
// Exportar instância singleton
exports.playbackService = PlaybackService.getInstance();
exports.default = exports.playbackService;
//# sourceMappingURL=PlaybackService.js.map