"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.playlistReorderService = exports.PlaylistReorderService = void 0;
const database_1 = require("../config/database");
const Restaurant_1 = require("../models/Restaurant");
const Playlist_1 = require("../models/Playlist");
const Suggestion_1 = require("../models/Suggestion");
const YouTubeOAuthService_1 = require("./YouTubeOAuthService");
const CollaborativePlaylistService_1 = require("./CollaborativePlaylistService");
const logger_1 = require("../utils/logger");
const typeorm_1 = require("typeorm");
const WebSocketService_1 = require("./WebSocketService");
const redis_1 = require("../config/redis");
const PlaylistSchedule_1 = require("../models/PlaylistSchedule");
class PlaylistReorderService {
    // Janela de "graça" para última leva de votos antes de aplicar a reordenação (em segundos)
    static { this.GRACE_WINDOW_SECONDS = 15; }
    constructor() {
        this.restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        this.playlistRepository = database_1.AppDataSource.getRepository(Playlist_1.Playlist);
        this.suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        this.scheduleRepository = database_1.AppDataSource.getRepository(PlaylistSchedule_1.PlaylistSchedule);
        this.reorderInterval = null;
        this.isRunning = false;
        // WebSocketService será inicializado quando necessário
        logger_1.logger.info("🔄 PlaylistReorderService inicializado");
    }
    static getInstance() {
        if (!PlaylistReorderService.instance) {
            PlaylistReorderService.instance = new PlaylistReorderService();
        }
        return PlaylistReorderService.instance;
    }
    /**
     * 🚀 INICIAR REORDENAÇÃO AUTOMÁTICA
     * Executa a cada 5 minutos (300.000ms)
     */
    startAutoReorder() {
        if (this.isRunning) {
            logger_1.logger.warn("⚠️ Reordenação automática já está em execução");
            return;
        }
        this.isRunning = true;
        logger_1.logger.info("🚀 Iniciando reordenação automática a cada 5 minutos");
        // Executar imediatamente na inicialização
        this.executeReorderCycle();
        // Agendar execução a cada 5 minutos
        this.reorderInterval = setInterval(() => {
            this.executeReorderCycle();
        }, 5 * 60 * 1000); // 5 minutos
    }
    /**
     * 🛑 PARAR REORDENAÇÃO AUTOMÁTICA
     */
    stopAutoReorder() {
        if (this.reorderInterval) {
            clearInterval(this.reorderInterval);
            this.reorderInterval = null;
        }
        this.isRunning = false;
        logger_1.logger.info("🛑 Reordenação automática parada");
    }
    /**
     * 🔄 EXECUTAR CICLO DE REORDENAÇÃO
     * Processa todos os restaurantes ativos
     */
    async executeReorderCycle() {
        const startTime = new Date();
        logger_1.logger.info("🔄 Iniciando ciclo de reordenação automática");
        try {
            // Buscar restaurantes ativos com playlists
            const restaurants = await this.restaurantRepository.find({
                where: { isActive: true },
                relations: ["playlists"],
            });
            const results = [];
            for (const restaurant of restaurants) {
                try {
                    const result = await this.reorderRestaurantPlaylists(restaurant.id);
                    results.push(result);
                }
                catch (error) {
                    logger_1.logger.error(`❌ Erro ao reordenar playlists do restaurante ${restaurant.id}:`, error);
                    results.push({
                        success: false,
                        restaurantId: restaurant.id,
                        tracksReordered: 0,
                        message: `Erro: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
                        timestamp: new Date(),
                    });
                }
            }
            // Log do resultado do ciclo
            const successful = results.filter((r) => r.success && r.status !== "skipped").length;
            const skipped = results.filter((r) => r.status === "skipped").length;
            const failed = results.filter((r) => !r.success).length;
            const totalTracks = results.reduce((sum, r) => sum + r.tracksReordered, 0);
            const duration = Date.now() - startTime.getTime();
            logger_1.logger.info(`✅ Ciclo de reordenação concluído em ${duration}ms:`);
            logger_1.logger.info(`   📊 ${successful} sucessos, ${skipped} ignorados, ${failed} falhas`);
            logger_1.logger.info(`   🎵 ${totalTracks} faixas reordenadas no total`);
        }
        catch (error) {
            logger_1.logger.error("❌ Erro no ciclo de reordenação:", error);
        }
    }
    /**
     * 🏪 REORDENAR PLAYLISTS DE UM RESTAURANTE
     */
    async reorderRestaurantPlaylists(restaurantId) {
        try {
            // Verificar se restaurante está autenticado com YouTube
            const isAuthenticated = await YouTubeOAuthService_1.youtubeOAuthService.isAuthenticated(restaurantId);
            // 1) Verificar agendamento e ativar playlist do slot atual (se houver)
            const schedules = await this.scheduleRepository.find({
                where: { restaurantId, isActive: true },
            });
            let activePlaylist = await this.playlistRepository.findOne({
                where: {
                    restaurant: { id: restaurantId },
                    status: Playlist_1.PlaylistStatus.ACTIVE,
                    youtubePlaylistId: (0, typeorm_1.Not)((0, typeorm_1.IsNull)()),
                },
                order: { isDefault: "DESC", createdAt: "DESC" },
            });
            if (schedules.length) {
                for (const schedule of schedules) {
                    const current = schedule.getCurrentActivePlaylist();
                    if (current?.playlistId) {
                        // Se a playlist agendada é diferente da ACTIVE atual, trocá-la
                        if (!activePlaylist || activePlaylist.id !== current.playlistId) {
                            const scheduledPlaylist = await this.playlistRepository.findOne({
                                where: {
                                    id: current.playlistId,
                                    restaurant: { id: restaurantId },
                                },
                            });
                            if (scheduledPlaylist) {
                                // Desativar outras ACTIVE e ativar a agendada
                                await this.playlistRepository.update({ restaurant: { id: restaurantId } }, { status: Playlist_1.PlaylistStatus.INACTIVE });
                                scheduledPlaylist.status = Playlist_1.PlaylistStatus.ACTIVE;
                                await this.playlistRepository.save(scheduledPlaylist);
                                activePlaylist = scheduledPlaylist;
                                logger_1.logger.info(`⏰ Playlist agendada ativada: ${scheduledPlaylist.name} (${scheduledPlaylist.id})`);
                            }
                        }
                        break; // Um schedule ativo por vez
                    }
                }
            }
            // Buscar playlist ativa principal (após considerar agendamento)
            if (!activePlaylist || !activePlaylist.youtubePlaylistId) {
                return {
                    success: true,
                    status: "skipped",
                    restaurantId,
                    tracksReordered: 0,
                    message: "Ignorado: nenhum playlist ativa com youtubePlaylistId definida",
                    timestamp: new Date(),
                };
            }
            // Cada restaurante pode ter sua própria playlist ativa; nenhuma restrição especial para demo
            // 🔁 Antes de reordenar: se houver snapshot de ordem original e ranking atual vier vazio,
            // tentar reverter para a ordem original (remove efeito do reordenamento anterior)
            try {
                const client = redis_1.redisClient.getClient();
                const originalKey = `playlist:originalOrder:${restaurantId}:${activePlaylist.id}`;
                const originalJson = await client.get(originalKey);
                if (originalJson) {
                    const rankingProbe = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, 5);
                    const hasActiveRanking = !!(rankingProbe.success && rankingProbe.data && rankingProbe.data.length);
                    if (!hasActiveRanking) {
                        const originalOrder = JSON.parse(originalJson);
                        const revertOk = await YouTubeOAuthService_1.youtubeOAuthService.reorderPlaylist(restaurantId, activePlaylist.youtubePlaylistId, originalOrder);
                        if (revertOk) {
                            // Atualizar ordem local conforme snapshot
                            if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
                                const posMap = new Map(originalOrder.map((o) => [o.videoId, o.position]));
                                activePlaylist.tracks = activePlaylist.tracks
                                    .map((track) => ({
                                    ...track,
                                    position: posMap.has(track.youtubeVideoId)
                                        ? posMap.get(track.youtubeVideoId)
                                        : track.position,
                                }))
                                    .sort((a, b) => a.position - b.position);
                                await this.playlistRepository.save(activePlaylist);
                            }
                            await client.del(originalKey);
                            await this.notifyPlaylistReorder(restaurantId, {
                                playlistId: activePlaylist.id,
                                playlistName: activePlaylist.name,
                                tracksReordered: originalOrder.length,
                                topTracks: [],
                            });
                            return {
                                success: true,
                                status: "success",
                                restaurantId,
                                playlistId: activePlaylist.id,
                                tracksReordered: originalOrder.length,
                                message: "Revertido para a ordem original por ausência de ranking ativo",
                                timestamp: new Date(),
                            };
                        }
                    }
                }
            }
            catch (e) {
                logger_1.logger.warn("Falha ao tentar reverter para ordem original (não bloqueante)", e);
            }
            // Buscar ranking de votos (primeira leitura)
            let rankingResult = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, 50);
            if (!rankingResult.success ||
                !rankingResult.data ||
                rankingResult.data.length === 0) {
                return {
                    success: true,
                    status: "skipped",
                    restaurantId,
                    playlistId: activePlaylist.id,
                    tracksReordered: 0,
                    message: "Ignorado: nenhuma música com votos para reordenar",
                    timestamp: new Date(),
                };
            }
            // Preparar nova ordem baseada no ranking, restrita às faixas da playlist
            const allowed = new Set((activePlaylist.tracks ?? []).map((t) => t.youtubeVideoId));
            // Respeitar cooldown ao reordenar: remove itens em cooldown
            const withAllowed = rankingResult.data.filter((it) => allowed.has(it.youtubeVideoId));
            let filtered = withAllowed;
            try {
                const { collaborativePlaylistService } = await Promise.resolve().then(() => __importStar(require('./CollaborativePlaylistService')));
                const tmp = [];
                for (const it of withAllowed) {
                    const inCd = await collaborativePlaylistService.isSongInCooldown(restaurantId, it.youtubeVideoId);
                    if (!inCd)
                        tmp.push(it);
                }
                filtered = tmp;
            }
            catch { }
            const newOrder = filtered.map((item, index) => ({
                videoId: item.youtubeVideoId,
                position: index,
                title: item.title,
                voteCount: item.voteCount,
                isPaid: item.isPaid,
                paymentAmount: item.paymentAmount,
            }));
            if (newOrder.length === 0) {
                return {
                    success: true,
                    status: "skipped",
                    restaurantId,
                    playlistId: activePlaylist.id,
                    tracksReordered: 0,
                    message: "Ignorado: nenhuma música elegível (todas fora da playlist)",
                    timestamp: new Date(),
                };
            }
            // Anunciar janela de graça de 15s com snapshot do candidato a próximo
            try {
                const wsService = WebSocketService_1.WebSocketService.getInstance();
                const nextCandidate = filtered && filtered.length ? filtered[0] : null;
                const graceSeconds = PlaylistReorderService.GRACE_WINDOW_SECONDS;
                const deadlineAt = new Date(Date.now() + graceSeconds * 1000);
                await wsService.emitToRestaurant(restaurantId, "ranking-snapshot", {
                    type: "ranking-snapshot",
                    restaurantId,
                    graceSeconds,
                    deadlineAt: deadlineAt.toISOString(),
                    nextTrack: nextCandidate
                        ? {
                            youtubeVideoId: nextCandidate.youtubeVideoId,
                            title: nextCandidate.title,
                            artist: nextCandidate.artist,
                            isPaid: nextCandidate.isPaid,
                            paymentAmount: nextCandidate.paymentAmount,
                            voteCount: nextCandidate.voteCount,
                        }
                        : null,
                });
            }
            catch (wsErr) {
                logger_1.logger.warn("Falha ao emitir ranking-snapshot (não bloqueante)", wsErr);
            }
            // Aguardar a janela de graça e recomputar o ranking para capturar últimos votos
            await new Promise((resolve) => setTimeout(resolve, PlaylistReorderService.GRACE_WINDOW_SECONDS * 1000));
            rankingResult = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, 50);
            const allowedAfter = new Set((activePlaylist.tracks ?? []).map((t) => t.youtubeVideoId));
            const withAllowedAfter = (rankingResult.data || []).filter((it) => allowedAfter.has(it.youtubeVideoId));
            let filteredAfter = withAllowedAfter;
            try {
                const { collaborativePlaylistService } = await Promise.resolve().then(() => __importStar(require('./CollaborativePlaylistService')));
                const tmpAfter = [];
                for (const item of withAllowedAfter) {
                    try {
                        const cd = await collaborativePlaylistService.isSongInCooldown(restaurantId, item.youtubeVideoId);
                        if (!cd)
                            tmpAfter.push(item);
                    }
                    catch {
                        tmpAfter.push(item);
                    }
                }
                filteredAfter = tmpAfter;
            }
            catch { }
            // Substituir lista filtrada pela versão pós-janela
            filtered = filteredAfter;
            // Antes de reordenar: armazenar snapshot da ordem original (para possível reversão)
            try {
                if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
                    const originalOrder = activePlaylist.tracks
                        .map((t) => ({ videoId: t.youtubeVideoId, position: t.position }))
                        .sort((a, b) => a.position - b.position);
                    const client = redis_1.redisClient.getClient();
                    const originalKey = `playlist:originalOrder:${restaurantId}:${activePlaylist.id}`;
                    // Manter por até 1 hora; será removido ao reverter com sucesso
                    await client.setEx(originalKey, 3600, JSON.stringify(originalOrder));
                }
            }
            catch (e) {
                logger_1.logger.warn("Não foi possível salvar snapshot da ordem original", e);
            }
            let reorderedOnYouTube = false;
            if (isAuthenticated) {
                // Reordenar playlist no YouTube
                const reorderSuccess = await YouTubeOAuthService_1.youtubeOAuthService.reorderPlaylist(restaurantId, activePlaylist.youtubePlaylistId, newOrder);
                if (!reorderSuccess) {
                    return {
                        success: false,
                        status: "failed",
                        restaurantId,
                        playlistId: activePlaylist.id,
                        tracksReordered: 0,
                        message: "Falha ao reordenar playlist no YouTube",
                        timestamp: new Date(),
                    };
                }
                reorderedOnYouTube = true;
            }
            else {
                // Fallback local: sem OAuth do YouTube, ainda aplicamos ordem no DB e emitimos eventos
                // para que o frontend obedeça a seleção a cada 5 minutos.
                logger_1.logger.info("⚠️ Reordenação local (sem YouTube OAuth): aplicando nova ordem no DB/WebSocket");
            }
            // Atualizar ordem local na base de dados
            if (activePlaylist.tracks && Array.isArray(activePlaylist.tracks)) {
                activePlaylist.tracks = activePlaylist.tracks
                    .map((track) => {
                    const newOrderItem = newOrder.find((item) => item.videoId === track.youtubeVideoId);
                    return {
                        ...track,
                        position: newOrderItem ? newOrderItem.position : track.position,
                    };
                })
                    .sort((a, b) => a.position - b.position);
                await this.playlistRepository.save(activePlaylist);
            }
            logger_1.logger.info(`🎵 Playlist reordenada${reorderedOnYouTube ? " (YouTube)" : " (local)"}: ${activePlaylist.name} (${newOrder.length} faixas)`);
            // ✅ Marcar snapshot do ranking como CONSUMIDO por um período (evita reaplicação/loop)
            try {
                const itemsForSignature = filtered.map((item) => ({
                    youtubeVideoId: item.youtubeVideoId,
                    voteCount: item.voteCount,
                    paymentAmount: item.paymentAmount,
                    isPaid: item.isPaid,
                }));
                await CollaborativePlaylistService_1.collaborativePlaylistService.markRankingAsConsumed(restaurantId, itemsForSignature, 5 * 60 // 5 minutos
                );
            }
            catch (e) {
                logger_1.logger.warn("Falha ao marcar ranking como consumido (não bloqueante)", e);
            }
            // 🔔 Notificar reordenação em tempo real
            await this.notifyPlaylistReorder(restaurantId, {
                playlistId: activePlaylist.id,
                playlistName: activePlaylist.name,
                tracksReordered: newOrder.length,
                topTracks: newOrder.slice(0, 5), // Top 5 músicas
            });
            return {
                success: true,
                status: "success",
                restaurantId,
                playlistId: activePlaylist.id,
                tracksReordered: newOrder.length,
                message: `Playlist reordenada com sucesso (${newOrder.length} faixas)`,
                timestamp: new Date(),
            };
        }
        catch (error) {
            logger_1.logger.error(`❌ Erro ao reordenar playlists do restaurante ${restaurantId}:`, error);
            return {
                success: false,
                status: "failed",
                restaurantId,
                tracksReordered: 0,
                message: `Erro: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
                timestamp: new Date(),
            };
        }
    }
    /**
     * 📊 OBTER STATUS DO SERVIÇO
     */
    getStatus() {
        const nextExecution = this.isRunning && this.reorderInterval
            ? new Date(Date.now() + 5 * 60 * 1000)
            : undefined;
        return {
            isRunning: this.isRunning,
            nextExecution,
            uptime: process.uptime(),
        };
    }
    /**
     * 🔔 NOTIFICAR REORDENAÇÃO DE PLAYLIST
     */
    async notifyPlaylistReorder(restaurantId, reorderData) {
        try {
            const notificationData = {
                type: "playlist_reordered",
                playlist: {
                    id: reorderData.playlistId,
                    name: reorderData.playlistName,
                    tracksReordered: reorderData.tracksReordered,
                },
                topTracks: reorderData.topTracks.map((track, index) => ({
                    position: index + 1,
                    title: track.title,
                    artist: track.artist,
                    voteCount: track.voteCount,
                    isPaid: track.isPaid,
                    paymentAmount: track.paymentAmount,
                })),
                timestamp: new Date().toISOString(),
                message: `Playlist reordenada automaticamente - ${reorderData.tracksReordered} músicas`,
            };
            // Notificar todos os clientes do restaurante (se WebSocket disponível)
            try {
                const wsService = WebSocketService_1.WebSocketService.getInstance();
                if (wsService) {
                    await wsService.emitToRestaurant(restaurantId, "playlistReordered", notificationData);
                }
            }
            catch (wsError) {
                logger_1.logger.warn("WebSocket não disponível para notificação:", wsError);
            }
            // Notificar administradores com dados detalhados (se WebSocket disponível)
            try {
                const wsService = WebSocketService_1.WebSocketService.getInstance();
                if (wsService) {
                    await wsService.emitToAdmins(restaurantId, "playlistReorderedAdmin", {
                        ...notificationData,
                        adminDetails: {
                            autoReorderEnabled: true,
                            nextReorderTime: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // +5 min
                        },
                    });
                }
            }
            catch (wsError) {
                logger_1.logger.warn("WebSocket não disponível para notificação admin:", wsError);
            }
            logger_1.logger.info(`🔔 Notificação de reordenação enviada: ${reorderData.playlistName}`);
            // Emitir também um evento 'reorderSelected' com o primeiro item da nova ordem
            try {
                const first = reorderData.topTracks?.[0];
                if (first) {
                    const wsService = WebSocketService_1.WebSocketService.getInstance();
                    if (wsService) {
                        await wsService.emitToRestaurant(restaurantId, "reorderSelected", {
                            type: "reorderSelected",
                            video: {
                                youtubeVideoId: first.videoId || first.youtubeVideoId,
                                title: first.title,
                                artist: first.artist,
                                position: 0,
                            },
                            timestamp: new Date().toISOString(),
                        });
                    }
                }
            }
            catch (wsError) {
                logger_1.logger.warn("Falha ao emitir reorderSelected (não bloqueante)", wsError);
            }
        }
        catch (error) {
            logger_1.logger.error("❌ Erro ao notificar reordenação:", error);
            // Não falhar o processo principal por erro de notificação
        }
    }
    /**
     * 🔧 REORDENAR MANUALMENTE (para testes)
     */
    async manualReorder(restaurantId) {
        logger_1.logger.info(`🔧 Reordenação manual solicitada para restaurante ${restaurantId}`);
        return await this.reorderRestaurantPlaylists(restaurantId);
    }
}
exports.PlaylistReorderService = PlaylistReorderService;
// Exportar instância singleton
exports.playlistReorderService = PlaylistReorderService.getInstance();
//# sourceMappingURL=PlaylistReorderService.js.map