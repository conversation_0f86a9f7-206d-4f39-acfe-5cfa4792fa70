{"version": 3, "file": "PlaybackService.d.ts", "sourceRoot": "", "sources": ["../../src/services/PlaybackService.ts"], "names": [], "mappings": "AA0BA;;GAEG;AACH,oBAAY,sBAAsB;IAChC,MAAM,WAAW;IACjB,SAAS,cAAc;IACvB,SAAS,cAAc;CACxB;AAED,oBAAY,cAAc;IACxB,IAAI,SAAS;IACb,KAAK,UAAU;IACf,IAAI,SAAS;IACb,IAAI,SAAS;IACb,IAAI,SAAS;CACd;AAED;;GAEG;AACH,MAAM,WAAW,MAAM;IACrB,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,cAAc,EAAE,MAAM,CAAC;IACvB,QAAQ,EAAE,MAAM,CAAC;IACjB,YAAY,EAAE,MAAM,CAAC;IACrB,OAAO,EAAE,MAAM,CAAC;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,CAAC,EAAE;QACT,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,IAAI,CAAC,EAAE,MAAM,CAAC;QACd,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,QAAQ,CAAC,EAAE,OAAO,CAAC;KACpB,CAAC;CACH;AAED,MAAM,WAAW,SAAS;IACxB,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,kBAAkB;IACjC,EAAE,EAAE,MAAM,CAAC;IACX,IAAI,EAAE,MAAM,CAAC;IACb,QAAQ,EAAE,MAAM,CAAC;IACjB,QAAQ,EAAE,IAAI,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,cAAc;IAC7B,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,SAAS,EAAE,OAAO,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,OAAO,EAAE,MAAM,EAAE,CAAC;IAClB,eAAe,EAAE,SAAS,GAAG,IAAI,CAAC;IAClC,qBAAqB,EAAE,kBAAkB,GAAG,IAAI,CAAC;IACjD,cAAc,EAAE,sBAAsB,CAAC;IACvC,eAAe,EAAE,OAAO,CAAC;IACzB,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,YAAY,EAAE,IAAI,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;CACpB;AAED,MAAM,WAAW,cAAc;IAC7B,WAAW,EAAE,MAAM,CAAC;IACpB,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,EAAE,MAAM,CAAC;IACxB,gBAAgB,EAAE,MAAM,EAAE,CAAC;IAC3B,SAAS,EAAE,MAAM,EAAE,CAAC;IACpB,QAAQ,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,qBAAa,iBAAiB;IAE5B,YAAY,EAAE,MAAM,CAAC;IAGrB,MAAM,EAAE,cAAc,CAAC;IAIvB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,qBAAa,gBAAgB;IAE3B,YAAY,EAAE,MAAM,CAAC;IAKrB,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,qBAAa,qBAAqB;IAEhC,YAAY,EAAE,MAAM,CAAC;IAGrB,UAAU,EAAE,MAAM,CAAC;IAInB,IAAI,CAAC,EAAE,sBAAsB,CAAC;CAC/B;AAED;;GAEG;AACH,qBAAa,aAAc,SAAQ,KAAK;IACtC,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,aAAa,EAAE,OAAO,CAAC;IACvC,SAAgB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtC,SAAgB,OAAO,CAAC,EAAE,MAAM,CAAC;gBAG/B,OAAO,EAAE,MAAM,EACf,IAAI,GAAE,MAAyB,EAC/B,UAAU,GAAE,MAAY,EACxB,aAAa,GAAE,OAAc,EAC7B,YAAY,CAAC,EAAE,MAAM,EACrB,OAAO,CAAC,EAAE,MAAM;CAYnB;AA+ED;;;;;;GAMG;AACH,qBAAa,eAAe;IAC1B,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAkB;IACzC,OAAO,CAAC,cAAc,CAA0C;IAChE,OAAO,CAAC,cAAc,CAA0C;IAChE,OAAO,CAAC,cAAc,CAA0C;IAChE,OAAO,CAAC,gBAAgB,CAAC,CAAiB;IAC1C,OAAO,CAAC,eAAe,CAAC,CAAiB;IACzC,OAAO,CAAC,cAAc,CAAS;IAC/B,OAAO,CAAC,SAAS,CAAmB;IACpC,OAAO,CAAC,gBAAgB,CAA2B;IACnD,OAAO,CAAC,KAAK,CAAsC;IAGnD,OAAO,CAAC,qBAAqB,CAAC,CAAyB;IACvD,OAAO,CAAC,sBAAsB,CAAC,CAA0B;IACzD,OAAO,CAAC,qBAAqB,CAAC,CAAyB;IACvD,OAAO,CAAC,mBAAmB,CAAC,CAAuB;IACnD,OAAO,CAAC,2BAA2B,CAAC,CAA+B;IAEnE,OAAO,KAAK,oBAAoB,GAK/B;IAED,OAAO,KAAK,qBAAqB,GAKhC;IAED,OAAO,KAAK,oBAAoB,GAK/B;IAED,OAAO,KAAK,kBAAkB,GAK7B;IAED,OAAO,KAAK,0BAA0B,GAKrC;IAED,OAAO;IAmBP,MAAM,CAAC,WAAW,IAAI,eAAe;IAOrC;;;OAGG;YACW,aAAa;IAsB3B;;;OAGG;IACH,OAAO,CAAC,qBAAqB;IA6B7B;;;;;OAKG;IACG,kBAAkB,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC;IAoFvE;;;;OAIG;IACG,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsIrE;;;OAGG;IACG,aAAa,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAuDxD;;;OAGG;IACG,cAAc,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA0DzD;;;OAGG;IACG,UAAU,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA0ErD;;;OAGG;IACG,cAAc,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA0CzD;;;OAGG;IACH,OAAO,CAAC,gBAAgB;IAoBxB;;;OAGG;YACW,YAAY;IAuE1B;;;OAGG;YACW,YAAY;IAkE1B;;;OAGG;YACW,aAAa;IA2C3B;;;OAGG;YACW,eAAe;IA4C7B;;;OAGG;YACW,aAAa;IA+C3B;;;;OAIG;IACG,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA+CpE;;;;;OAKG;IACG,UAAU,CACd,YAAY,EAAE,MAAM,EACpB,KAAK,EAAE,MAAM,EACb,UAAU,GAAE,OAAe,GAC1B,OAAO,CAAC,IAAI,CAAC;IAoEhB;;;;OAIG;IACG,eAAe,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAsE3E;;;;OAIG;IACG,YAAY,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IAsIhE;;;OAGG;IACG,gBAAgB,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;IAmB5E;;;;OAIG;IACG,iBAAiB,CACrB,YAAY,EAAE,MAAM,EACpB,WAAW,EAAE,MAAM,GAClB,OAAO,CAAC,IAAI,CAAC;IAyBhB;;;OAGG;YACW,wBAAwB;IA2EtC;;;OAGG;YACW,wBAAwB;IAwFtC;;;OAGG;YACW,qBAAqB;IAqBnC;;;OAGG;YACW,uBAAuB;IAuBrC;;;OAGG;YACW,wBAAwB;IA6CtC;;;OAGG;YACW,yBAAyB;IA2DvC;;;OAGG;IACG,0BAA0B,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC;QAC9D,iBAAiB,EAAE,GAAG,EAAE,CAAC;QACzB,eAAe,EAAE,MAAM,EAAE,CAAC;QAC1B,WAAW,EAAE,MAAM,CAAC;KACrB,CAAC;IAkDF;;;OAGG;IACH,OAAO,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;IAmBnC;;;OAGG;IACG,aAAa,CAAC,UAAU,EAAE,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;IAoEjE;;OAEG;IACI,QAAQ,IAAI,IAAI;CAexB;AAGD,eAAO,MAAM,eAAe,iBAAgC,CAAC;AAC7D,eAAe,eAAe,CAAC"}