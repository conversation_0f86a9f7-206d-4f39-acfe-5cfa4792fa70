import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { AppDataSource } from '../config/database';
import { DeepPartial, Repository } from 'typeorm';
import { Restaurant } from '../models/Restaurant';
import { Suggestion, SuggestionStatus } from '../models/Suggestion';
import { PlaybackService } from '../services/PlaybackService';

// Evitar ruído de WS/Analytics
jest.mock('../services/WebSocketService', () => ({
  WebSocketService: {
    getInstance: () => ({
      emitToRestaurant: jest.fn(),
      emitToAdmins: jest.fn(),
    })
  }
}));

jest.mock('../services/PlaylistAnalyticsService', () => ({
  PlaylistAnalyticsService: {
    getInstance: () => ({
      trackPlay: jest.fn(),
      trackSkip: jest.fn(),
    })
  }
}));

// Redis mock leve
jest.mock('../config/redis', () => ({
  redisClient: {
    isReady: false,
    getClient: () => ({
      mGet: jest.fn().mockResolvedValue([]),
      get: jest.fn().mockResolvedValue('0'),
      setEx: jest.fn(),
    })
  }
}));

describe('PlaybackService respeita playlist ativa para fila (inclusive pagas)', () => {
  let restaurantRepo: Repository<Restaurant>;
  let suggestionRepo: Repository<Suggestion>;
  let restaurant: Restaurant;

  const REST_ID = 'rest-no-pl';

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    await initializeTestDatabase();
    // Redirecionar AppDataSource para TestDataSource
    (AppDataSource as any).getRepository = (entity: any) => TestDataSource.getRepository(entity);
    (AppDataSource as any).isInitialized = true;

    restaurantRepo = TestDataSource.getRepository(Restaurant);
    suggestionRepo = TestDataSource.getRepository(Suggestion);
  });

  afterAll(async () => {
    await cleanTestData();
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestData();
    restaurant = restaurantRepo.create({ id: REST_ID, name: 'Sem Playlist' });
    await restaurantRepo.save(restaurant);
  });

  it('sem playlist ativa: sugestões (pagas e normais) não devem entrar na fila do PlaybackService', async () => {
    // Criar duas sugestões aprovadas (uma paga)
  const paid = suggestionRepo.create({
      youtubeVideoId: 'VIDPAID12345',
      title: 'Paga',
      artist: 'Artista',
      duration: 180,
      restaurant,
      status: SuggestionStatus.APPROVED,
      isPaid: true,
      createdAt: new Date(),
      updatedAt: new Date(),
  } as DeepPartial<Suggestion>);
  const free = suggestionRepo.create({
      youtubeVideoId: 'VIDFREE54321',
      title: 'Normal',
      artist: 'Artista',
      duration: 200,
      restaurant,
      status: SuggestionStatus.APPROVED,
      isPaid: false,
      createdAt: new Date(),
      updatedAt: new Date(),
  } as DeepPartial<Suggestion>);
    await suggestionRepo.save([paid, free]);

    const psvc = PlaybackService.getInstance();
    const state = await psvc.initializePlayback(REST_ID);

    // Esperado: sem playlist ativa, a fila deve estar vazia
    expect(state.queue).toHaveLength(0);
    expect(state.priorityQueue).toHaveLength(0);
    expect(state.normalQueue).toHaveLength(0);
  });
});
