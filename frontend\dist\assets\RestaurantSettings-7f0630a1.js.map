{"version": 3, "file": "RestaurantSettings-7f0630a1.js", "sources": ["../../src/components/admin/RestaurantSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Settings,\r\n  Save,\r\n  Clock,\r\n  Users,\r\n  Music,\r\n  Shield,\r\n  Palette,\r\n  Bell,\r\n  Globe,\r\n  Volume2,\r\n  Eye,\r\n  Lock,\r\n  Youtube,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { YouTubeAuthManager } from \"@/components/restaurant/YouTubeAuthManager\";\r\nimport { useSettings } from \"@/store\";\r\nimport { useRestaurantContext } from \"@/components/restaurant/RestaurantDashboard\";\r\n\r\ninterface RestaurantConfig {\r\n  general: {\r\n    name: string;\r\n    description: string;\r\n    timezone: string;\r\n    language: string;\r\n  };\r\n  interface: {\r\n    theme: \"light\" | \"dark\" | \"auto\";\r\n    primaryColor: string;\r\n    allowSuggestions: boolean;\r\n    allowVoting: boolean;\r\n    showQueue: boolean;\r\n    showVoteCounts: boolean;\r\n    maxSuggestionsPerUser: number;\r\n  };\r\n  moderation: {\r\n    autoApprove: boolean;\r\n    requireModeration: boolean;\r\n    bannedWords: string[];\r\n    maxVotesForAutoApproval: number;\r\n    minVotesForAutoRejection: number;\r\n  };\r\n  schedule: {\r\n    enabled: boolean;\r\n    openTime: string;\r\n    closeTime: string;\r\n    timezone: string;\r\n    closedMessage: string;\r\n  };\r\n  notifications: {\r\n    emailNotifications: boolean;\r\n    newSuggestionAlert: boolean;\r\n    highVoteAlert: boolean;\r\n    moderationAlert: boolean;\r\n  };\r\n  audio: {\r\n    volume: number;\r\n    fadeInDuration: number;\r\n    fadeOutDuration: number;\r\n    crossfade: boolean;\r\n  };\r\n}\r\n\r\nconst RestaurantSettings: React.FC = () => {\r\n  const { settings, updateSettings } = useSettings();\r\n  const [config, setConfig] = useState<RestaurantConfig>({\r\n    general: {\r\n      name: \"Restaurante Demo\",\r\n      description: \"Um restaurante incrível com playlist interativa\",\r\n      timezone: \"America/Sao_Paulo\",\r\n      language: \"pt-BR\",\r\n    },\r\n    interface: {\r\n      theme: \"auto\",\r\n      primaryColor: \"#3B82F6\",\r\n      allowSuggestions: true,\r\n      allowVoting: true,\r\n      showQueue: true,\r\n      showVoteCounts: true,\r\n      maxSuggestionsPerUser: 5,\r\n    },\r\n    moderation: {\r\n      autoApprove: false,\r\n      requireModeration: true,\r\n      bannedWords: [\"palavra1\", \"palavra2\"],\r\n      maxVotesForAutoApproval: 10,\r\n      minVotesForAutoRejection: -5,\r\n    },\r\n    schedule: {\r\n      enabled: true,\r\n      openTime: \"11:00\",\r\n      closeTime: \"23:00\",\r\n      timezone: \"America/Sao_Paulo\",\r\n      closedMessage:\r\n        \"Estamos fechados. Volte durante nosso horário de funcionamento!\",\r\n    },\r\n    notifications: {\r\n      emailNotifications: true,\r\n      newSuggestionAlert: true,\r\n      highVoteAlert: true,\r\n      moderationAlert: true,\r\n    },\r\n    audio: {\r\n      volume: 75,\r\n      fadeInDuration: 3,\r\n      fadeOutDuration: 3,\r\n      crossfade: true,\r\n    },\r\n  });\r\n\r\n  const [loading, setLoading] = useState(false);\r\n  const [activeTab, setActiveTab] = useState(\"general\");\r\n  const { restaurantId } = useRestaurantContext();\r\n\r\n  const saveSettings = async () => {\r\n    setLoading(true);\r\n    try {\r\n      // Em uma implementação real, isso salvaria as configurações na API\r\n      await new Promise((resolve) => setTimeout(resolve, 1500));\r\n      toast.success(\"Configurações salvas com sucesso!\");\r\n    } catch (error) {\r\n      toast.error(\"Erro ao salvar configurações\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const updateConfig = (\r\n    section: keyof RestaurantConfig,\r\n    field: string,\r\n    value: any\r\n  ) => {\r\n    setConfig((prev) => ({\r\n      ...prev,\r\n      [section]: {\r\n        ...prev[section],\r\n        [field]: value,\r\n      },\r\n    }));\r\n  };\r\n\r\n  const tabs = [\r\n    { id: \"general\", name: \"Geral\", icon: Settings },\r\n    { id: \"interface\", name: \"Interface\", icon: Palette },\r\n    { id: \"moderation\", name: \"Moderação\", icon: Shield },\r\n    { id: \"schedule\", name: \"Horários\", icon: Clock },\r\n    { id: \"notifications\", name: \"Notificações\", icon: Bell },\r\n    { id: \"audio\", name: \"Áudio\", icon: Volume2 },\r\n    { id: \"youtube\", name: \"YouTube\", icon: Youtube },\r\n  ];\r\n\r\n  const renderGeneralSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Nome do Restaurante\r\n        </label>\r\n        <input\r\n          type=\"text\"\r\n          value={config.general.name}\r\n          onChange={(e) => updateConfig(\"general\", \"name\", e.target.value)}\r\n          className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n        />\r\n      </div>\r\n\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Descrição\r\n        </label>\r\n        <textarea\r\n          value={config.general.description}\r\n          onChange={(e) =>\r\n            updateConfig(\"general\", \"description\", e.target.value)\r\n          }\r\n          rows={3}\r\n          className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Fuso Horário\r\n          </label>\r\n          <select\r\n            value={config.general.timezone}\r\n            onChange={(e) =>\r\n              updateConfig(\"general\", \"timezone\", e.target.value)\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"America/Sao_Paulo\">São Paulo (GMT-3)</option>\r\n            <option value=\"America/New_York\">Nova York (GMT-5)</option>\r\n            <option value=\"Europe/London\">Londres (GMT+0)</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Idioma\r\n          </label>\r\n          <select\r\n            value={config.general.language}\r\n            onChange={(e) =>\r\n              updateConfig(\"general\", \"language\", e.target.value)\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"pt-BR\">Português (Brasil)</option>\r\n            <option value=\"en-US\">English (US)</option>\r\n            <option value=\"es-ES\">Español</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderInterfaceSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Tema\r\n          </label>\r\n          <select\r\n            value={config.interface.theme}\r\n            onChange={(e) => updateConfig(\"interface\", \"theme\", e.target.value)}\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"light\">Claro</option>\r\n            <option value=\"dark\">Escuro</option>\r\n            <option value=\"auto\">Automático</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Cor Principal\r\n          </label>\r\n          <input\r\n            type=\"color\"\r\n            value={config.interface.primaryColor}\r\n            onChange={(e) =>\r\n              updateConfig(\"interface\", \"primaryColor\", e.target.value)\r\n            }\r\n            className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Posição das Notificações\r\n          </label>\r\n          <select\r\n            value={settings.notificationPosition || 'top-left'}\r\n            onChange={(e) => updateSettings({ notificationPosition: e.target.value as any })}\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          >\r\n            <option value=\"top-right\">Topo Direita</option>\r\n            <option value=\"top-left\">Topo Esquerda</option>\r\n            <option value=\"bottom-right\">Base Direita</option>\r\n            <option value=\"bottom-left\">Base Esquerda</option>\r\n          </select>\r\n          <p className=\"text-sm text-gray-500 mt-1\">Ajusta onde os avisos aparecem na tela do restaurante</p>\r\n        </div>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Permitir Sugestões\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Clientes podem sugerir músicas\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.interface.allowSuggestions}\r\n              onChange={(e) =>\r\n                updateConfig(\"interface\", \"allowSuggestions\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Permitir Votação\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Clientes podem votar em sugestões\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.interface.allowVoting}\r\n              onChange={(e) =>\r\n                updateConfig(\"interface\", \"allowVoting\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Mostrar Fila\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Exibir fila de reprodução para clientes\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.interface.showQueue}\r\n              onChange={(e) =>\r\n                updateConfig(\"interface\", \"showQueue\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div>\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n          Máximo de Sugestões por Cliente\r\n        </label>\r\n        <input\r\n          type=\"number\"\r\n          min=\"1\"\r\n          max=\"20\"\r\n          value={config.interface.maxSuggestionsPerUser}\r\n          onChange={(e) =>\r\n            updateConfig(\r\n              \"interface\",\r\n              \"maxSuggestionsPerUser\",\r\n              parseInt(e.target.value)\r\n            )\r\n          }\r\n          className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderModerationSettings = () => (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Aprovação Automática\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Aprovar sugestões automaticamente com base em votos\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.moderation.autoApprove}\r\n              onChange={(e) =>\r\n                updateConfig(\"moderation\", \"autoApprove\", e.target.checked)\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n              Requer Moderação\r\n            </h4>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Todas as sugestões precisam ser moderadas\r\n            </p>\r\n          </div>\r\n          <label className=\"relative inline-flex items-center cursor-pointer\">\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={config.moderation.requireModeration}\r\n              onChange={(e) =>\r\n                updateConfig(\r\n                  \"moderation\",\r\n                  \"requireModeration\",\r\n                  e.target.checked\r\n                )\r\n              }\r\n              className=\"sr-only peer\"\r\n            />\r\n            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Votos para Aprovação Automática\r\n          </label>\r\n          <input\r\n            type=\"number\"\r\n            value={config.moderation.maxVotesForAutoApproval}\r\n            onChange={(e) =>\r\n              updateConfig(\r\n                \"moderation\",\r\n                \"maxVotesForAutoApproval\",\r\n                parseInt(e.target.value)\r\n              )\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          />\r\n        </div>\r\n\r\n        <div>\r\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n            Votos para Rejeição Automática\r\n          </label>\r\n          <input\r\n            type=\"number\"\r\n            value={config.moderation.minVotesForAutoRejection}\r\n            onChange={(e) =>\r\n              updateConfig(\r\n                \"moderation\",\r\n                \"minVotesForAutoRejection\",\r\n                parseInt(e.target.value)\r\n              )\r\n            }\r\n            className=\"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderCurrentTab = () => {\r\n    switch (activeTab) {\r\n      case \"general\":\r\n        return renderGeneralSettings();\r\n      case \"interface\":\r\n        return renderInterfaceSettings();\r\n      case \"moderation\":\r\n        return renderModerationSettings();\r\n      case \"youtube\":\r\n        return renderYouTubeSettings();\r\n      default:\r\n        return (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            Em desenvolvimento...\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  const renderYouTubeSettings = () => {\r\n    console.log(\r\n      \"🎵 Renderizando configurações do YouTube para restaurantId:\",\r\n      restaurantId\r\n    );\r\n\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        <div>\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n            Integração com YouTube\r\n          </h3>\r\n          <p className=\"text-gray-600 dark:text-gray-400 text-sm mb-6\">\r\n            Configure a autenticação com YouTube para controlar playlists em\r\n            tempo real baseado nas votações dos clientes.\r\n          </p>\r\n        </div>\r\n\r\n        <YouTubeAuthManager\r\n          restaurantId={restaurantId}\r\n          onAuthStatusChange={(isAuthenticated) => {\r\n            console.log(\"YouTube Auth Status:\", isAuthenticated);\r\n          }}\r\n        />\r\n\r\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\r\n          <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">\r\n            💡 Como funciona\r\n          </h4>\r\n          <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1\">\r\n            <li>• Conecte sua conta YouTube Premium</li>\r\n            <li>• Crie playlists controláveis pelo sistema</li>\r\n            <li>\r\n              • As votações dos clientes reordenam automaticamente as músicas\r\n            </li>\r\n            <li>• Controle total sobre a ordem de reprodução</li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            Configurações do Restaurante\r\n          </h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Personalize a experiência do seu restaurante\r\n          </p>\r\n        </div>\r\n\r\n        <button\r\n          onClick={saveSettings}\r\n          disabled={loading}\r\n          className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2\"\r\n        >\r\n          <Save className=\"w-4 h-4\" />\r\n          <span>{loading ? \"Salvando...\" : \"Salvar\"}</span>\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n          <nav className=\"flex space-x-8 px-6\">\r\n            {tabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                onClick={() => setActiveTab(tab.id)}\r\n                className={`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${\r\n                  activeTab === tab.id\r\n                    ? \"border-blue-500 text-blue-600 dark:text-blue-400\"\r\n                    : \"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300\"\r\n                }`}\r\n              >\r\n                <tab.icon className=\"w-4 h-4\" />\r\n                <span>{tab.name}</span>\r\n              </button>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n\r\n        {/* Tab Content */}\r\n        <div className=\"p-6\">{renderCurrentTab()}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RestaurantSettings;\r\n"], "names": ["RestaurantSettings", "settings", "updateSettings", "useSettings", "config", "setConfig", "useState", "loading", "setLoading", "activeTab", "setActiveTab", "restaurantId", "useRestaurantContext", "saveSettings", "resolve", "toast", "updateConfig", "section", "field", "value", "prev", "tabs", "Settings", "Palette", "Shield", "Clock", "Bell", "Volume2", "Youtube", "renderGeneralSettings", "jsxs", "jsx", "e", "renderInterfaceSettings", "renderModerationSettings", "renderCurrentTab", "renderYouTubeSettings", "YouTubeAuthManager", "isAuthenticated", "Save", "tab"], "mappings": "wSAkEA,MAAMA,EAA+B,IAAM,CACzC,KAAM,CAAE,SAAAC,EAAU,eAAAC,CAAe,EAAIC,EAAY,EAC3C,CAACC,EAAQC,CAAS,EAAIC,WAA2B,CACrD,QAAS,CACP,KAAM,mBACN,YAAa,kDACb,SAAU,oBACV,SAAU,OACZ,EACA,UAAW,CACT,MAAO,OACP,aAAc,UACd,iBAAkB,GAClB,YAAa,GACb,UAAW,GACX,eAAgB,GAChB,sBAAuB,CACzB,EACA,WAAY,CACV,YAAa,GACb,kBAAmB,GACnB,YAAa,CAAC,WAAY,UAAU,EACpC,wBAAyB,GACzB,yBAA0B,EAC5B,EACA,SAAU,CACR,QAAS,GACT,SAAU,QACV,UAAW,QACX,SAAU,oBACV,cACE,iEACJ,EACA,cAAe,CACb,mBAAoB,GACpB,mBAAoB,GACpB,cAAe,GACf,gBAAiB,EACnB,EACA,MAAO,CACL,OAAQ,GACR,eAAgB,EAChB,gBAAiB,EACjB,UAAW,EACb,CAAA,CACD,EAEK,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAK,EACtC,CAACG,EAAWC,CAAY,EAAIJ,WAAS,SAAS,EAC9C,CAAE,aAAAK,GAAiBC,IAEnBC,EAAe,SAAY,CAC/BL,EAAW,EAAI,EACX,GAAA,CAEF,MAAM,IAAI,QAASM,GAAY,WAAWA,EAAS,IAAI,CAAC,EACxDC,EAAM,QAAQ,mCAAmC,OACnC,CACdA,EAAM,MAAM,8BAA8B,CAAA,QAC1C,CACAP,EAAW,EAAK,CAClB,CAAA,EAGIQ,EAAe,CACnBC,EACAC,EACAC,IACG,CACHd,EAAWe,IAAU,CACnB,GAAGA,EACH,CAACH,CAAO,EAAG,CACT,GAAGG,EAAKH,CAAO,EACf,CAACC,CAAK,EAAGC,CACX,CACA,EAAA,CAAA,EAGEE,EAAO,CACX,CAAE,GAAI,UAAW,KAAM,QAAS,KAAMC,CAAS,EAC/C,CAAE,GAAI,YAAa,KAAM,YAAa,KAAMC,CAAQ,EACpD,CAAE,GAAI,aAAc,KAAM,YAAa,KAAMC,CAAO,EACpD,CAAE,GAAI,WAAY,KAAM,WAAY,KAAMC,CAAM,EAChD,CAAE,GAAI,gBAAiB,KAAM,eAAgB,KAAMC,CAAK,EACxD,CAAE,GAAI,QAAS,KAAM,QAAS,KAAMC,CAAQ,EAC5C,CAAE,GAAI,UAAW,KAAM,UAAW,KAAMC,CAAQ,CAAA,EAG5CC,EAAwB,IAC3BC,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,sBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAO3B,EAAO,QAAQ,KACtB,SAAW4B,GAAMhB,EAAa,UAAW,OAAQgB,EAAE,OAAO,KAAK,EAC/D,UAAU,iIAAA,CACZ,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,YAAA,EACAA,EAAA,IAAC,WAAA,CACC,MAAO3B,EAAO,QAAQ,YACtB,SAAW4B,GACThB,EAAa,UAAW,cAAegB,EAAE,OAAO,KAAK,EAEvD,KAAM,EACN,UAAU,iIAAA,CACZ,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,eAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAO1B,EAAO,QAAQ,SACtB,SAAW4B,GACThB,EAAa,UAAW,WAAYgB,EAAE,OAAO,KAAK,EAEpD,UAAU,kIAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,oBAAoB,SAAiB,oBAAA,EAClDA,EAAA,IAAA,SAAA,CAAO,MAAM,mBAAmB,SAAiB,oBAAA,EACjDA,EAAA,IAAA,SAAA,CAAO,MAAM,gBAAgB,SAAe,kBAAA,CAAA,CAAA,CAC/C,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,SAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAO1B,EAAO,QAAQ,SACtB,SAAW4B,GACThB,EAAa,UAAW,WAAYgB,EAAE,OAAO,KAAK,EAEpD,UAAU,kIAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAkB,qBAAA,EACvCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAY,eAAA,EACjCA,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAO,UAAA,CAAA,CAAA,CAC/B,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,EAGIE,EAA0B,IAC7BH,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,OAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAO1B,EAAO,UAAU,MACxB,SAAW4B,GAAMhB,EAAa,YAAa,QAASgB,EAAE,OAAO,KAAK,EAClE,UAAU,kIAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAM,SAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAU,aAAA,CAAA,CAAA,CACjC,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,gBAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,QACL,MAAO3B,EAAO,UAAU,aACxB,SAAW4B,GACThB,EAAa,YAAa,eAAgBgB,EAAE,OAAO,KAAK,EAE1D,UAAU,oEAAA,CACZ,CAAA,EACF,CAAA,EACF,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,2BAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAO7B,EAAS,sBAAwB,WACxC,SAAW+B,GAAM9B,EAAe,CAAE,qBAAsB8B,EAAE,OAAO,MAAc,EAC/E,UAAU,kIAEV,SAAA,CAACD,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAY,eAAA,EACrCA,EAAA,IAAA,SAAA,CAAO,MAAM,WAAW,SAAa,gBAAA,EACrCA,EAAA,IAAA,SAAA,CAAO,MAAM,eAAe,SAAY,eAAA,EACxCA,EAAA,IAAA,SAAA,CAAO,MAAM,cAAc,SAAa,gBAAA,CAAA,CAAA,CAC3C,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAAqD,wDAAA,CAAA,EACjG,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,qBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,iCAAA,CAAA,EACF,EACAD,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS3B,EAAO,UAAU,iBAC1B,SAAW4B,GACThB,EAAa,YAAa,mBAAoBgB,EAAE,OAAO,OAAO,EAEhE,UAAU,cAAA,CACZ,EACAD,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,oCAAA,CAAA,EACF,EACAD,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS3B,EAAO,UAAU,YAC1B,SAAW4B,GACThB,EAAa,YAAa,cAAegB,EAAE,OAAO,OAAO,EAE3D,UAAU,cAAA,CACZ,EACAD,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,eAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,0CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS3B,EAAO,UAAU,UAC1B,SAAW4B,GACThB,EAAa,YAAa,YAAagB,EAAE,OAAO,OAAO,EAEzD,UAAU,cAAA,CACZ,EACAD,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,kCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,IAAI,IACJ,IAAI,KACJ,MAAO3B,EAAO,UAAU,sBACxB,SAAW4B,GACThB,EACE,YACA,wBACA,SAASgB,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,iIAAA,CACZ,CAAA,EACF,CACF,CAAA,CAAA,EAGIE,EAA2B,IAC9BJ,OAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,uBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,sDAAA,CAAA,EACF,EACAD,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS3B,EAAO,WAAW,YAC3B,SAAW4B,GACThB,EAAa,aAAc,cAAegB,EAAE,OAAO,OAAO,EAE5D,UAAU,cAAA,CACZ,EACAD,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,4CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS3B,EAAO,WAAW,kBAC3B,SAAW4B,GACThB,EACE,aACA,oBACAgB,EAAE,OAAO,OACX,EAEF,UAAU,cAAA,CACZ,EACAD,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,CAAA,EACF,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,kCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAO3B,EAAO,WAAW,wBACzB,SAAW4B,GACThB,EACE,aACA,0BACA,SAASgB,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,iIAAA,CACZ,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,iCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAO3B,EAAO,WAAW,yBACzB,SAAW4B,GACThB,EACE,aACA,2BACA,SAASgB,EAAE,OAAO,KAAK,CACzB,EAEF,UAAU,iIAAA,CACZ,CAAA,EACF,CAAA,EACF,CACF,CAAA,CAAA,EAGIG,EAAmB,IAAM,CAC7B,OAAQ1B,EAAW,CACjB,IAAK,UACH,OAAOoB,EAAsB,EAC/B,IAAK,YACH,OAAOI,EAAwB,EACjC,IAAK,aACH,OAAOC,EAAyB,EAClC,IAAK,UACH,OAAOE,EAAsB,EAC/B,QACE,OACGL,EAAAA,IAAA,MAAA,CAAI,UAAU,iCAAiC,SAEhD,uBAAA,CAAA,CAEN,CAAA,EAGIK,EAAwB,KACpB,QAAA,IACN,8DACAzB,CAAA,EAIAmB,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,yBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAG7D,iHAAA,CAAA,EACF,EAEAA,EAAA,IAACM,EAAA,CACC,aAAA1B,EACA,mBAAqB2B,GAAoB,CAC/B,QAAA,IAAI,uBAAwBA,CAAe,CACrD,CAAA,CACF,EAEAR,EAAAA,KAAC,MAAI,CAAA,UAAU,4FACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,oDAAoD,SAElE,mBAAA,EACAD,EAAAA,KAAC,KAAG,CAAA,UAAU,qDACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAmC,qCAAA,CAAA,EACvCA,EAAAA,IAAC,MAAG,SAA0C,4CAAA,CAAA,EAC9CA,EAAAA,IAAC,MAAG,SAEJ,iEAAA,CAAA,EACAA,EAAAA,IAAC,MAAG,SAA4C,8CAAA,CAAA,CAAA,EAClD,CAAA,EACF,CACF,CAAA,CAAA,GAKF,OAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAEjE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,+CAAA,CAAA,EACF,EAEAD,EAAA,KAAC,SAAA,CACC,QAASjB,EACT,SAAUN,EACV,UAAU,gHAEV,SAAA,CAACwB,EAAAA,IAAAQ,EAAA,CAAK,UAAU,SAAU,CAAA,EACzBR,EAAA,IAAA,OAAA,CAAM,SAAUxB,EAAA,cAAgB,SAAS,CAAA,CAAA,CAC5C,CAAA,EACF,EAEAuB,EAAAA,KAAC,MAAI,CAAA,UAAU,8CAEb,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,gDACb,SAACA,EAAAA,IAAA,MAAA,CAAI,UAAU,sBACZ,SAAAV,EAAK,IAAKmB,GACTV,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMpB,EAAa8B,EAAI,EAAE,EAClC,UAAW,0FACT/B,IAAc+B,EAAI,GACd,mDACA,wHACN,GAEA,SAAA,CAAAT,EAAAA,IAACS,EAAI,KAAJ,CAAS,UAAU,SAAU,CAAA,EAC9BT,EAAAA,IAAC,OAAM,CAAA,SAAAS,EAAI,IAAK,CAAA,CAAA,CAAA,EATXA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGCT,EAAA,IAAA,MAAA,CAAI,UAAU,MAAO,aAAmB,CAAA,EAC3C,CACF,CAAA,CAAA,CAEJ"}