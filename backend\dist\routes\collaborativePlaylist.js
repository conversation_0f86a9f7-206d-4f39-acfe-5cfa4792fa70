"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../utils/validation");
const asyncHandler_1 = __importDefault(require("../middleware/asyncHandler"));
const auth_1 = require("../middleware/auth");
const CollaborativePlaylistService_1 = require("../services/CollaborativePlaylistService");
const voteRateLimit_1 = require("../utils/voteRateLimit");
const redis_1 = require("../config/redis");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/create:
 *   post:
 *     summary: Criar playlist colaborativa para restaurante
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               playlistName:
 *                 type: string
 *                 example: "Playlist Principal"
 *               description:
 *                 type: string
 *                 example: "Playlist colaborativa do restaurante"
 *               initialTracks:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dQw4w9WgXcQ", "kJQP7kiw5Fk"]
 *     responses:
 *       201:
 *         description: Playlist colaborativa criada com sucesso
 */
router.post("/:restaurantId/create", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("playlistName")
        .notEmpty()
        .withMessage("Nome da playlist é obrigatório"),
    (0, validation_1.body)("description").optional().isString(),
    (0, validation_1.body)("initialTracks").optional().isArray(),
], auth_1.authMiddleware, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { playlistName, description, initialTracks = [] } = req.body;
    console.log(`🎵 Criando playlist colaborativa: ${playlistName} para ${restaurantId}`);
    // 1. Criar playlist colaborativa
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.createRestaurantPlaylist(restaurantId, playlistName, description);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    // 2. Adicionar músicas iniciais se fornecidas
    let initialTracksResult = null;
    if (initialTracks.length > 0 && result.playlistId) {
        initialTracksResult = await CollaborativePlaylistService_1.collaborativePlaylistService.addInitialTracks(result.playlistId, initialTracks);
    }
    res.status(201).json({
        success: true,
        message: result.message,
        data: {
            playlistId: result.playlistId,
            youtubePlaylistId: result.youtubePlaylistId,
            initialTracksAdded: initialTracksResult?.addedCount || 0,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/stats:
 *   get:
 *     summary: Obter estatísticas da playlist colaborativa
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Estatísticas da playlist colaborativa
 */
router.get("/:restaurantId/stats", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.getPlaylistStats(restaurantId);
    if (!result.success) {
        return res.status(404).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: result.data,
    });
}));
/**
 * Compat: endpoint legado para obter a música atual tocando.
 * Mapeia a partir do estado de playback (/playback/:restaurantId/state).
 * Mantido para clientes antigos que ainda chamam 'currently-playing'.
 */
router.get("/:restaurantId/currently-playing", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    try {
        const stateKey = `playback:${restaurantId}`;
        const cachedState = await redis_1.redisClient.getClient().get(stateKey);
        let currentTrack = null;
        if (cachedState) {
            const playbackState = JSON.parse(cachedState);
            const song = playbackState?.currentSong;
            if (song) {
                currentTrack = {
                    id: song.id || song.youtubeId,
                    youtubeVideoId: song.youtubeId || song.id,
                    title: song.title,
                    artist: song.artist,
                    duration: song.duration || 0,
                    formattedDuration: song.duration
                        ? `${Math.floor((song.duration || 0) / 60)}:${String(Math.floor((song.duration || 0) % 60)).padStart(2, "0")}`
                        : "0:00",
                    thumbnailUrl: song.thumbnailUrl,
                    upvotes: 0,
                    downvotes: 0,
                    voteCount: 0,
                    score: 0,
                };
            }
        }
        return res.json({
            success: true,
            message: currentTrack ? "Música atual encontrada" : "Nenhuma música tocando",
            currentTrack,
        });
    }
    catch (error) {
        console.error("Erro ao obter currently-playing (compat):", error);
        return res.status(500).json({
            success: false,
            message: "Erro ao obter música atual",
        });
    }
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/vote:
 *   post:
 *     summary: Processar voto normal (gratuito)
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 example: "dQw4w9WgXcQ"
 *               tableNumber:
 *                 type: integer
 *                 example: 5
 *               clientSessionId:
 *                 type: string
 *                 example: "session_123"
 *     responses:
 *       200:
 *         description: Voto registrado com sucesso
 */
router.post("/:restaurantId/vote", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("youtubeVideoId")
        .notEmpty()
        .withMessage("ID do vídeo do YouTube é obrigatório"),
    (0, validation_1.body)("tableNumber").optional().isInt(),
    (0, validation_1.body)("clientSessionId").optional().isString(),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { youtubeVideoId, tableNumber, clientSessionId } = req.body;
    // VOTE RATE LIMIT: bloquear votos normais consecutivos por sessão/IP
    const clientIp = req.headers["x-forwarded-for"]?.split(",")[0]?.trim() ||
        req.socket?.remoteAddress || undefined;
    const rate = await (0, voteRateLimit_1.checkNormalVoteRateLimit)(restaurantId, clientSessionId, clientIp);
    if (!rate.allowed) {
        return res.status(429).json({
            success: false,
            message: `Muitos votos em sequência. Tente novamente em ${rate.retryAfter ?? 180}s`,
            retryAfter: rate.retryAfter ?? 180,
        });
    }
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.processNormalVote(restaurantId, youtubeVideoId, tableNumber, clientSessionId);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: {
            voteWeight: result.voteWeight,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/supervote:
 *   post:
 *     summary: Processar supervoto (pago)
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               youtubeVideoId:
 *                 type: string
 *                 example: "dQw4w9WgXcQ"
 *               paymentAmount:
 *                 type: number
 *                 example: 5.00
 *                 description: "Valor em reais (R$ 5, R$ 20 ou R$ 50)"
 *               paymentId:
 *                 type: string
 *                 example: "pix_123456"
 *               tableNumber:
 *                 type: integer
 *                 example: 5
 *               clientSessionId:
 *                 type: string
 *                 example: "session_123"
 *     responses:
 *       200:
 *         description: Supervoto processado com sucesso
 */
router.post("/:restaurantId/supervote", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("youtubeVideoId")
        .notEmpty()
        .withMessage("ID do vídeo do YouTube é obrigatório"),
    (0, validation_1.body)("paymentAmount")
        .isFloat({ min: 5 })
        .withMessage("Valor do pagamento deve ser no mínimo R$ 5,00"),
    (0, validation_1.body)("paymentId").notEmpty().withMessage("ID do pagamento é obrigatório"),
    (0, validation_1.body)("tableNumber").optional().isInt(),
    (0, validation_1.body)("clientSessionId").optional().isString(),
    (0, validation_1.body)("clientMessage").optional().isString().isLength({ max: 200 }),
    (0, validation_1.body)("clientName").optional().isString().isLength({ max: 80 }),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { youtubeVideoId, paymentAmount, paymentId, tableNumber, clientSessionId, clientMessage, clientName, } = req.body;
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.processSuperVote(restaurantId, youtubeVideoId, paymentAmount, paymentId, tableNumber, clientSessionId, clientMessage, clientName);
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: {
            voteWeight: result.voteWeight,
            paymentAmount,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/add-tracks:
 *   post:
 *     summary: Adicionar músicas à playlist colaborativa
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               videoIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["dQw4w9WgXcQ", "kJQP7kiw5Fk"]
 *     responses:
 *       200:
 *         description: Músicas adicionadas com sucesso
 */
router.post("/:restaurantId/add-tracks", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("videoIds")
        .isArray({ min: 1 })
        .withMessage("Lista de vídeos é obrigatória"),
], auth_1.authMiddleware, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { videoIds } = req.body;
    // Buscar playlist ativa do restaurante
    const active = await CollaborativePlaylistService_1.collaborativePlaylistService.getActivePlaylistForRestaurant(restaurantId);
    if (!active || !active.id) {
        return res.status(404).json({
            success: false,
            message: "Playlist ativa não encontrada para o restaurante",
        });
    }
    // Adicionar músicas pela service (salva no YouTube e no DB)
    const addResult = await CollaborativePlaylistService_1.collaborativePlaylistService.addInitialTracks(active.id, videoIds);
    if (!addResult.success) {
        return res.status(400).json({ success: false, message: addResult.message });
    }
    // Retornar estatísticas atualizadas
    const stats = await CollaborativePlaylistService_1.collaborativePlaylistService.getPlaylistStats(restaurantId);
    res.json({
        success: true,
        message: addResult.message,
        data: {
            addedCount: addResult.addedCount || 0,
            playlistId: active.id,
            youtubePlaylistId: active.youtubePlaylistId,
            stats: stats.success ? stats.data : undefined,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/ranking:
 *   get:
 *     summary: Obter ranking de votação
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: Ranking de votação
 */
router.get("/:restaurantId/ranking", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.query)("limit").optional().isInt({ min: 1, max: 100 }),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { limit = 20 } = req.query;
    // Evitar cache para sempre retornar dados frescos
    res.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
    res.setHeader("Pragma", "no-cache");
    res.setHeader("Expires", "0");
    const result = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, parseInt(limit));
    if (!result.success) {
        return res.status(400).json({
            success: false,
            message: result.message,
        });
    }
    res.json({
        success: true,
        message: result.message,
        data: result.data,
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/reorder:
 *   post:
 *     summary: Reordenar playlist baseada em votos
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Playlist reordenada com sucesso
 */
router.post("/:restaurantId/reorder", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    // Usar o serviço central de reordenação (emite WebSocket e atualiza DB)
    const { playlistReorderService } = await Promise.resolve().then(() => __importStar(require("../services/PlaylistReorderService")));
    const reorderResult = await playlistReorderService.manualReorder(restaurantId);
    if (!reorderResult.success) {
        return res.status(400).json({
            success: false,
            message: reorderResult.message,
        });
    }
    res.json({
        success: true,
        message: reorderResult.message,
        data: {
            reorderedCount: reorderResult.tracksReordered,
            playlistId: reorderResult.playlistId,
        },
    });
}));
/**
 * @swagger
 * /api/v1/collaborative-playlist/{restaurantId}/cooldown/{youtubeVideoId}:
 *   get:
 *     summary: Verificar se uma música está em cooldown
 *     tags: [Collaborative Playlist]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: youtubeVideoId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status de cooldown da música
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 isInCooldown:
 *                   type: boolean
 *                 cooldownTimeLeft:
 *                   type: number
 *                   description: Tempo restante em segundos
 */
router.get("/:restaurantId/cooldown/:youtubeVideoId", [
    (0, validation_1.param)("restaurantId").isString().notEmpty(),
    (0, validation_1.param)("youtubeVideoId").isString().notEmpty(),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError("Dados inválidos", errors.array());
    }
    // Evitar cache para sempre retornar dados frescos
    res.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
    res.setHeader("Pragma", "no-cache");
    res.setHeader("Expires", "0");
    const { restaurantId, youtubeVideoId } = req.params;
    const isInCooldown = await CollaborativePlaylistService_1.collaborativePlaylistService.isSongInCooldown(restaurantId, youtubeVideoId);
    let cooldownTimeLeft = 0;
    if (isInCooldown) {
        try {
            const client = redis_1.redisClient.getClient();
            const key = `song:cooldown:${restaurantId}:${youtubeVideoId}`;
            cooldownTimeLeft = await client.ttl(key);
            if (cooldownTimeLeft < 0)
                cooldownTimeLeft = 0;
        }
        catch (error) {
            console.warn("Erro ao obter TTL do cooldown:", error);
        }
    }
    res.json({
        success: true,
        isInCooldown,
        cooldownTimeLeft,
    });
}));
exports.default = router;
//# sourceMappingURL=collaborativePlaylist.js.map