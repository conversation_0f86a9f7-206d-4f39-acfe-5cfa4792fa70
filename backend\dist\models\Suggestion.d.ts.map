{"version": 3, "file": "Suggestion.d.ts", "sourceRoot": "", "sources": ["../../src/models/Suggestion.ts"], "names": [], "mappings": "AAWA,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAGhD,oBAAY,gBAAgB;IAC1B,OAAO,YAAY;IACnB,QAAQ,aAAa;IACrB,QAAQ,aAAa;IACrB,OAAO,YAAY;IACnB,MAAM,WAAW;IACjB,OAAO,YAAY;IACnB,SAAS,cAAc;IACvB,OAAO,YAAY;CACpB;AAED,oBAAY,gBAAgB;IAC1B,MAAM,WAAW;IACjB,KAAK,UAAU;IACf,IAAI,SAAS;IACb,MAAM,WAAW;CAClB;AAED,qBAEa,UAAU;IAErB,EAAE,EAAE,MAAM,CAAC;IAGX,cAAc,EAAE,MAAM,CAAC;IAGvB,KAAK,EAAE,MAAM,CAAC;IAGd,MAAM,EAAE,MAAM,CAAC;IAGf,KAAK,CAAC,EAAE,MAAM,CAAC;IAGf,WAAW,EAAE,MAAM,CAAC;IAGpB,QAAQ,EAAE,MAAM,CAAC;IAGjB,YAAY,EAAE,MAAM,CAAC;IAGrB,WAAW,EAAE,MAAM,CAAC;IAOpB,MAAM,EAAE,gBAAgB,CAAC;IAOzB,MAAM,EAAE,gBAAgB,CAAC;IAGzB,eAAe,CAAC,EAAE,MAAM,CAAC;IAGzB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAGlB,eAAe,CAAC,EAAE,MAAM,CAAC;IAGzB,SAAS,EAAE,MAAM,CAAC;IAGlB,OAAO,EAAE,MAAM,CAAC;IAGhB,SAAS,EAAE,MAAM,CAAC;IAGlB,WAAW,EAAE,IAAI,CAAC;IAGlB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAG1B,QAAQ,EAAE,IAAI,CAAC;IAGf,YAAY,EAAE,MAAM,CAAC;IAGrB,UAAU,CAAC,EAAE,MAAM,CAAC;IAGpB,QAAQ,EAAE;QACR,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;QACjB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,QAAQ,CAAC,EAAE,OAAO,CAAC;QACnB,IAAI,CAAC,EAAE,OAAO,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;KACjB,CAAC;IAGF,eAAe,EAAE;QACf,YAAY,CAAC,EAAE,OAAO,CAAC;QACvB,YAAY,CAAC,EAAE,MAAM,EAAE,CAAC;QACxB,eAAe,CAAC,EAAE,MAAM,EAAE,CAAC;QAC3B,eAAe,CAAC,EAAE,OAAO,CAAC;QAC1B,gBAAgB,CAAC,EAAE,OAAO,CAAC;QAC3B,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB,QAAQ,CAAC,EAAE,OAAO,CAAC;KACpB,CAAC;IAIF,UAAU,EAAE,OAAO,CAAC;IAGpB,MAAM,EAAE,OAAO,CAAC;IAGhB,MAAM,CAAC,EAAE,IAAI,CAAC;IASd,aAAa,CAAC,EAAE,MAAM,CAAC;IAGvB,SAAS,CAAC,EAAE,MAAM,CAAC;IAOnB,aAAa,CAAC,EAAE,MAAM,CAAC;IAGvB,OAAO,CAAC,EAAE,MAAM,CAAC;IAGjB,aAAa,CAAC,EAAE,MAAM,CAAC;IAIvB,UAAU,CAAC,EAAE,MAAM,CAAC;IAGpB,aAAa,CAAC,EAAE,MAAM,CAAC;IAGvB,WAAW,CAAC,EAAE,MAAM,CAAC;IAGrB,SAAS,CAAC,EAAE,MAAM,CAAC;IAGnB,WAAW,CAAC,EAAE,IAAI,CAAC;IAGnB,UAAU,EAAE,MAAM,CAAC;IAGnB,eAAe,CAAC,EAAE,MAAM,CAAC;IAMzB,UAAU,EAAE,UAAU,CAAC;IAOvB,QAAQ,EAAE,QAAQ,CAAC;IAOnB,WAAW,CAAC,EAAE,IAAI,CAAC;IAOnB,WAAW,EAAE,IAAI,CAAC;IAGlB,KAAK,EAAE,IAAI,EAAE,CAAC;IAQd,aAAa,CAAC,EAAE,aAAa,CAAC;IAG9B,SAAS,EAAE,IAAI,CAAC;IAGhB,SAAS,EAAE,IAAI,CAAC;IAKhB,cAAc,IAAI,MAAM;IAOxB,OAAO,CAAC,kBAAkB;IAgB1B,UAAU,IAAI,OAAO;IAOrB,SAAS,IAAI,OAAO;IAOpB,oBAAoB,IAAI,MAAM;IAS9B,UAAU,IAAI,OAAO;IAKrB,MAAM,IAAI,OAAO;IAKjB,cAAc,IAAI,OAAO;IAKzB,gBAAgB,IAAI,OAAO;IAK3B,gBAAgB,IAAI,OAAO;IAK3B,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,IAAI;IAYnC,oBAAoB,IAAI,IAAI;IAK5B,aAAa,IAAI,MAAM;IAKvB,kBAAkB,IAAI,MAAM;IAK5B,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAQhD,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAQ/C,YAAY,IAAI,IAAI;IAMpB,aAAa,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI;IAQtC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI;IAM3B,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;IAK3C,eAAe,IAAI,OAAO;IAK1B,WAAW,IAAI,OAAO;IAOtB,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2CZ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsBZ"}