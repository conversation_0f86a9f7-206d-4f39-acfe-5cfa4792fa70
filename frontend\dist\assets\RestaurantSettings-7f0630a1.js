import{e as w,u as C,j as e,c}from"./index-de865c15.js";import{r as s}from"./vendor-66b0ef43.js";import{Y as S}from"./YouTubeAuthManager-18e1f41f.js";import{u as A,d as T,aJ as P,o as V,K as R,w as E,a2 as M,at as Y}from"./ui-d6218fb3.js";import"./router-f729e475.js";import"./utils-08f61814.js";const U=()=>{const{settings:u,updateSettings:g}=w(),[a,x]=s.useState({general:{name:"Restaurante Demo",description:"Um restaurante incrível com playlist interativa",timezone:"America/Sao_Paulo",language:"pt-BR"},interface:{theme:"auto",primaryColor:"#3B82F6",allowSuggestions:!0,allowVoting:!0,showQueue:!0,showVoteCounts:!0,maxSuggestionsPerUser:5},moderation:{autoApprove:!1,requireModeration:!0,bannedWords:["palavra1","palavra2"],maxVotesForAutoApproval:10,minVotesForAutoRejection:-5},schedule:{enabled:!0,openTime:"11:00",closeTime:"23:00",timezone:"America/Sao_Paulo",closedMessage:"Estamos fechados. Volte durante nosso horário de funcionamento!"},notifications:{emailNotifications:!0,newSuggestionAlert:!0,highVoteAlert:!0,moderationAlert:!0},audio:{volume:75,fadeInDuration:3,fadeOutDuration:3,crossfade:!0}}),[o,l]=s.useState(!1),[n,m]=s.useState("general"),{restaurantId:i}=C(),h=async()=>{l(!0);try{await new Promise(r=>setTimeout(r,1500)),c.success("Configurações salvas com sucesso!")}catch{c.error("Erro ao salvar configurações")}finally{l(!1)}},t=(r,v,N)=>{x(d=>({...d,[r]:{...d[r],[v]:N}}))},p=[{id:"general",name:"Geral",icon:T},{id:"interface",name:"Interface",icon:P},{id:"moderation",name:"Moderação",icon:V},{id:"schedule",name:"Horários",icon:R},{id:"notifications",name:"Notificações",icon:E},{id:"audio",name:"Áudio",icon:M},{id:"youtube",name:"YouTube",icon:Y}],b=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome do Restaurante"}),e.jsx("input",{type:"text",value:a.general.name,onChange:r=>t("general","name",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Descrição"}),e.jsx("textarea",{value:a.general.description,onChange:r=>t("general","description",r.target.value),rows:3,className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Fuso Horário"}),e.jsxs("select",{value:a.general.timezone,onChange:r=>t("general","timezone",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"America/Sao_Paulo",children:"São Paulo (GMT-3)"}),e.jsx("option",{value:"America/New_York",children:"Nova York (GMT-5)"}),e.jsx("option",{value:"Europe/London",children:"Londres (GMT+0)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Idioma"}),e.jsxs("select",{value:a.general.language,onChange:r=>t("general","language",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"pt-BR",children:"Português (Brasil)"}),e.jsx("option",{value:"en-US",children:"English (US)"}),e.jsx("option",{value:"es-ES",children:"Español"})]})]})]})]}),f=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tema"}),e.jsxs("select",{value:a.interface.theme,onChange:r=>t("interface","theme",r.target.value),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"light",children:"Claro"}),e.jsx("option",{value:"dark",children:"Escuro"}),e.jsx("option",{value:"auto",children:"Automático"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Cor Principal"}),e.jsx("input",{type:"color",value:a.interface.primaryColor,onChange:r=>t("interface","primaryColor",r.target.value),className:"w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Posição das Notificações"}),e.jsxs("select",{value:u.notificationPosition||"top-left",onChange:r=>g({notificationPosition:r.target.value}),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[e.jsx("option",{value:"top-right",children:"Topo Direita"}),e.jsx("option",{value:"top-left",children:"Topo Esquerda"}),e.jsx("option",{value:"bottom-right",children:"Base Direita"}),e.jsx("option",{value:"bottom-left",children:"Base Esquerda"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Ajusta onde os avisos aparecem na tela do restaurante"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Permitir Sugestões"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Clientes podem sugerir músicas"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.interface.allowSuggestions,onChange:r=>t("interface","allowSuggestions",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Permitir Votação"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Clientes podem votar em sugestões"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.interface.allowVoting,onChange:r=>t("interface","allowVoting",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Mostrar Fila"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Exibir fila de reprodução para clientes"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.interface.showQueue,onChange:r=>t("interface","showQueue",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Sugestões por Cliente"}),e.jsx("input",{type:"number",min:"1",max:"20",value:a.interface.maxSuggestionsPerUser,onChange:r=>t("interface","maxSuggestionsPerUser",parseInt(r.target.value)),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),y=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Aprovação Automática"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Aprovar sugestões automaticamente com base em votos"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.moderation.autoApprove,onChange:r=>t("moderation","autoApprove",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Requer Moderação"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Todas as sugestões precisam ser moderadas"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:a.moderation.requireModeration,onChange:r=>t("moderation","requireModeration",r.target.checked),className:"sr-only peer"}),e.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Votos para Aprovação Automática"}),e.jsx("input",{type:"number",value:a.moderation.maxVotesForAutoApproval,onChange:r=>t("moderation","maxVotesForAutoApproval",parseInt(r.target.value)),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Votos para Rejeição Automática"}),e.jsx("input",{type:"number",value:a.moderation.minVotesForAutoRejection,onChange:r=>t("moderation","minVotesForAutoRejection",parseInt(r.target.value)),className:"w-full border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),j=()=>{switch(n){case"general":return b();case"interface":return f();case"moderation":return y();case"youtube":return k();default:return e.jsx("div",{className:"text-center py-8 text-gray-500",children:"Em desenvolvimento..."})}},k=()=>(console.log("🎵 Renderizando configurações do YouTube para restaurantId:",i),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Integração com YouTube"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-6",children:"Configure a autenticação com YouTube para controlar playlists em tempo real baseado nas votações dos clientes."})]}),e.jsx(S,{restaurantId:i,onAuthStatusChange:r=>{console.log("YouTube Auth Status:",r)}}),e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[e.jsx("h4",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2",children:"💡 Como funciona"}),e.jsxs("ul",{className:"text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• Conecte sua conta YouTube Premium"}),e.jsx("li",{children:"• Crie playlists controláveis pelo sistema"}),e.jsx("li",{children:"• As votações dos clientes reordenam automaticamente as músicas"}),e.jsx("li",{children:"• Controle total sobre a ordem de reprodução"})]})]})]}));return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Configurações do Restaurante"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Personalize a experiência do seu restaurante"})]}),e.jsxs("button",{onClick:h,disabled:o,className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-2",children:[e.jsx(A,{className:"w-4 h-4"}),e.jsx("span",{children:o?"Salvando...":"Salvar"})]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700",children:e.jsx("nav",{className:"flex space-x-8 px-6",children:p.map(r=>e.jsxs("button",{onClick:()=>m(r.id),className:`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors ${n===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"}`,children:[e.jsx(r.icon,{className:"w-4 h-4"}),e.jsx("span",{children:r.name})]},r.id))})}),e.jsx("div",{className:"p-6",children:j()})]})]})};export{U as default};
//# sourceMappingURL=RestaurantSettings-7f0630a1.js.map
