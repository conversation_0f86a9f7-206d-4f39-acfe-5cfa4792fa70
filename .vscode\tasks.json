{"version": "2.0.0", "tasks": [{"label": "docker-compose up backend (rebuild)", "type": "shell", "command": "docker compose build backend", "problemMatcher": ["$tsc"], "group": "build"}, {"label": "docker-compose up backend (run)", "type": "shell", "command": "docker compose up -d backend", "group": "build"}, {"label": "docker-compose up backend (force recreate)", "type": "shell", "command": "docker compose up -d --force-recreate backend", "group": "build"}, {"label": "test: one jest file", "type": "shell", "command": "npm run -w backend test -- PlaylistMembershipFilter.test.ts --runInBand", "problemMatcher": ["$tsc", "$eslint-stylish"], "group": "test"}]}