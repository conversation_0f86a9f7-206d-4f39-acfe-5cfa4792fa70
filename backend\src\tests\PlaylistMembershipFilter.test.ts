import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { DeepPartial, Repository } from 'typeorm';
import { Restaurant, RestaurantStatus } from '../models/Restaurant';
import { Playlist, PlaylistStatus, PlaylistType } from '../models/Playlist';
import { Suggestion, SuggestionStatus } from '../models/Suggestion';
import PlaybackQueueService from '../services/PlaybackQueueService';
import { CollaborativePlaylistService } from '../services/CollaborativePlaylistService';

// Redireciona AppDataSource para o TestDataSource nos serviços
jest.mock('../config/database', () => ({
  AppDataSource: {
    getRepository: (entity: any) => {
      const { TestDataSource } = require('../config/test-database');
      return TestDataSource.getRepository(entity);
    }
  }
}));

// Mocka YouTubeService para não chamar API externa
jest.mock('../services/YouTubeService', () => ({
  YouTubeService: jest.fn().mockImplementation(() => ({
    getVideoInfo: jest.fn().mockResolvedValue({
      title: 'Titulo Teste',
      artist: 'Artista Teste',
      duration: 180,
      thumbnailUrl: 'https://img.youtube.com/vi/test/mqdefault.jpg'
    })
  }))
}));

// Evitar ruído de WS
jest.mock('../services/WebSocketService', () => ({
  WebSocketService: {
    getInstance: () => ({
      emitToRestaurant: jest.fn(),
      emitToAdmins: jest.fn(),
    })
  }
}));

// Redis: usar stub simples
jest.mock('../config/redis', () => ({
  redisClient: {
    isReady: false,
    getClient: () => ({
      setEx: jest.fn(),
      exists: jest.fn().mockResolvedValue(0),
      mGet: jest.fn().mockResolvedValue([]),
      get: jest.fn().mockResolvedValue('0'),
      incr: jest.fn(),
    })
  }
}));

const VID_IN = 'AAA11111111';
const VID_OUT = 'BBB22222222';

describe('Filtro de pertença à playlist ativa', () => {
  let restaurantRepo: Repository<Restaurant>;
  let playlistRepo: Repository<Playlist>;
  let suggestionRepo: Repository<Suggestion>;
  let restaurant: Restaurant;
  let playlist: Playlist;

  beforeAll(async () => {
    await initializeTestDatabase();
    restaurantRepo = TestDataSource.getRepository(Restaurant);
    playlistRepo = TestDataSource.getRepository(Playlist);
    suggestionRepo = TestDataSource.getRepository(Suggestion);
  });

  afterAll(async () => {
    await closeTestDatabase();
  });

  beforeEach(async () => {
    await cleanTestData();

    restaurant = restaurantRepo.create({
      id: 'rest-' + Date.now(),
      name: 'R1',
      email: '<EMAIL>',
      status: RestaurantStatus.ACTIVE,
      isActive: true,
      settings: {},
    });
    await restaurantRepo.save(restaurant);

    playlist = playlistRepo.create({
      name: 'P1',
      description: 'desc',
      type: PlaylistType.SUGGESTIONS,
      status: PlaylistStatus.ACTIVE,
      youtubePlaylistId: 'yt-pl',
      restaurant,
      isPublic: true,
      isDefault: true,
      trackCount: 1,
      totalDuration: 180,
      tracks: [
        {
          youtubeVideoId: VID_IN,
          title: 'Track in',
          artist: 'Artist',
          duration: 180,
          thumbnailUrl: 'thumb',
          addedAt: new Date().toISOString(),
          position: 0,
        },
      ],
      settings: { allowSuggestions: true },
    });
    await playlistRepo.save(playlist);
  });

  it('getPlaybackQueue inclui apenas sugestões presentes na playlist ativa', async () => {
  const sIn = suggestionRepo.create({
      youtubeVideoId: VID_IN,
      title: 'ok',
      artist: 'a',
      duration: 180,
      restaurant,
      status: SuggestionStatus.APPROVED,
      createdAt: new Date(),
      updatedAt: new Date(),
      isPaid: false,
  } as DeepPartial<Suggestion>);
  const sOut = suggestionRepo.create({
      youtubeVideoId: VID_OUT,
      title: 'no',
      artist: 'a',
      duration: 180,
      restaurant,
      status: SuggestionStatus.APPROVED,
      createdAt: new Date(),
      updatedAt: new Date(),
      isPaid: false,
  } as DeepPartial<Suggestion>);
    await suggestionRepo.save([sIn, sOut]);

    const qsvc = new PlaybackQueueService();
    const { queue } = await qsvc.getPlaybackQueue(restaurant.id);
    expect(queue.map(q => q.youtubeVideoId)).toEqual([VID_IN]);
  });

  it('processNormalVote bloqueia votos para vídeos fora da playlist ativa', async () => {
    const csvc = new CollaborativePlaylistService();
    const res = await csvc.processNormalVote(restaurant.id, VID_OUT, 1, 'sess');
    expect(res.success).toBe(false);
    expect(res.message).toMatch(/não faz parte da playlist/i);
  });

  it('após remover track da playlist, ela desaparece da fila', async () => {
    // Criar sugestão aprovada para a track válida
  const sIn = suggestionRepo.create({
      youtubeVideoId: VID_IN,
      title: 'ok',
      artist: 'a',
      duration: 180,
      restaurant,
      status: SuggestionStatus.APPROVED,
      createdAt: new Date(),
      updatedAt: new Date(),
      isPaid: false,
  } as DeepPartial<Suggestion>);
    await suggestionRepo.save(sIn);

    const qsvc = new PlaybackQueueService();
    let q1 = await qsvc.getPlaybackQueue(restaurant.id);
    expect(q1.queue.length).toBe(1);

    // Remover a track da playlist ativa
    const pl = await playlistRepo.findOne({ where: { id: playlist.id } });
    expect(pl).toBeTruthy();
    pl!.tracks = [];
    pl!.trackCount = 0;
    await playlistRepo.save(pl!);

    // Buscar fila novamente -> deve estar vazia
    let q2 = await qsvc.getPlaybackQueue(restaurant.id);
    expect(q2.queue.length).toBe(0);
  });
});
