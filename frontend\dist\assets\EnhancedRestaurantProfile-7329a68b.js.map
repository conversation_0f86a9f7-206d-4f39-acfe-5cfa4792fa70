{"version": 3, "file": "EnhancedRestaurantProfile-7329a68b.js", "sources": ["../../src/components/restaurant/BusinessHoursManager.tsx", "../../src/components/restaurant/EnhancedRestaurantProfile.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, memo } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  Clock,\r\n  Calendar,\r\n  Plus,\r\n  Edit3,\r\n  Trash2,\r\n  Save,\r\n  X,\r\n  AlertCircle,\r\n  Copy,\r\n  RotateCcw,\r\n  Globe,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport apiService from \"@/services/api\";\r\n\r\nenum DayOfWeek {\r\n  Monday = \"monday\",\r\n  Tuesday = \"tuesday\",\r\n  Wednesday = \"wednesday\",\r\n  Thursday = \"thursday\",\r\n  Friday = \"friday\",\r\n  Saturday = \"saturday\",\r\n  Sunday = \"sunday\",\r\n}\r\n\r\ninterface BusinessHours {\r\n  [key: string]: {\r\n    open: string;\r\n    close: string;\r\n    isOpen: boolean;\r\n  };\r\n}\r\n\r\ninterface SpecialHours {\r\n  id: string;\r\n  date: string;\r\n  name: string;\r\n  hours: {\r\n    open: string;\r\n    close: string;\r\n    isOpen: boolean;\r\n  };\r\n  description?: string;\r\n}\r\n\r\ninterface BusinessHoursManagerProps {\r\n  restaurantId: string;\r\n  initialHours?: BusinessHours;\r\n  onSave?: (hours: BusinessHours, specialHours: SpecialHours[]) => void;\r\n}\r\n\r\nconst BusinessHoursManager: React.FC<BusinessHoursManagerProps> = memo(\r\n  ({ restaurantId, initialHours, onSave }) => {\r\n    const [businessHours, setBusinessHours] = useState<BusinessHours>({\r\n      [DayOfWeek.Monday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n      [DayOfWeek.Tuesday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n      [DayOfWeek.Wednesday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n      [DayOfWeek.Thursday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n      [DayOfWeek.Friday]: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n      [DayOfWeek.Saturday]: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n      [DayOfWeek.Sunday]: { open: \"11:00\", close: \"22:00\", isOpen: true },\r\n    });\r\n    const [specialHours, setSpecialHours] = useState<SpecialHours[]>([]);\r\n    const [timezone, setTimezone] = useState(\"America/Sao_Paulo\");\r\n    const [isEditing, setIsEditing] = useState(false);\r\n    const [loading, setLoading] = useState(false);\r\n    const [status, setStatus] = useState<{ isOpen: boolean; message: string }>({\r\n      isOpen: false,\r\n      message: \"Carregando...\",\r\n    });\r\n\r\n    const daysOfWeek = [\r\n      { key: DayOfWeek.Monday, label: \"Segunda-feira\", short: \"Seg\" },\r\n      { key: DayOfWeek.Tuesday, label: \"Terça-feira\", short: \"Ter\" },\r\n      { key: DayOfWeek.Wednesday, label: \"Quarta-feira\", short: \"Qua\" },\r\n      { key: DayOfWeek.Thursday, label: \"Quinta-feira\", short: \"Qui\" },\r\n      { key: DayOfWeek.Friday, label: \"Sexta-feira\", short: \"Sex\" },\r\n      { key: DayOfWeek.Saturday, label: \"Sábado\", short: \"Sáb\" },\r\n      { key: DayOfWeek.Sunday, label: \"Domingo\", short: \"Dom\" },\r\n    ];\r\n\r\n    const timezones = [\r\n      { value: \"America/Sao_Paulo\", label: \"São Paulo (GMT-3)\" },\r\n      { value: \"America/New_York\", label: \"Nova York (GMT-5)\" },\r\n      { value: \"Europe/London\", label: \"Londres (GMT+0)\" },\r\n      { value: \"Asia/Tokyo\", label: \"Tóquio (GMT+9)\" },\r\n      { value: \"Australia/Sydney\", label: \"Sydney (GMT+10)\" },\r\n    ];\r\n\r\n    const loadBusinessHours = useCallback(async () => {\r\n      try {\r\n        setLoading(true);\r\n        const [hoursResponse, statusResponse] = await Promise.all([\r\n          apiService.client.get(`/business-hours/${restaurantId}`),\r\n          apiService.client.get(`/business-hours/${restaurantId}/status`),\r\n        ]);\r\n\r\n        if (hoursResponse.data.hours?.regular) {\r\n          const hoursObj: BusinessHours = {};\r\n          hoursResponse.data.hours.regular.forEach((day: any) => {\r\n            hoursObj[day.day as keyof BusinessHours] = {\r\n              open: day.open,\r\n              close: day.close,\r\n              isOpen: day.isOpen,\r\n            };\r\n          });\r\n          setBusinessHours(hoursObj);\r\n        }\r\n\r\n        if (hoursResponse.data.hours?.special) {\r\n          setSpecialHours(\r\n            hoursResponse.data.hours.special.map((sh: any) => ({\r\n              ...sh,\r\n              date: new Date(sh.date).toISOString().split(\"T\")[0],\r\n            }))\r\n          );\r\n        }\r\n\r\n        if (hoursResponse.data.hours?.timezone) {\r\n          setTimezone(hoursResponse.data.hours.timezone);\r\n        }\r\n\r\n        if (statusResponse.data.status) {\r\n          setStatus(statusResponse.data.status);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Erro ao carregar horários:\", error);\r\n        toast.error(\"Erro ao carregar horários de funcionamento\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }, [restaurantId]);\r\n\r\n    useEffect(() => {\r\n      if (initialHours) {\r\n        setBusinessHours(initialHours);\r\n      }\r\n      loadBusinessHours();\r\n    }, [initialHours, loadBusinessHours]);\r\n\r\n    const updateBusinessHours = useCallback(\r\n      (day: string, field: \"open\" | \"close\" | \"isOpen\", value: string | boolean) => {\r\n        setBusinessHours((prev) => ({\r\n          ...prev,\r\n          [day]: { ...prev[day], [field]: value },\r\n        }));\r\n      },\r\n      []\r\n    );\r\n\r\n    const copyHours = useCallback(\r\n      (fromDay: string, toDay: string) => {\r\n        setBusinessHours((prev) => ({\r\n          ...prev,\r\n          [toDay]: { ...prev[fromDay] },\r\n        }));\r\n        toast.success(\r\n          `Horários de ${daysOfWeek.find((d) => d.key === fromDay)?.label} copiados para ${\r\n            daysOfWeek.find((d) => d.key === toDay)?.label\r\n          }`\r\n        );\r\n      },\r\n      []\r\n    );\r\n\r\n    const applyToAllDays = useCallback(\r\n      (day: string) => {\r\n        const sourceHours = businessHours[day];\r\n        const newHours = { ...businessHours };\r\n        daysOfWeek.forEach((d) => {\r\n          if (d.key !== day) {\r\n            newHours[d.key] = { ...sourceHours };\r\n          }\r\n        });\r\n        setBusinessHours(newHours);\r\n        toast.success(\"Horários aplicados a todos os dias\");\r\n      },\r\n      [businessHours]\r\n    );\r\n\r\n    const resetToDefault = useCallback(() => {\r\n      setBusinessHours({\r\n        [DayOfWeek.Monday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n        [DayOfWeek.Tuesday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n        [DayOfWeek.Wednesday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n        [DayOfWeek.Thursday]: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n        [DayOfWeek.Friday]: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n        [DayOfWeek.Saturday]: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n        [DayOfWeek.Sunday]: { open: \"11:00\", close: \"22:00\", isOpen: true },\r\n      });\r\n      toast.success(\"Horários resetados para padrão\");\r\n    }, []);\r\n\r\n    const addSpecialHours = useCallback(() => {\r\n      const newSpecialHours: SpecialHours = {\r\n        id: crypto.randomUUID(),\r\n        date: new Date().toISOString().split(\"T\")[0],\r\n        name: \"Horário Especial\",\r\n        hours: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n        description: \"\",\r\n      };\r\n      setSpecialHours((prev) => [...prev, newSpecialHours]);\r\n      toast.success(\"Novo horário especial adicionado\");\r\n    }, []);\r\n\r\n    const updateSpecialHours = useCallback((id: string, field: keyof SpecialHours, value: any) => {\r\n      setSpecialHours((prev) =>\r\n        prev.map((sh) => (sh.id === id ? { ...sh, [field]: value } : sh))\r\n      );\r\n    }, []);\r\n\r\n    const removeSpecialHours = useCallback((id: string) => {\r\n      setSpecialHours((prev) => prev.filter((sh) => sh.id !== id));\r\n      toast.success(\"Horário especial removido\");\r\n    }, []);\r\n\r\n    const saveBusinessHours = useCallback(async () => {\r\n      setLoading(true);\r\n      try {\r\n        const response = await apiService.client.put(`/business-hours/${restaurantId}`, {\r\n          businessHours,\r\n          specialHours,\r\n          timezone,\r\n        });\r\n\r\n        toast.success(\"Horários salvos com sucesso!\");\r\n        setIsEditing(false);\r\n        if (onSave) {\r\n          onSave(businessHours, specialHours);\r\n        }\r\n        await loadBusinessHours();\r\n      } catch (error) {\r\n        console.error(\"Erro ao salvar:\", error);\r\n        toast.error(\"Erro ao salvar horários\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }, [businessHours, specialHours, timezone, restaurantId, onSave, loadBusinessHours]);\r\n\r\n    const getCurrentStatus = useCallback(() => {\r\n      const now = new Date();\r\n      const currentDay = now.toLocaleDateString(\"en-US\", { weekday: \"long\" }).toLowerCase();\r\n      const currentTime = now.toTimeString().slice(0, 5);\r\n\r\n      const specialHour = specialHours.find(\r\n        (sh) => sh.date === now.toISOString().split(\"T\")[0]\r\n      );\r\n\r\n      if (specialHour) {\r\n        if (!specialHour.hours.isOpen) {\r\n          return { isOpen: false, message: `Fechado - ${specialHour.name}` };\r\n        }\r\n        const isOpen = currentTime >= specialHour.hours.open && currentTime <= specialHour.hours.close;\r\n        return {\r\n          isOpen,\r\n          message: isOpen ? `Aberto até ${specialHour.hours.close} (${specialHour.name})` : `Fechado - Abre às ${specialHour.hours.open} (${specialHour.name})`,\r\n        };\r\n      }\r\n\r\n      const todayHours = businessHours[currentDay];\r\n      if (!todayHours?.isOpen) {\r\n        return { isOpen: false, message: \"Fechado hoje\" };\r\n      }\r\n\r\n      const isCurrentlyOpen = currentTime >= todayHours.open && currentTime <= todayHours.close;\r\n      return {\r\n        isOpen: isCurrentlyOpen,\r\n        message: isCurrentlyOpen ? `Aberto até ${todayHours.close}` : `Fechado - Abre às ${todayHours.open}`,\r\n      };\r\n    }, [businessHours, specialHours]);\r\n\r\n    useEffect(() => {\r\n      setStatus(getCurrentStatus());\r\n    }, [businessHours, specialHours, getCurrentStatus]);\r\n\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <div className=\"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg\">\r\n              <Clock className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n            <div>\r\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">Horários de Funcionamento</h2>\r\n              <div className=\"flex items-center space-x-2 mt-1\">\r\n                <div className={`w-2 h-2 rounded-full ${status.isOpen ? \"bg-green-500\" : \"bg-red-500\"}`} />\r\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">{status.message}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            {!isEditing ? (\r\n              <button\r\n                onClick={() => setIsEditing(true)}\r\n                onKeyDown={(e) => e.key === \"Enter\" && setIsEditing(true)}\r\n                className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                aria-label=\"Editar horários\"\r\n              >\r\n                <Edit3 className=\"w-4 h-4\" />\r\n                <span>Editar</span>\r\n              </button>\r\n            ) : (\r\n              <>\r\n                <button\r\n                  onClick={() => setIsEditing(false)}\r\n                  onKeyDown={(e) => e.key === \"Enter\" && setIsEditing(false)}\r\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\"\r\n                  aria-label=\"Cancelar edição\"\r\n                >\r\n                  <X className=\"w-4 h-4\" />\r\n                  <span>Cancelar</span>\r\n                </button>\r\n                <button\r\n                  onClick={saveBusinessHours}\r\n                  disabled={loading}\r\n                  className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\"\r\n                  aria-label=\"Salvar horários\"\r\n                >\r\n                  <Save className=\"w-4 h-4\" />\r\n                  <span>{loading ? \"Salvando...\" : \"Salvar\"}</span>\r\n                </button>\r\n              </>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Timezone Selection */}\r\n        {isEditing && (\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center space-x-3 mb-3\">\r\n              <Globe className=\"w-5 h-5 text-gray-600 dark:text-gray-400\" />\r\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">Fuso Horário</h3>\r\n            </div>\r\n            <select\r\n              value={timezone}\r\n              onChange={(e) => setTimezone(e.target.value)}\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n              aria-label=\"Selecionar fuso horário\"\r\n            >\r\n              {timezones.map((tz) => (\r\n                <option key={tz.value} value={tz.value}>\r\n                  {tz.label}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n        )}\r\n\r\n        {/* Business Hours */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"font-medium text-gray-900 dark:text-white\">Horários Regulares</h3>\r\n            {isEditing && (\r\n              <div className=\"flex items-center space-x-2\">\r\n                <button\r\n                  onClick={resetToDefault}\r\n                  onKeyDown={(e) => e.key === \"Enter\" && resetToDefault()}\r\n                  className=\"flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\r\n                  aria-label=\"Resetar horários\"\r\n                >\r\n                  <RotateCcw className=\"w-3 h-3\" />\r\n                  <span>Resetar</span>\r\n                </button>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"space-y-3\">\r\n            {daysOfWeek.map((day) => (\r\n              <motion.div\r\n                key={day.key}\r\n                initial={{ opacity: 0, y: 10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.3 }}\r\n                className=\"flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg\"\r\n              >\r\n                <div className=\"w-24\">\r\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{day.short}</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={businessHours[day.key]?.isOpen || false}\r\n                    onChange={(e) => updateBusinessHours(day.key, \"isOpen\", e.target.checked)}\r\n                    disabled={!isEditing}\r\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                    aria-label={`Aberto na ${day.label}`}\r\n                  />\r\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Aberto</span>\r\n                </div>\r\n                {businessHours[day.key]?.isOpen && (\r\n                  <div className=\"flex items-center space-x-2 flex-1\">\r\n                    <input\r\n                      type=\"time\"\r\n                      value={businessHours[day.key]?.open || \"\"}\r\n                      onChange={(e) => updateBusinessHours(day.key, \"open\", e.target.value)}\r\n                      disabled={!isEditing}\r\n                      className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      aria-label={`Horário de abertura na ${day.label}`}\r\n                    />\r\n                    <span className=\"text-gray-500 text-sm\">às</span>\r\n                    <input\r\n                      type=\"time\"\r\n                      value={businessHours[day.key]?.close || \"\"}\r\n                      onChange={(e) => updateBusinessHours(day.key, \"close\", e.target.value)}\r\n                      disabled={!isEditing}\r\n                      className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      aria-label={`Horário de fechamento na ${day.label}`}\r\n                    />\r\n                  </div>\r\n                )}\r\n                {isEditing && (\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <button\r\n                      onClick={() => applyToAllDays(day.key)}\r\n                      onKeyDown={(e) => e.key === \"Enter\" && applyToAllDays(day.key)}\r\n                      className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\r\n                      title=\"Aplicar a todos os dias\"\r\n                      aria-label={`Aplicar horários de ${day.label} a todos os dias`}\r\n                    >\r\n                      <Copy className=\"w-4 h-4\" />\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Special Hours */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h3 className=\"font-medium text-gray-900 dark:text-white\">Horários Especiais</h3>\r\n            {isEditing && (\r\n              <button\r\n                onClick={addSpecialHours}\r\n                onKeyDown={(e) => e.key === \"Enter\" && addSpecialHours()}\r\n                className=\"flex items-center space-x-2 px-3 py-1 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\"\r\n                aria-label=\"Adicionar horário especial\"\r\n              >\r\n                <Plus className=\"w-4 h-4\" />\r\n                <span>Adicionar</span>\r\n              </button>\r\n            )}\r\n          </div>\r\n          {specialHours.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\r\n              <Calendar className=\"w-12 h-12 mx-auto mb-4 opacity-50\" />\r\n              <p>Nenhum horário especial configurado</p>\r\n              <p className=\"text-sm\">Adicione feriados ou eventos especiais</p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-3\">\r\n              {specialHours.map((special) => (\r\n                <motion.div\r\n                  key={special.id}\r\n                  initial={{ opacity: 0, y: 10 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.3 }}\r\n                  className=\"flex items-center space-x-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800\"\r\n                >\r\n                  <input\r\n                    type=\"date\"\r\n                    value={special.date}\r\n                    onChange={(e) => updateSpecialHours(special.id, \"date\", e.target.value)}\r\n                    disabled={!isEditing}\r\n                    className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    aria-label={`Data do horário especial ${special.name}`}\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    value={special.name}\r\n                    onChange={(e) => updateSpecialHours(special.id, \"name\", e.target.value)}\r\n                    disabled={!isEditing}\r\n                    placeholder=\"Nome do evento\"\r\n                    className=\"flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    aria-label={`Nome do horário especial`}\r\n                  />\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={special.hours.isOpen}\r\n                      onChange={(e) =>\r\n                        updateSpecialHours(special.id, \"hours\", {\r\n                          ...special.hours,\r\n                          isOpen: e.target.checked,\r\n                        })\r\n                      }\r\n                      disabled={!isEditing}\r\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                      aria-label={`Aberto para ${special.name}`}\r\n                    />\r\n                    {special.hours.isOpen && (\r\n                      <>\r\n                        <input\r\n                          type=\"time\"\r\n                          value={special.hours.open}\r\n                          onChange={(e) =>\r\n                            updateSpecialHours(special.id, \"hours\", {\r\n                              ...special.hours,\r\n                              open: e.target.value,\r\n                            })\r\n                          }\r\n                          disabled={!isEditing}\r\n                          className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                          aria-label={`Horário de abertura para ${special.name}`}\r\n                        />\r\n                        <span className=\"text-gray-500 text-sm\">às</span>\r\n                        <input\r\n                          type=\"time\"\r\n                          value={special.hours.close}\r\n                          onChange={(e) =>\r\n                            updateSpecialHours(special.id, \"hours\", {\r\n                              ...special.hours,\r\n                              close: e.target.value,\r\n                            })\r\n                          }\r\n                          disabled={!isEditing}\r\n                          className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                          aria-label={`Horário de fechamento para ${special.name}`}\r\n                        />\r\n                      </>\r\n                    )}\r\n                  </div>\r\n                  {isEditing && (\r\n                    <button\r\n                      onClick={() => removeSpecialHours(special.id)}\r\n                      onKeyDown={(e) => e.key === \"Enter\" && removeSpecialHours(special.id)}\r\n                      className=\"p-1 text-red-400 hover:text-red-600 transition-colors\"\r\n                      aria-label={`Remover horário especial ${special.name}`}\r\n                    >\r\n                      <Trash2 className=\"w-4 h-4\" />\r\n                    </button>\r\n                  )}\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Status Info */}\r\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n          <div className=\"flex items-start space-x-3\">\r\n            <AlertCircle className=\"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5\" />\r\n            <div>\r\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-1\">Informações Importantes</h4>\r\n              <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1\">\r\n                <li>• Os horários são aplicados no fuso horário selecionado</li>\r\n                <li>• Horários especiais têm prioridade sobre horários regulares</li>\r\n                <li>• Clientes verão uma mensagem quando o restaurante estiver fechado</li>\r\n                <li>• Use \"24:00\" para meia-noite do dia seguinte</li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nexport default BusinessHoursManager;\r\n", "import React, { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  User,\r\n  Mail,\r\n  Phone,\r\n  MapPin,\r\n  Building2,\r\n  Clock,\r\n  Save,\r\n  Edit,\r\n  X,\r\n  Check,\r\n  AlertCircle,\r\n  RefreshCw,\r\n  Settings,\r\n  Globe,\r\n  Shield,\r\n  Music,\r\n  Users,\r\n  Camera,\r\n  Upload,\r\n  Eye,\r\n  EyeOff,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport BusinessHoursManager from \"./BusinessHoursManager\";\r\n\r\ninterface RestaurantProfile {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  address: string;\r\n  description: string;\r\n  businessHours: {\r\n    [key: string]: {\r\n      open: string;\r\n      close: string;\r\n      isOpen: boolean;\r\n    };\r\n  };\r\n  settings: {\r\n    allowSuggestions: boolean;\r\n    maxSuggestionsPerUser: number;\r\n    autoPlayEnabled: boolean;\r\n    playlist?: any;\r\n  };\r\n  createdAt?: string;\r\n  updatedAt?: string;\r\n}\r\n\r\ninterface EnhancedRestaurantProfileProps {\r\n  restaurantId?: string;\r\n}\r\n\r\nconst EnhancedRestaurantProfile: React.FC<EnhancedRestaurantProfileProps> = ({\r\n  restaurantId = \"demo-restaurant\",\r\n}) => {\r\n  const [profile, setProfile] = useState<RestaurantProfile | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [saving, setSaving] = useState(false);\r\n  const [activeTab, setActiveTab] = useState(\"basic\");\r\n  const [editingField, setEditingField] = useState<string | null>(null);\r\n  const [tempValues, setTempValues] = useState<Record<string, any>>({});\r\n\r\n  const tabs = [\r\n    { id: \"basic\", label: \"Informações Básicas\", icon: Building2 },\r\n    { id: \"hours\", label: \"Horários\", icon: Clock },\r\n    { id: \"settings\", label: \"Configurações\", icon: Settings },\r\n    { id: \"advanced\", label: \"Avançado\", icon: Shield },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    loadProfile();\r\n  }, [restaurantId]);\r\n\r\n  const loadProfile = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await fetch(\r\n        `http://localhost:8001/api/v1/restaurants/${restaurantId}/profile`\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"Profile loaded:\", data);\r\n        setProfile(data.profile);\r\n      } else {\r\n        console.error(\"Failed to load profile, status:\", response.status);\r\n        const errorData = await response.json().catch(() => ({}));\r\n        console.error(\"Error details:\", errorData);\r\n\r\n        // Fallback para dados mock\r\n        const mockProfile: RestaurantProfile = {\r\n          id: restaurantId,\r\n          name: \"Restaurante Demo\",\r\n          email: \"<EMAIL>\",\r\n          phone: \"(11) 99999-9999\",\r\n          address: \"Rua das Flores, 123 - São Paulo, SP\",\r\n          description:\r\n            \"Um restaurante aconchegante com música ambiente personalizada pelos clientes.\",\r\n          businessHours: {\r\n            monday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n            tuesday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n            wednesday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n            thursday: { open: \"11:00\", close: \"23:00\", isOpen: true },\r\n            friday: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n            saturday: { open: \"11:00\", close: \"24:00\", isOpen: true },\r\n            sunday: { open: \"12:00\", close: \"22:00\", isOpen: true },\r\n          },\r\n          settings: {\r\n            allowSuggestions: true,\r\n            maxSuggestionsPerUser: 3,\r\n            autoPlayEnabled: true,\r\n          },\r\n        };\r\n        setProfile(mockProfile);\r\n        toast.error(\"Usando dados de demonstração\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar perfil:\", error);\r\n      toast.error(\"Erro de conexão ao carregar perfil\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const startEdit = (field: string, currentValue: any) => {\r\n    setEditingField(field);\r\n    setTempValues({ [field]: currentValue });\r\n  };\r\n\r\n  const cancelEdit = () => {\r\n    setEditingField(null);\r\n    setTempValues({});\r\n  };\r\n\r\n  const saveField = async (field: string) => {\r\n    if (!profile) return;\r\n\r\n    try {\r\n      setSaving(true);\r\n      const updateData = { [field]: tempValues[field] };\r\n\r\n      const response = await fetch(\r\n        `http://localhost:8001/api/v1/restaurants/${restaurantId}/profile`,\r\n        {\r\n          method: \"PUT\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify(updateData),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setProfile(data.profile || { ...profile, [field]: tempValues[field] });\r\n        setEditingField(null);\r\n        setTempValues({});\r\n        toast.success(\"Campo atualizado com sucesso!\");\r\n      } else {\r\n        const errorData = await response.json().catch(() => ({}));\r\n        toast.error(errorData.message || \"Erro ao salvar\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar campo:\", error);\r\n      toast.error(\"Erro de conexão\");\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const saveSettings = async (newSettings: any) => {\r\n    if (!profile) return;\r\n\r\n    try {\r\n      setSaving(true);\r\n      const response = await fetch(\r\n        `http://localhost:8001/api/v1/restaurants/${restaurantId}/profile`,\r\n        {\r\n          method: \"PUT\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ settings: newSettings }),\r\n        }\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setProfile(data.profile || { ...profile, settings: newSettings });\r\n        toast.success(\"Configurações salvas!\");\r\n      } else {\r\n        const errorData = await response.json().catch(() => ({}));\r\n        toast.error(errorData.message || \"Erro ao salvar configurações\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar configurações:\", error);\r\n      toast.error(\"Erro de conexão\");\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const EditableField: React.FC<{\r\n    field: string;\r\n    label: string;\r\n    value: string;\r\n    icon: React.ComponentType<any>;\r\n    type?: string;\r\n    multiline?: boolean;\r\n  }> = ({\r\n    field,\r\n    label,\r\n    value,\r\n    icon: Icon,\r\n    type = \"text\",\r\n    multiline = false,\r\n  }) => {\r\n    const isEditing = editingField === field;\r\n\r\n    return (\r\n      <div className=\"space-y-2\">\r\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n          <Icon className=\"w-4 h-4 inline mr-2\" />\r\n          {label}\r\n        </label>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          {isEditing ? (\r\n            <>\r\n              {multiline ? (\r\n                <textarea\r\n                  value={tempValues[field] || \"\"}\r\n                  onChange={(e) =>\r\n                    setTempValues({ ...tempValues, [field]: e.target.value })\r\n                  }\r\n                  className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  rows={3}\r\n                />\r\n              ) : (\r\n                <input\r\n                  type={type}\r\n                  value={tempValues[field] || \"\"}\r\n                  onChange={(e) =>\r\n                    setTempValues({ ...tempValues, [field]: e.target.value })\r\n                  }\r\n                  className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                />\r\n              )}\r\n\r\n              <button\r\n                onClick={() => saveField(field)}\r\n                disabled={saving}\r\n                className=\"p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\"\r\n              >\r\n                <Check className=\"w-4 h-4\" />\r\n              </button>\r\n\r\n              <button\r\n                onClick={cancelEdit}\r\n                className=\"p-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\"\r\n              >\r\n                <X className=\"w-4 h-4\" />\r\n              </button>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <div className=\"flex-1 px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white\">\r\n                {value || \"Não informado\"}\r\n              </div>\r\n\r\n              <button\r\n                onClick={() => startEdit(field, value)}\r\n                className=\"p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n              >\r\n                <Edit className=\"w-4 h-4\" />\r\n              </button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <RefreshCw className=\"w-6 h-6 animate-spin text-blue-600 mr-2\" />\r\n        <span className=\"text-gray-600 dark:text-gray-400\">\r\n          Carregando perfil...\r\n        </span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!profile) {\r\n    return (\r\n      <div className=\"text-center p-8\">\r\n        <AlertCircle className=\"w-12 h-12 text-red-500 mx-auto mb-4\" />\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n          Erro ao carregar perfil\r\n        </h3>\r\n        <button\r\n          onClick={loadProfile}\r\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n        >\r\n          Tentar novamente\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div className=\"p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg\">\r\n            <Building2 className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\r\n          </div>\r\n          <div>\r\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\r\n              Perfil do Restaurante\r\n            </h2>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Gerencie as informações do seu estabelecimento\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <button\r\n          onClick={loadProfile}\r\n          disabled={loading}\r\n          className=\"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50\"\r\n        >\r\n          <RefreshCw className={`w-4 h-4 ${loading ? \"animate-spin\" : \"\"}`} />\r\n          <span>Atualizar</span>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n        <nav className=\"flex space-x-8\">\r\n          {tabs.map((tab) => (\r\n            <button\r\n              key={tab.id}\r\n              onClick={() => setActiveTab(tab.id)}\r\n              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${\r\n                activeTab === tab.id\r\n                  ? \"border-blue-500 text-blue-600 dark:text-blue-400\"\r\n                  : \"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\r\n              }`}\r\n            >\r\n              <tab.icon className=\"w-4 h-4\" />\r\n              <span>{tab.label}</span>\r\n            </button>\r\n          ))}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <AnimatePresence mode=\"wait\">\r\n        <motion.div\r\n          key={activeTab}\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -10 }}\r\n          transition={{ duration: 0.2 }}\r\n        >\r\n          {activeTab === \"basic\" && (\r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\r\n                  Informações Básicas\r\n                </h3>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <EditableField\r\n                    field=\"name\"\r\n                    label=\"Nome do Restaurante\"\r\n                    value={profile.name}\r\n                    icon={Building2}\r\n                  />\r\n\r\n                  <EditableField\r\n                    field=\"email\"\r\n                    label=\"Email\"\r\n                    value={profile.email}\r\n                    icon={Mail}\r\n                    type=\"email\"\r\n                  />\r\n\r\n                  <EditableField\r\n                    field=\"phone\"\r\n                    label=\"Telefone\"\r\n                    value={profile.phone}\r\n                    icon={Phone}\r\n                    type=\"tel\"\r\n                  />\r\n\r\n                  <EditableField\r\n                    field=\"address\"\r\n                    label=\"Endereço\"\r\n                    value={profile.address}\r\n                    icon={MapPin}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"mt-6\">\r\n                  <EditableField\r\n                    field=\"description\"\r\n                    label=\"Descrição\"\r\n                    value={profile.description}\r\n                    icon={User}\r\n                    multiline\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === \"hours\" && (\r\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n              <BusinessHoursManager\r\n                restaurantId={restaurantId}\r\n                initialHours={profile.businessHours}\r\n                onSave={(hours) => {\r\n                  setProfile({ ...profile, businessHours: hours });\r\n                  toast.success(\"Horários atualizados!\");\r\n                }}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === \"settings\" && (\r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\r\n                  Configurações da Playlist\r\n                </h3>\r\n\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                        Permitir Sugestões\r\n                      </h4>\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                        Clientes podem sugerir músicas\r\n                      </p>\r\n                    </div>\r\n                    <button\r\n                      onClick={() =>\r\n                        saveSettings({\r\n                          ...profile.settings,\r\n                          allowSuggestions: !profile.settings.allowSuggestions,\r\n                        })\r\n                      }\r\n                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                        profile.settings.allowSuggestions\r\n                          ? \"bg-blue-600\"\r\n                          : \"bg-gray-200 dark:bg-gray-700\"\r\n                      }`}\r\n                    >\r\n                      <span\r\n                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                          profile.settings.allowSuggestions\r\n                            ? \"translate-x-6\"\r\n                            : \"translate-x-1\"\r\n                        }`}\r\n                      />\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div>\r\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\r\n                        Reprodução Automática\r\n                      </h4>\r\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                        Tocar próxima música automaticamente\r\n                      </p>\r\n                    </div>\r\n                    <button\r\n                      onClick={() =>\r\n                        saveSettings({\r\n                          ...profile.settings,\r\n                          autoPlayEnabled: !profile.settings.autoPlayEnabled,\r\n                        })\r\n                      }\r\n                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\r\n                        profile.settings.autoPlayEnabled\r\n                          ? \"bg-blue-600\"\r\n                          : \"bg-gray-200 dark:bg-gray-700\"\r\n                      }`}\r\n                    >\r\n                      <span\r\n                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\r\n                          profile.settings.autoPlayEnabled\r\n                            ? \"translate-x-6\"\r\n                            : \"translate-x-1\"\r\n                        }`}\r\n                      />\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                      Máximo de Sugestões por Cliente\r\n                    </label>\r\n                    <select\r\n                      value={profile.settings.maxSuggestionsPerUser}\r\n                      onChange={(e) =>\r\n                        saveSettings({\r\n                          ...profile.settings,\r\n                          maxSuggestionsPerUser: parseInt(e.target.value),\r\n                        })\r\n                      }\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                    >\r\n                      <option value={1}>1 sugestão</option>\r\n                      <option value={2}>2 sugestões</option>\r\n                      <option value={3}>3 sugestões</option>\r\n                      <option value={5}>5 sugestões</option>\r\n                      <option value={10}>10 sugestões</option>\r\n                      <option value={-1}>Ilimitado</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === \"advanced\" && (\r\n            <div className=\"space-y-6\">\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\r\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\r\n                  Configurações Avançadas\r\n                </h3>\r\n\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\r\n                      Informações do Sistema\r\n                    </h4>\r\n                    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-2\">\r\n                      <div className=\"flex justify-between\">\r\n                        <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                          ID:\r\n                        </span>\r\n                        <span className=\"text-sm font-mono text-gray-900 dark:text-white\">\r\n                          {profile.id}\r\n                        </span>\r\n                      </div>\r\n                      {profile.createdAt && (\r\n                        <div className=\"flex justify-between\">\r\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            Criado em:\r\n                          </span>\r\n                          <span className=\"text-sm text-gray-900 dark:text-white\">\r\n                            {new Date(profile.createdAt).toLocaleDateString(\r\n                              \"pt-BR\"\r\n                            )}\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                      {profile.updatedAt && (\r\n                        <div className=\"flex justify-between\">\r\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            Atualizado em:\r\n                          </span>\r\n                          <span className=\"text-sm text-gray-900 dark:text-white\">\r\n                            {new Date(profile.updatedAt).toLocaleDateString(\r\n                              \"pt-BR\"\r\n                            )}\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EnhancedRestaurantProfile;\r\n"], "names": ["BusinessHoursManager", "memo", "restaurantId", "initialHours", "onSave", "businessHours", "setBusinessHours", "useState", "specialHours", "setSpecialHours", "timezone", "setTimezone", "isEditing", "setIsEditing", "loading", "setLoading", "status", "setStatus", "daysOfWeek", "timezones", "loadBusinessHours", "useCallback", "hoursResponse", "statusResponse", "apiService", "_a", "hours<PERSON>bj", "day", "_b", "sh", "_c", "error", "toast", "useEffect", "updateBusinessHours", "field", "value", "prev", "fromDay", "toDay", "d", "applyToAllDays", "sourceHours", "newHours", "resetToDefault", "addSpecialHours", "newSpecialHours", "updateSpecialHours", "id", "removeSpecialHours", "saveBusinessHours", "response", "getCurrentStatus", "now", "currentDay", "currentTime", "specialHour", "isOpen", "todayHours", "isCurrentlyOpen", "jsxs", "jsx", "Clock", "Fragment", "e", "X", "Save", "Edit3", "Globe", "tz", "RotateCcw", "motion", "_d", "Copy", "Plus", "Calendar", "special", "Trash2", "AlertCircle", "EnhancedRestaurantProfile", "profile", "setProfile", "saving", "setSaving", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON>", "setEditingField", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "tabs", "Building2", "Settings", "Shield", "loadProfile", "data", "errorData", "startEdit", "currentValue", "cancelEdit", "saveField", "updateData", "saveSettings", "newSettings", "EditableField", "label", "Icon", "type", "multiline", "Check", "Edit", "RefreshCw", "tab", "AnimatePresence", "Mail", "Phone", "MapPin", "User", "hours"], "mappings": "oWAsDA,MAAMA,GAA4DC,EAAA,KAChE,CAAC,CAAE,aAAAC,EAAc,aAAAC,EAAc,OAAAC,KAAa,CAC1C,KAAM,CAACC,EAAeC,CAAgB,EAAIC,WAAwB,CAC/D,OAAmB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACjE,QAAoB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EAClE,UAAsB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACpE,SAAqB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACnE,OAAmB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACjE,SAAqB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACnE,OAAmB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,CAAA,CACnE,EACK,CAACC,EAAcC,CAAe,EAAIF,EAAA,SAAyB,CAAE,CAAA,EAC7D,CAACG,EAAUC,CAAW,EAAIJ,WAAS,mBAAmB,EACtD,CAACK,EAAWC,CAAY,EAAIN,WAAS,EAAK,EAC1C,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAK,EACtC,CAACS,EAAQC,CAAS,EAAIV,WAA+C,CACzE,OAAQ,GACR,QAAS,eAAA,CACV,EAEKW,EAAa,CACjB,CAAE,IAAK,SAAkB,MAAO,gBAAiB,MAAO,KAAM,EAC9D,CAAE,IAAK,UAAmB,MAAO,cAAe,MAAO,KAAM,EAC7D,CAAE,IAAK,YAAqB,MAAO,eAAgB,MAAO,KAAM,EAChE,CAAE,IAAK,WAAoB,MAAO,eAAgB,MAAO,KAAM,EAC/D,CAAE,IAAK,SAAkB,MAAO,cAAe,MAAO,KAAM,EAC5D,CAAE,IAAK,WAAoB,MAAO,SAAU,MAAO,KAAM,EACzD,CAAE,IAAK,SAAkB,MAAO,UAAW,MAAO,KAAM,CAAA,EAGpDC,EAAY,CAChB,CAAE,MAAO,oBAAqB,MAAO,mBAAoB,EACzD,CAAE,MAAO,mBAAoB,MAAO,mBAAoB,EACxD,CAAE,MAAO,gBAAiB,MAAO,iBAAkB,EACnD,CAAE,MAAO,aAAc,MAAO,gBAAiB,EAC/C,CAAE,MAAO,mBAAoB,MAAO,iBAAkB,CAAA,EAGlDC,EAAoBC,EAAAA,YAAY,SAAY,WAC5C,GAAA,CACFN,EAAW,EAAI,EACf,KAAM,CAACO,EAAeC,CAAc,EAAI,MAAM,QAAQ,IAAI,CACxDC,EAAW,OAAO,IAAI,mBAAmBtB,CAAY,EAAE,EACvDsB,EAAW,OAAO,IAAI,mBAAmBtB,CAAY,SAAS,CAAA,CAC/D,EAEG,IAAAuB,EAAAH,EAAc,KAAK,QAAnB,MAAAG,EAA0B,QAAS,CACrC,MAAMC,EAA0B,CAAA,EAChCJ,EAAc,KAAK,MAAM,QAAQ,QAASK,GAAa,CAC5CD,EAAAC,EAAI,GAA0B,EAAI,CACzC,KAAMA,EAAI,KACV,MAAOA,EAAI,MACX,OAAQA,EAAI,MAAA,CACd,CACD,EACDrB,EAAiBoB,CAAQ,CAC3B,EAEIE,EAAAN,EAAc,KAAK,QAAnB,MAAAM,EAA0B,SAC5BnB,EACEa,EAAc,KAAK,MAAM,QAAQ,IAAKO,IAAa,CACjD,GAAGA,EACH,KAAM,IAAI,KAAKA,EAAG,IAAI,EAAE,YAAA,EAAc,MAAM,GAAG,EAAE,CAAC,CAAA,EAClD,CAAA,GAIFC,EAAAR,EAAc,KAAK,QAAnB,MAAAQ,EAA0B,UAChBnB,EAAAW,EAAc,KAAK,MAAM,QAAQ,EAG3CC,EAAe,KAAK,QACZN,EAAAM,EAAe,KAAK,MAAM,QAE/BQ,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjDC,EAAM,MAAM,4CAA4C,CAAA,QACxD,CACAjB,EAAW,EAAK,CAClB,CAAA,EACC,CAACb,CAAY,CAAC,EAEjB+B,EAAAA,UAAU,IAAM,CACV9B,GACFG,EAAiBH,CAAY,EAEbiB,GAAA,EACjB,CAACjB,EAAciB,CAAiB,CAAC,EAEpC,MAAMc,EAAsBb,EAAA,YAC1B,CAACM,EAAaQ,EAAoCC,IAA4B,CAC5E9B,EAAkB+B,IAAU,CAC1B,GAAGA,EACH,CAACV,CAAG,EAAG,CAAE,GAAGU,EAAKV,CAAG,EAAG,CAACQ,CAAK,EAAGC,CAAM,CACtC,EAAA,CACJ,EACA,CAAC,CAAA,EAGef,EAAA,YAChB,CAACiB,EAAiBC,IAAkB,SAClCjC,EAAkB+B,IAAU,CAC1B,GAAGA,EACH,CAACE,CAAK,EAAG,CAAE,GAAGF,EAAKC,CAAO,CAAE,CAC5B,EAAA,EACIN,EAAA,QACJ,gBAAeP,EAAAP,EAAW,KAAMsB,GAAMA,EAAE,MAAQF,CAAO,IAAxC,YAAAb,EAA2C,KAAK,mBAC7DG,EAAAV,EAAW,KAAMsB,GAAMA,EAAE,MAAQD,CAAK,IAAtC,YAAAX,EAAyC,KAC3C,EAAA,CAEJ,EACA,CAAC,CACH,EAEA,MAAMa,EAAiBpB,EAAA,YACpBM,GAAgB,CACT,MAAAe,EAAcrC,EAAcsB,CAAG,EAC/BgB,EAAW,CAAE,GAAGtC,GACXa,EAAA,QAASsB,GAAM,CACpBA,EAAE,MAAQb,IACZgB,EAASH,EAAE,GAAG,EAAI,CAAE,GAAGE,CAAY,EACrC,CACD,EACDpC,EAAiBqC,CAAQ,EACzBX,EAAM,QAAQ,oCAAoC,CACpD,EACA,CAAC3B,CAAa,CAAA,EAGVuC,EAAiBvB,EAAAA,YAAY,IAAM,CACtBf,EAAA,CACd,OAAmB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACjE,QAAoB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EAClE,UAAsB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACpE,SAAqB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACnE,OAAmB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACjE,SAAqB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACnE,OAAmB,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,CAAA,CACnE,EACD0B,EAAM,QAAQ,gCAAgC,CAChD,EAAG,CAAE,CAAA,EAECa,EAAkBxB,EAAAA,YAAY,IAAM,CACxC,MAAMyB,EAAgC,CACpC,GAAI,OAAO,WAAW,EACtB,SAAU,OAAO,YAAc,EAAA,MAAM,GAAG,EAAE,CAAC,EAC3C,KAAM,mBACN,MAAO,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACrD,YAAa,EAAA,EAEfrC,EAAiB4B,GAAS,CAAC,GAAGA,EAAMS,CAAe,CAAC,EACpDd,EAAM,QAAQ,kCAAkC,CAClD,EAAG,CAAE,CAAA,EAECe,EAAqB1B,EAAA,YAAY,CAAC2B,EAAYb,EAA2BC,IAAe,CAC5F3B,EAAiB4B,GACfA,EAAK,IAAKR,GAAQA,EAAG,KAAOmB,EAAK,CAAE,GAAGnB,EAAI,CAACM,CAAK,EAAGC,CAAA,EAAUP,CAAG,CAAA,CAEpE,EAAG,CAAE,CAAA,EAECoB,EAAqB5B,cAAa2B,GAAe,CACrCvC,EAAC4B,GAASA,EAAK,OAAQR,GAAOA,EAAG,KAAOmB,CAAE,CAAC,EAC3DhB,EAAM,QAAQ,2BAA2B,CAC3C,EAAG,CAAE,CAAA,EAECkB,EAAoB7B,EAAAA,YAAY,SAAY,CAChDN,EAAW,EAAI,EACX,GAAA,CACF,MAAMoC,EAAW,MAAM3B,EAAW,OAAO,IAAI,mBAAmBtB,CAAY,GAAI,CAC9E,cAAAG,EACA,aAAAG,EACA,SAAAE,CAAA,CACD,EAEDsB,EAAM,QAAQ,8BAA8B,EAC5CnB,EAAa,EAAK,EACdT,GACFA,EAAOC,EAAeG,CAAY,EAEpC,MAAMY,EAAkB,QACjBW,EAAO,CACN,QAAA,MAAM,kBAAmBA,CAAK,EACtCC,EAAM,MAAM,yBAAyB,CAAA,QACrC,CACAjB,EAAW,EAAK,CAClB,CAAA,EACC,CAACV,EAAeG,EAAcE,EAAUR,EAAcE,EAAQgB,CAAiB,CAAC,EAE7EgC,EAAmB/B,EAAAA,YAAY,IAAM,CACnC,MAAAgC,MAAU,KACVC,EAAaD,EAAI,mBAAmB,QAAS,CAAE,QAAS,MAAA,CAAQ,EAAE,cAClEE,EAAcF,EAAI,aAAA,EAAe,MAAM,EAAG,CAAC,EAE3CG,EAAchD,EAAa,KAC9BqB,GAAOA,EAAG,OAASwB,EAAI,cAAc,MAAM,GAAG,EAAE,CAAC,CAAA,EAGpD,GAAIG,EAAa,CACX,GAAA,CAACA,EAAY,MAAM,OACrB,MAAO,CAAE,OAAQ,GAAO,QAAS,aAAaA,EAAY,IAAI,IAEhE,MAAMC,EAASF,GAAeC,EAAY,MAAM,MAAQD,GAAeC,EAAY,MAAM,MAClF,MAAA,CACL,OAAAC,EACA,QAASA,EAAS,cAAcD,EAAY,MAAM,KAAK,KAAKA,EAAY,IAAI,IAAM,qBAAqBA,EAAY,MAAM,IAAI,KAAKA,EAAY,IAAI,GAAA,CAEtJ,CAEM,MAAAE,EAAarD,EAAciD,CAAU,EACvC,GAAA,EAACI,GAAA,MAAAA,EAAY,QACf,MAAO,CAAE,OAAQ,GAAO,QAAS,cAAe,EAGlD,MAAMC,EAAkBJ,GAAeG,EAAW,MAAQH,GAAeG,EAAW,MAC7E,MAAA,CACL,OAAQC,EACR,QAASA,EAAkB,cAAcD,EAAW,KAAK,GAAK,qBAAqBA,EAAW,IAAI,EAAA,CACpG,EACC,CAACrD,EAAeG,CAAY,CAAC,EAEhCyB,OAAAA,EAAAA,UAAU,IAAM,CACdhB,EAAUmC,GAAkB,CAC3B,EAAA,CAAC/C,EAAeG,EAAc4C,CAAgB,CAAC,EAGhDQ,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,iDACb,eAACC,EAAM,CAAA,UAAU,2CAA2C,CAC9D,CAAA,SACC,MACC,CAAA,SAAA,CAACD,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAyB,4BAAA,EAC7FD,EAAAA,KAAC,MAAI,CAAA,UAAU,mCACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAW,wBAAwB7C,EAAO,OAAS,eAAiB,YAAY,GAAI,EACxF6C,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA4C,WAAO,QAAQ,CAAA,EAC7E,CAAA,EACF,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,8BACZ,SAACjD,EAYEgD,EAAA,KAAAG,WAAA,CAAA,SAAA,CAAAH,EAAA,KAAC,SAAA,CACC,QAAS,IAAM/C,EAAa,EAAK,EACjC,UAAYmD,GAAMA,EAAE,MAAQ,SAAWnD,EAAa,EAAK,EACzD,UAAU,8GACV,aAAW,kBAEX,SAAA,CAACgD,EAAAA,IAAAI,EAAA,CAAE,UAAU,SAAU,CAAA,EACvBJ,EAAAA,IAAC,QAAK,SAAQ,UAAA,CAAA,CAAA,CAAA,CAChB,EACAD,EAAA,KAAC,SAAA,CACC,QAASV,EACT,SAAUpC,EACV,UAAU,oIACV,aAAW,kBAEX,SAAA,CAAC+C,EAAAA,IAAAK,EAAA,CAAK,UAAU,SAAU,CAAA,EACzBL,EAAA,IAAA,OAAA,CAAM,SAAU/C,EAAA,cAAgB,SAAS,CAAA,CAAA,CAC5C,CAAA,CAAA,CACF,EA7BA8C,EAAA,KAAC,SAAA,CACC,QAAS,IAAM/C,EAAa,EAAI,EAChC,UAAYmD,GAAMA,EAAE,MAAQ,SAAWnD,EAAa,EAAI,EACxD,UAAU,8GACV,aAAW,kBAEX,SAAA,CAACgD,EAAAA,IAAAM,EAAA,CAAM,UAAU,SAAU,CAAA,EAC3BN,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CAAA,CAwBlB,CAAA,CAAA,EACF,EAGCjD,GACCgD,EAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACC,EAAAA,IAAAO,EAAA,CAAM,UAAU,0CAA2C,CAAA,EAC3DP,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAAY,eAAA,CAAA,EACxE,EACAA,EAAA,IAAC,SAAA,CACC,MAAOnD,EACP,SAAWsD,GAAMrD,EAAYqD,EAAE,OAAO,KAAK,EAC3C,UAAU,4LACV,aAAW,0BAEV,SAAU7C,EAAA,IAAKkD,GACbR,EAAAA,IAAA,SAAA,CAAsB,MAAOQ,EAAG,MAC9B,SAAAA,EAAG,KADO,EAAAA,EAAG,KAEhB,CACD,CAAA,CACH,CAAA,EACF,EAIFT,EAAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAAkB,qBAAA,EAC3EjD,GACCiD,EAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAD,EAAA,KAAC,SAAA,CACC,QAAShB,EACT,UAAYoB,GAAMA,EAAE,MAAQ,SAAWpB,EAAe,EACtD,UAAU,oLACV,aAAW,mBAEX,SAAA,CAACiB,EAAAA,IAAAS,EAAA,CAAU,UAAU,SAAU,CAAA,EAC/BT,EAAAA,IAAC,QAAK,SAAO,SAAA,CAAA,CAAA,CAAA,CAAA,EAEjB,CAAA,EAEJ,QACC,MAAI,CAAA,UAAU,YACZ,SAAW3C,EAAA,IAAKS,gBACfiC,OAAAA,EAAA,KAACW,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,4EAEV,SAAA,CAACV,EAAA,IAAA,MAAA,CAAI,UAAU,OACb,SAAAA,EAAAA,IAAC,QAAK,UAAU,uDAAwD,SAAIlC,EAAA,KAAA,CAAM,CACpF,CAAA,EACAiC,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAASpC,EAAApB,EAAcsB,EAAI,GAAG,IAArB,YAAAF,EAAwB,SAAU,GAC3C,SAAWuC,GAAM9B,EAAoBP,EAAI,IAAK,SAAUqC,EAAE,OAAO,OAAO,EACxE,SAAU,CAACpD,EACX,UAAU,4DACV,aAAY,aAAae,EAAI,KAAK,EAAA,CACpC,EACCkC,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAAM,SAAA,CAAA,EACnE,IACCjC,EAAAvB,EAAcsB,EAAI,GAAG,IAArB,YAAAC,EAAwB,SACtBgC,OAAA,MAAA,CAAI,UAAU,qCACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAO/B,EAAAzB,EAAcsB,EAAI,GAAG,IAArB,YAAAG,EAAwB,OAAQ,GACvC,SAAWkC,GAAM9B,EAAoBP,EAAI,IAAK,OAAQqC,EAAE,OAAO,KAAK,EACpE,SAAU,CAACpD,EACX,UAAU,0LACV,aAAY,0BAA0Be,EAAI,KAAK,EAAA,CACjD,EACCkC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAE,KAAA,EAC1CA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,QAAOW,EAAAnE,EAAcsB,EAAI,GAAG,IAArB,YAAA6C,EAAwB,QAAS,GACxC,SAAWR,GAAM9B,EAAoBP,EAAI,IAAK,QAASqC,EAAE,OAAO,KAAK,EACrE,SAAU,CAACpD,EACX,UAAU,0LACV,aAAY,4BAA4Be,EAAI,KAAK,EAAA,CACnD,CAAA,EACF,EAEDf,GACCiD,EAAA,IAAC,MAAI,CAAA,UAAU,8BACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMpB,EAAed,EAAI,GAAG,EACrC,UAAYqC,GAAMA,EAAE,MAAQ,SAAWvB,EAAed,EAAI,GAAG,EAC7D,UAAU,0DACV,MAAM,0BACN,aAAY,uBAAuBA,EAAI,KAAK,mBAE5C,SAAAkC,EAAAA,IAACY,EAAK,CAAA,UAAU,SAAU,CAAA,CAAA,CAAA,EAE9B,CAAA,CAAA,EApDG9C,EAAI,GAuDZ,EAAA,EACH,CAAA,EACF,EAGAiC,EAAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAAkB,qBAAA,EAC3EjD,GACCgD,EAAA,KAAC,SAAA,CACC,QAASf,EACT,UAAYmB,GAAMA,EAAE,MAAQ,SAAWnB,EAAgB,EACvD,UAAU,sHACV,aAAW,6BAEX,SAAA,CAACgB,EAAAA,IAAAa,EAAA,CAAK,UAAU,SAAU,CAAA,EAC1Bb,EAAAA,IAAC,QAAK,SAAS,WAAA,CAAA,CAAA,CAAA,CACjB,CAAA,EAEJ,EACCrD,EAAa,SAAW,EACtBoD,EAAA,KAAA,MAAA,CAAI,UAAU,oDACb,SAAA,CAACC,EAAAA,IAAAc,EAAA,CAAS,UAAU,mCAAoC,CAAA,EACxDd,EAAAA,IAAC,KAAE,SAAmC,qCAAA,CAAA,EACrCA,EAAA,IAAA,IAAA,CAAE,UAAU,UAAU,SAAsC,yCAAA,CAC/D,CAAA,CAAA,QAEC,MAAI,CAAA,UAAU,YACZ,SAAarD,EAAA,IAAKoE,GACjBhB,EAAA,KAACW,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,gIAEV,SAAA,CAAAV,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOe,EAAQ,KACf,SAAWZ,GAAMjB,EAAmB6B,EAAQ,GAAI,OAAQZ,EAAE,OAAO,KAAK,EACtE,SAAU,CAACpD,EACX,UAAU,0LACV,aAAY,4BAA4BgE,EAAQ,IAAI,EAAA,CACtD,EACAf,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOe,EAAQ,KACf,SAAWZ,GAAMjB,EAAmB6B,EAAQ,GAAI,OAAQZ,EAAE,OAAO,KAAK,EACtE,SAAU,CAACpD,EACX,YAAY,iBACZ,UAAU,iMACV,aAAY,0BAAA,CACd,EACAgD,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAASe,EAAQ,MAAM,OACvB,SAAWZ,GACTjB,EAAmB6B,EAAQ,GAAI,QAAS,CACtC,GAAGA,EAAQ,MACX,OAAQZ,EAAE,OAAO,OAAA,CAClB,EAEH,SAAU,CAACpD,EACX,UAAU,4DACV,aAAY,eAAegE,EAAQ,IAAI,EAAA,CACzC,EACCA,EAAQ,MAAM,QAEXhB,EAAA,KAAAG,EAAA,SAAA,CAAA,SAAA,CAAAF,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOe,EAAQ,MAAM,KACrB,SAAWZ,GACTjB,EAAmB6B,EAAQ,GAAI,QAAS,CACtC,GAAGA,EAAQ,MACX,KAAMZ,EAAE,OAAO,KAAA,CAChB,EAEH,SAAU,CAACpD,EACX,UAAU,0LACV,aAAY,4BAA4BgE,EAAQ,IAAI,EAAA,CACtD,EACCf,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAE,KAAA,EAC1CA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOe,EAAQ,MAAM,MACrB,SAAWZ,GACTjB,EAAmB6B,EAAQ,GAAI,QAAS,CACtC,GAAGA,EAAQ,MACX,MAAOZ,EAAE,OAAO,KAAA,CACjB,EAEH,SAAU,CAACpD,EACX,UAAU,0LACV,aAAY,8BAA8BgE,EAAQ,IAAI,EAAA,CACxD,CAAA,EACF,CAAA,EAEJ,EACChE,GACCiD,EAAA,IAAC,SAAA,CACC,QAAS,IAAMZ,EAAmB2B,EAAQ,EAAE,EAC5C,UAAYZ,GAAMA,EAAE,MAAQ,SAAWf,EAAmB2B,EAAQ,EAAE,EACpE,UAAU,wDACV,aAAY,4BAA4BA,EAAQ,IAAI,GAEpD,SAAAf,EAAAA,IAACgB,EAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAC9B,CAAA,CAAA,EA7EGD,EAAQ,EAgFhB,CAAA,EACH,CAAA,EAEJ,QAGC,MAAI,CAAA,UAAU,4FACb,SAAChB,EAAA,KAAA,MAAA,CAAI,UAAU,6BACb,SAAA,CAACC,EAAAA,IAAAiB,EAAA,CAAY,UAAU,iDAAkD,CAAA,SACxE,MACC,CAAA,SAAA,CAACjB,EAAA,IAAA,KAAA,CAAG,UAAU,oDAAoD,SAAuB,0BAAA,EACzFD,EAAAA,KAAC,KAAG,CAAA,UAAU,qDACZ,SAAA,CAAAC,EAAAA,IAAC,MAAG,SAAuD,yDAAA,CAAA,EAC3DA,EAAAA,IAAC,MAAG,SAA4D,8DAAA,CAAA,EAChEA,EAAAA,IAAC,MAAG,SAAkE,oEAAA,CAAA,EACtEA,EAAAA,IAAC,MAAG,SAA6C,+CAAA,CAAA,CAAA,EACnD,CAAA,EACF,CAAA,CAAA,CACF,CACF,CAAA,CACF,CAAA,CAAA,CAEJ,CACF,ECzfMkB,GAAsE,CAAC,CAC3E,aAAA7E,EAAe,iBACjB,IAAM,CACJ,KAAM,CAAC8E,EAASC,CAAU,EAAI1E,WAAmC,IAAI,EAC/D,CAACO,EAASC,CAAU,EAAIR,WAAS,EAAI,EACrC,CAAC2E,EAAQC,CAAS,EAAI5E,WAAS,EAAK,EACpC,CAAC6E,EAAWC,CAAY,EAAI9E,WAAS,OAAO,EAC5C,CAAC+E,EAAcC,CAAe,EAAIhF,WAAwB,IAAI,EAC9D,CAACiF,EAAYC,CAAa,EAAIlF,EAAA,SAA8B,CAAE,CAAA,EAE9DmF,EAAO,CACX,CAAE,GAAI,QAAS,MAAO,sBAAuB,KAAMC,CAAU,EAC7D,CAAE,GAAI,QAAS,MAAO,WAAY,KAAM7B,CAAM,EAC9C,CAAE,GAAI,WAAY,MAAO,gBAAiB,KAAM8B,EAAS,EACzD,CAAE,GAAI,WAAY,MAAO,WAAY,KAAMC,EAAO,CAAA,EAGpD5D,EAAAA,UAAU,IAAM,CACF6D,GAAA,EACX,CAAC5F,CAAY,CAAC,EAEjB,MAAM4F,EAAc,SAAY,CAC1B,GAAA,CACF/E,EAAW,EAAI,EACf,MAAMoC,EAAW,MAAM,MACrB,4CAA4CjD,CAAY,UAAA,EAG1D,GAAIiD,EAAS,GAAI,CACT,MAAA4C,EAAO,MAAM5C,EAAS,OACpB,QAAA,IAAI,kBAAmB4C,CAAI,EACnCd,EAAWc,EAAK,OAAO,CAAA,KAClB,CACG,QAAA,MAAM,kCAAmC5C,EAAS,MAAM,EAC1D,MAAA6C,EAAY,MAAM7C,EAAS,OAAO,MAAM,KAAO,CAAG,EAAA,EAChD,QAAA,MAAM,iBAAkB6C,CAAS,EA0BzCf,EAvBuC,CACrC,GAAI/E,EACJ,KAAM,mBACN,MAAO,iBACP,MAAO,kBACP,QAAS,sCACT,YACE,gFACF,cAAe,CACb,OAAQ,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACtD,QAAS,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACvD,UAAW,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACzD,SAAU,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACxD,OAAQ,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACtD,SAAU,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,EACxD,OAAQ,CAAE,KAAM,QAAS,MAAO,QAAS,OAAQ,EAAK,CACxD,EACA,SAAU,CACR,iBAAkB,GAClB,sBAAuB,EACvB,gBAAiB,EACnB,CAAA,CAEoB,EACtB8B,EAAM,MAAM,8BAA8B,CAC5C,QACOD,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,EAC/CC,EAAM,MAAM,oCAAoC,CAAA,QAChD,CACAjB,EAAW,EAAK,CAClB,CAAA,EAGIkF,EAAY,CAAC9D,EAAe+D,IAAsB,CACtDX,EAAgBpD,CAAK,EACrBsD,EAAc,CAAE,CAACtD,CAAK,EAAG+D,CAAc,CAAA,CAAA,EAGnCC,EAAa,IAAM,CACvBZ,EAAgB,IAAI,EACpBE,EAAc,CAAE,CAAA,CAAA,EAGZW,EAAY,MAAOjE,GAAkB,CACzC,GAAK6C,EAED,GAAA,CACFG,EAAU,EAAI,EACd,MAAMkB,EAAa,CAAE,CAAClE,CAAK,EAAGqD,EAAWrD,CAAK,CAAE,EAE1CgB,EAAW,MAAM,MACrB,4CAA4CjD,CAAY,WACxD,CACE,OAAQ,MACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,UAAUmG,CAAU,CACjC,CAAA,EAGF,GAAIlD,EAAS,GAAI,CACT,MAAA4C,EAAO,MAAM5C,EAAS,OACjB8B,EAAAc,EAAK,SAAW,CAAE,GAAGf,EAAS,CAAC7C,CAAK,EAAGqD,EAAWrD,CAAK,CAAA,CAAG,EACrEoD,EAAgB,IAAI,EACpBE,EAAc,CAAE,CAAA,EAChBzD,EAAM,QAAQ,+BAA+B,CAAA,KACxC,CACC,MAAAgE,EAAY,MAAM7C,EAAS,OAAO,MAAM,KAAO,CAAG,EAAA,EAClDnB,EAAA,MAAMgE,EAAU,SAAW,gBAAgB,CACnD,QACOjE,EAAO,CACN,QAAA,MAAM,wBAAyBA,CAAK,EAC5CC,EAAM,MAAM,iBAAiB,CAAA,QAC7B,CACAmD,EAAU,EAAK,CACjB,CAAA,EAGImB,EAAe,MAAOC,GAAqB,CAC/C,GAAKvB,EAED,GAAA,CACFG,EAAU,EAAI,EACd,MAAMhC,EAAW,MAAM,MACrB,4CAA4CjD,CAAY,WACxD,CACE,OAAQ,MACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,UAAU,CAAE,SAAUqG,EAAa,CAChD,CAAA,EAGF,GAAIpD,EAAS,GAAI,CACT,MAAA4C,EAAO,MAAM5C,EAAS,OAC5B8B,EAAWc,EAAK,SAAW,CAAE,GAAGf,EAAS,SAAUuB,EAAa,EAChEvE,EAAM,QAAQ,uBAAuB,CAAA,KAChC,CACC,MAAAgE,EAAY,MAAM7C,EAAS,OAAO,MAAM,KAAO,CAAG,EAAA,EAClDnB,EAAA,MAAMgE,EAAU,SAAW,8BAA8B,CACjE,QACOjE,EAAO,CACN,QAAA,MAAM,gCAAiCA,CAAK,EACpDC,EAAM,MAAM,iBAAiB,CAAA,QAC7B,CACAmD,EAAU,EAAK,CACjB,CAAA,EAGIqB,EAOD,CAAC,CACJ,MAAArE,EACA,MAAAsE,EACA,MAAArE,EACA,KAAMsE,EACN,KAAAC,EAAO,OACP,UAAAC,EAAY,EAAA,IACR,CACJ,MAAMhG,EAAY0E,IAAiBnD,EAGjC,OAAAyB,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,QAAA,CAAM,UAAU,6DACf,SAAA,CAACC,EAAAA,IAAA6C,EAAA,CAAK,UAAU,qBAAsB,CAAA,EACrCD,CAAA,EACH,EAEC5C,MAAA,MAAA,CAAI,UAAU,8BACZ,WAEID,EAAA,KAAAG,WAAA,CAAA,SAAA,CACC6C,EAAA/C,EAAA,IAAC,WAAA,CACC,MAAO2B,EAAWrD,CAAK,GAAK,GAC5B,SAAW6B,GACTyB,EAAc,CAAE,GAAGD,EAAY,CAACrD,CAAK,EAAG6B,EAAE,OAAO,MAAO,EAE1D,UAAU,4LACV,KAAM,CAAA,CAAA,EAGRH,EAAA,IAAC,QAAA,CACC,KAAA8C,EACA,MAAOnB,EAAWrD,CAAK,GAAK,GAC5B,SAAW6B,GACTyB,EAAc,CAAE,GAAGD,EAAY,CAACrD,CAAK,EAAG6B,EAAE,OAAO,MAAO,EAE1D,UAAU,2LAAA,CACZ,EAGFH,EAAA,IAAC,SAAA,CACC,QAAS,IAAMuC,EAAUjE,CAAK,EAC9B,SAAU+C,EACV,UAAU,kGAEV,SAAArB,EAAAA,IAACgD,GAAM,CAAA,UAAU,SAAU,CAAA,CAAA,CAC7B,EAEAhD,EAAA,IAAC,SAAA,CACC,QAASsC,EACT,UAAU,4EAEV,SAAAtC,EAAAA,IAACI,EAAE,CAAA,UAAU,SAAU,CAAA,CAAA,CACzB,CAAA,CAAA,CACF,EAGEL,EAAAA,KAAAG,EAAA,SAAA,CAAA,SAAA,CAAAF,EAAA,IAAC,MAAI,CAAA,UAAU,wFACZ,SAAAzB,GAAS,gBACZ,EAEAyB,EAAA,IAAC,SAAA,CACC,QAAS,IAAMoC,EAAU9D,EAAOC,CAAK,EACrC,UAAU,4EAEV,SAAAyB,EAAAA,IAACiD,GAAK,CAAA,UAAU,SAAU,CAAA,CAAA,CAC5B,CAAA,CAAA,CACF,CAEJ,CAAA,CACF,CAAA,CAAA,CAAA,EAIJ,OAAIhG,EAEA8C,EAAA,KAAC,MAAI,CAAA,UAAU,uCACb,SAAA,CAACC,EAAAA,IAAAkD,EAAA,CAAU,UAAU,yCAA0C,CAAA,EAC9DlD,EAAA,IAAA,OAAA,CAAK,UAAU,mCAAmC,SAEnD,uBAAA,CACF,CAAA,CAAA,EAICmB,EAkBHpB,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,iDACb,eAAC8B,EAAU,CAAA,UAAU,2CAA2C,CAClE,CAAA,SACC,MACC,CAAA,SAAA,CAAC9B,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAEpE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,iDAAA,CAAA,EACF,CAAA,EACF,EAEAD,EAAA,KAAC,SAAA,CACC,QAASkC,EACT,SAAUhF,EACV,UAAU,gMAEV,SAAA,CAAA+C,MAACkD,GAAU,UAAW,WAAWjG,EAAU,eAAiB,EAAE,GAAI,EAClE+C,EAAAA,IAAC,QAAK,SAAS,WAAA,CAAA,CAAA,CAAA,CACjB,CAAA,EACF,EAGAA,EAAA,IAAC,MAAI,CAAA,UAAU,gDACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,iBACZ,SAAA6B,EAAK,IAAKsB,GACTpD,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMyB,EAAa2B,EAAI,EAAE,EAClC,UAAW,0FACT5B,IAAc4B,EAAI,GACd,mDACA,kGACN,GAEA,SAAA,CAAAnD,EAAAA,IAACmD,EAAI,KAAJ,CAAS,UAAU,SAAU,CAAA,EAC9BnD,EAAAA,IAAC,OAAM,CAAA,SAAAmD,EAAI,KAAM,CAAA,CAAA,CAAA,EATZA,EAAI,EAAA,CAWZ,EACH,CACF,CAAA,EAGAnD,EAAAA,IAACoD,EAAgB,CAAA,KAAK,OACpB,SAAArD,EAAA,KAACW,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAE3B,SAAA,CAAca,IAAA,eACZ,MAAI,CAAA,UAAU,YACb,SAACxB,EAAA,KAAA,MAAA,CAAI,UAAU,uFACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,sBAAA,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAC,EAAA,IAAC2C,EAAA,CACC,MAAM,OACN,MAAM,sBACN,MAAOxB,EAAQ,KACf,KAAMW,CAAA,CACR,EAEA9B,EAAA,IAAC2C,EAAA,CACC,MAAM,QACN,MAAM,QACN,MAAOxB,EAAQ,MACf,KAAMkC,EACN,KAAK,OAAA,CACP,EAEArD,EAAA,IAAC2C,EAAA,CACC,MAAM,QACN,MAAM,WACN,MAAOxB,EAAQ,MACf,KAAMmC,EACN,KAAK,KAAA,CACP,EAEAtD,EAAA,IAAC2C,EAAA,CACC,MAAM,UACN,MAAM,WACN,MAAOxB,EAAQ,QACf,KAAMoC,EAAA,CACR,CAAA,EACF,EAEAvD,EAAAA,IAAC,MAAI,CAAA,UAAU,OACb,SAAAA,EAAA,IAAC2C,EAAA,CACC,MAAM,cACN,MAAM,YACN,MAAOxB,EAAQ,YACf,KAAMqC,GACN,UAAS,EAAA,CAAA,EAEb,CAAA,CAAA,CACF,CACF,CAAA,EAGDjC,IAAc,SACZvB,MAAA,MAAA,CAAI,UAAU,uFACb,SAAAA,EAAA,IAAC7D,GAAA,CACC,aAAAE,EACA,aAAc8E,EAAQ,cACtB,OAASsC,GAAU,CACjBrC,EAAW,CAAE,GAAGD,EAAS,cAAesC,CAAO,CAAA,EAC/CtF,EAAM,QAAQ,uBAAuB,CACvC,CAAA,CAAA,EAEJ,EAGDoD,IAAc,YACZvB,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,4BAAA,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,qBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,iCAAA,CAAA,EACF,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IACPyC,EAAa,CACX,GAAGtB,EAAQ,SACX,iBAAkB,CAACA,EAAQ,SAAS,gBAAA,CACrC,EAEH,UAAW,6EACTA,EAAQ,SAAS,iBACb,cACA,8BACN,GAEA,SAAAnB,EAAA,IAAC,OAAA,CACC,UAAW,6EACTmB,EAAQ,SAAS,iBACb,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,EACF,EAEApB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4CAA4C,SAE1D,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,uCAAA,CAAA,EACF,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IACPyC,EAAa,CACX,GAAGtB,EAAQ,SACX,gBAAiB,CAACA,EAAQ,SAAS,eAAA,CACpC,EAEH,UAAW,6EACTA,EAAQ,SAAS,gBACb,cACA,8BACN,GAEA,SAAAnB,EAAA,IAAC,OAAA,CACC,UAAW,6EACTmB,EAAQ,SAAS,gBACb,gBACA,eACN,EAAA,CACF,CAAA,CACF,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAACnB,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,kCAAA,EACAD,EAAA,KAAC,SAAA,CACC,MAAOoB,EAAQ,SAAS,sBACxB,SAAWhB,GACTsC,EAAa,CACX,GAAGtB,EAAQ,SACX,sBAAuB,SAAShB,EAAE,OAAO,KAAK,CAAA,CAC/C,EAEH,UAAU,4LAEV,SAAA,CAACH,EAAA,IAAA,SAAA,CAAO,MAAO,EAAG,SAAU,aAAA,EAC3BA,EAAA,IAAA,SAAA,CAAO,MAAO,EAAG,SAAW,cAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAO,EAAG,SAAW,cAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAO,EAAG,SAAW,cAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAY,eAAA,EAC9BA,EAAA,IAAA,SAAA,CAAO,MAAO,GAAI,SAAS,YAAA,CAAA,CAAA,CAC9B,CAAA,EACF,CAAA,EACF,CAAA,CAAA,CACF,CACF,CAAA,EAGDuB,IAAc,YACZvB,EAAAA,IAAA,MAAA,CAAI,UAAU,YACb,SAAAD,EAAA,KAAC,MAAI,CAAA,UAAU,uFACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,0BAAA,EAECA,MAAA,MAAA,CAAI,UAAU,YACb,gBAAC,MACC,CAAA,SAAA,CAACA,EAAA,IAAA,KAAA,CAAG,UAAU,iDAAiD,SAE/D,yBAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAE3D,MAAA,EACCA,EAAA,IAAA,OAAA,CAAK,UAAU,kDACb,WAAQ,GACX,CAAA,EACF,EACCmB,EAAQ,WACNpB,OAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAE3D,aAAA,EACAA,MAAC,QAAK,UAAU,wCACb,aAAI,KAAKmB,EAAQ,SAAS,EAAE,mBAC3B,OAAA,EAEJ,CAAA,EACF,EAEDA,EAAQ,WACNpB,OAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAE3D,iBAAA,EACAA,MAAC,QAAK,UAAU,wCACb,aAAI,KAAKmB,EAAQ,SAAS,EAAE,mBAC3B,OAAA,EAEJ,CAAA,EACF,CAAA,EAEJ,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,CAAA,EA3NGI,CAAA,EA8NT,CACF,CAAA,CAAA,EAjSExB,EAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAACC,EAAAA,IAAAiB,EAAA,CAAY,UAAU,qCAAsC,CAAA,EAC5DjB,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,0BAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAASiC,EACT,UAAU,kFACX,SAAA,kBAAA,CAED,CACF,CAAA,CAAA,CAwRN"}