"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../utils/validation");
const asyncHandler_1 = __importDefault(require("../middleware/asyncHandler"));
const errors_1 = require("../utils/errors");
const database_1 = require("../config/database");
const Restaurant_1 = require("../models/Restaurant");
const Playlist_1 = require("../models/Playlist");
const Suggestion_1 = require("../models/Suggestion");
const logger_1 = require("../utils/logger");
const QRCode = __importStar(require("qrcode"));
const MercadoPagoClient_1 = require("../services/MercadoPagoClient");
const youtube_1 = require("../config/youtube");
const auth_1 = require("../middleware/auth");
const PaymentService_1 = require("../services/PaymentService");
const router = (0, express_1.Router)();
// Debug: logar todas as requisições que entram no router de pagamentos
router.use((req, _res, next) => {
    try {
        logger_1.logger.info(`[payments-router] ${req.method} ${req.path}`);
    }
    catch { }
    next();
});
/**
 * GET /api/v1/payments/config
 * Verificar configuração do Mercado Pago
 */
router.get("/config", (0, asyncHandler_1.default)(async (req, res) => {
    const accessToken = process.env.MERCADO_PAGO_ACCESS_TOKEN || process.env.MP_ACCESS_TOKEN;
    const publicKey = process.env.MERCADO_PAGO_PUBLIC_KEY;
    const environment = process.env.MERCADO_PAGO_ENVIRONMENT || "sandbox";
    res.json({
        success: true,
        config: {
            accessTokenConfigured: !!accessToken && accessToken !== "TEST-YOUR-ACCESS-TOKEN",
            publicKeyConfigured: !!publicKey && publicKey !== "TEST-YOUR-PUBLIC-KEY",
            environment,
            webhookUrl: `${req.protocol}://${req.get("host")}/api/v1/payments/webhook`,
            accessTokenPrefix: accessToken ? accessToken.substring(0, 20) + "..." : "não configurado"
        }
    });
}));
/**
 * Gera QR Code de forma segura com fallback
 */
async function generateQRCodeSafely(pixCode) {
    try {
        const qrCodeDataURL = await QRCode.toDataURL(pixCode, {
            errorCorrectionLevel: "M",
            type: "image/png",
            margin: 2,
            color: {
                dark: "#000000",
                light: "#FFFFFF",
            },
            width: 256,
        });
        logger_1.logger.info("✅ QR Code PIX gerado com sucesso");
        return qrCodeDataURL;
    }
    catch (error) {
        logger_1.logger.error("❌ Erro ao gerar QR Code PIX:", error);
        // Fallback: gerar QR Code simples usando API externa
        try {
            const fallbackUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(pixCode)}&size=256x256&margin=10`;
            const response = await fetch(fallbackUrl);
            if (response.ok) {
                const buffer = await response.arrayBuffer();
                const base64 = Buffer.from(buffer).toString("base64");
                const dataUrl = `data:image/png;base64,${base64}`;
                logger_1.logger.info("✅ QR Code gerado via fallback API");
                return dataUrl;
            }
        }
        catch (fallbackError) {
            logger_1.logger.error("❌ Erro no fallback do QR Code:", fallbackError);
        }
        // Último recurso: QR Code placeholder
        logger_1.logger.warn("⚠️ Usando QR Code placeholder");
        return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
    }
}
// Rota de diagnóstico para listar rotas registradas neste router
router.get("/_routes", (req, res) => {
    try {
        const list = [];
        const anyRouter = router;
        const stack = anyRouter.stack || [];
        for (const layer of stack) {
            if (layer && layer.route && layer.route.path && layer.route.stack) {
                const methods = Object.keys(layer.route.methods || {}).filter((m) => layer.route.methods[m]);
                for (const m of methods) {
                    list.push({ method: m.toUpperCase(), path: String(layer.route.path) });
                }
            }
        }
        res.json({ success: true, routes: list });
    }
    catch (e) {
        res.json({ success: false, error: e.message });
    }
});
// Rota de teste
router.get("/test", (req, res) => {
    console.log("🧪 Rota de teste de pagamentos chamada");
    res.json({ message: "Rota de pagamentos funcionando!" });
});
// Debug: listar rotas registradas neste router
router.get("/routes", (0, asyncHandler_1.default)(async (req, res) => {
    // @ts-ignore acessar estrutura interna do router para depuração
    const stack = router.stack || [];
    const routes = stack
        .filter((l) => l.route)
        .map((l) => ({
        methods: Object.keys(l.route.methods),
        path: l.route.path,
    }));
    res.json({ success: true, base: "/api/v1/payments", routes });
}));
/**
 * Diagnóstico de configuração de pagamentos
 */
router.get("/config", (0, asyncHandler_1.default)(async (req, res) => {
    const accessToken = process.env.MERCADO_PAGO_ACCESS_TOKEN || process.env.MP_ACCESS_TOKEN;
    const publicBaseUrl = process.env.PUBLIC_BASE_URL || `${req.protocol}://${req.get("host")}`;
    const integratorId = process.env.MP_INTEGRATOR_ID || null;
    res.json({
        success: true,
        mercadoPago: {
            accessTokenConfigured: Boolean(accessToken),
            integratorIdConfigured: Boolean(integratorId),
            environment: process.env.MERCADO_PAGO_ENVIRONMENT || process.env.NODE_ENV ||
                "development",
        },
        publicBaseUrl,
        webhookUrl: `${publicBaseUrl.replace(/\/$/, "")}/api/v1/payments/webhook`,
    });
}));
/**
 * @swagger
 * /api/v1/payments/stats/{restaurantId}:
 *   get:
 *     summary: Obter estatísticas de pagamentos PIX
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [1d, 7d, 30d, 90d]
 *           default: 7d
 *     responses:
 *       200:
 *         description: Estatísticas de pagamentos
 */
router.get("/stats/:restaurantId", [
    (0, validation_1.param)("restaurantId").notEmpty().withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.query)("period").optional().isIn(["1d", "7d", "30d", "90d"]),
], auth_1.optionalAuth, (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Parâmetros inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { period = "7d" } = req.query;
    const stats = await PaymentService_1.paymentService.getPaymentStats({ restaurantId, period });
    res.json({ success: true, stats, restaurantId, period });
}));
/**
 * @swagger
 * /api/v1/payments/pix/suggestion:
 *   post:
 *     summary: Criar pagamento PIX para sugestão prioritária
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - youtubeId
 *               - title
 *             properties:
 *               restaurantId:
 *                 type: string
 *               youtubeId:
 *                 type: string
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableNumber:
 *                 type: integer
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Pagamento PIX criado com sucesso
 *       400:
 *         description: Dados inválidos
 *       404:
 *         description: Restaurante não encontrado
 */
router.post("/pix/suggestion", [
    (0, validation_1.body)("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    (0, validation_1.body)("youtubeId").notEmpty().withMessage("YoutubeId é obrigatório"),
    (0, validation_1.body)("title").notEmpty().withMessage("Title é obrigatório"),
    (0, validation_1.body)("clientMessage").optional().isString().isLength({ max: 200 }),
    (0, validation_1.body)("clientName").optional().isString().isLength({ max: 80 }),
    (0, validation_1.body)("amount")
        .optional()
        .isInt({ min: 100 })
        .withMessage("Amount deve ser em centavos (mínimo 100)"),
    validation_1.checkValidationErrors,
], (0, asyncHandler_1.default)(async (req, res) => {
    logger_1.logger.info("🟢 Handler /pix/suggestion iniciado");
    logger_1.logger.info("📥 Request body:", JSON.stringify(req.body, null, 2));
    logger_1.logger.info("📥 Request headers:", JSON.stringify(req.headers, null, 2));
    try {
        const { restaurantId, youtubeId, title: inputTitle, artist, clientName, clientMessage, tableNumber, sessionId, amount: rawAmountCents, } = req.body;
        const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        const restaurant = await restaurantRepository.findOne({ where: { id: restaurantId } });
        if (!restaurant)
            throw new errors_1.NotFoundError("Restaurante não encontrado");
        // Validar valores permitidos: R$ 5,00, R$ 20,00, R$ 50,00
        const allowedAmounts = [500, 2000, 5000]; // R$ 5,00, R$ 20,00, R$ 50,00
        const amountCents = Number(rawAmountCents) && Number(rawAmountCents) > 0 ? Number(rawAmountCents) : 500;
        if (!allowedAmounts.includes(amountCents)) {
            logger_1.logger.warn("❌ Valor de pagamento inválido", {
                amountCents,
                allowedAmounts,
                restaurantId,
                sessionId
            });
            throw new errors_1.ValidationError(`Valor inválido. Valores permitidos: R$ 5,00 (500), R$ 20,00 (2000) ou R$ 50,00 (5000) centavos. Recebido: ${amountCents}`);
        }
        const amount = parseFloat((amountCents / 100).toFixed(2));
        logger_1.logger.info("💰 Valor do pagamento validado", {
            amountCents,
            amountReais: amount,
            restaurantId
        });
        // Resolver metadados reais (título/artista) a partir da playlist ativa ou YouTube
        let resolvedTitle = inputTitle;
        let resolvedArtist = artist;
        try {
            const playlistRepo = database_1.AppDataSource.getRepository(Playlist_1.Playlist);
            const activePlaylists = await playlistRepo.find({
                where: { restaurant: { id: restaurantId }, status: Playlist_1.PlaylistStatus.ACTIVE },
                order: { executionOrder: "ASC", createdAt: "DESC" },
            });
            const withTracks = activePlaylists.find((p) => Array.isArray(p.tracks) && p.tracks.some((t) => t.youtubeVideoId === youtubeId));
            if (withTracks) {
                const track = withTracks.tracks.find((t) => t.youtubeVideoId === youtubeId);
                resolvedTitle = track?.title || resolvedTitle;
                resolvedArtist = track?.artist || resolvedArtist;
            }
            else if (activePlaylists.length > 0 && process.env.ENFORCE_PLAYLIST_MEMBERSHIP === "true") {
                // Restaurante possui playlist ativa mas vídeo não está nela
                throw new errors_1.ValidationError("Vídeo não pertence à playlist ativa do restaurante. Sincronize a playlist ou escolha uma música da playlist.");
            }
            // Se ainda não resolvido, tentar YouTube (quando chave configurada)
            if ((!resolvedTitle || !resolvedArtist) && process.env.YOUTUBE_API_KEY) {
                try {
                    const video = await youtube_1.youtubeService.getVideoDetails(youtubeId);
                    if (video) {
                        resolvedTitle = resolvedTitle || video.title;
                        resolvedArtist = resolvedArtist || video.artist;
                    }
                }
                catch (e) {
                    // ignora, seguimos com dados fornecidos
                }
            }
        }
        catch (metaErr) {
            // Para erros de validação da regra de membership, propagar
            if (metaErr instanceof errors_1.ValidationError)
                throw metaErr;
            logger_1.logger.warn("Falha ao resolver metadados reais da música", { error: metaErr.message });
        }
        const finalTitle = resolvedTitle || inputTitle || "Música";
        const finalArtist = resolvedArtist || artist || "Artista Desconhecido";
        // Inicializa cliente Mercado Pago
        const accessToken = process.env.MERCADO_PAGO_ACCESS_TOKEN || process.env.MP_ACCESS_TOKEN;
        if (!accessToken) {
            logger_1.logger.error("❌ Configuração de Mercado Pago ausente", {
                MERCADO_PAGO_ACCESS_TOKEN: !!process.env.MERCADO_PAGO_ACCESS_TOKEN,
                MP_ACCESS_TOKEN: !!process.env.MP_ACCESS_TOKEN
            });
            throw new errors_1.ValidationError("Configuração de Mercado Pago ausente (MERCADO_PAGO_ACCESS_TOKEN)");
        }
        const integratorId = process.env.MP_INTEGRATOR_ID;
        const mp = new MercadoPagoClient_1.MercadoPagoClient(accessToken, integratorId);
        logger_1.logger.info("💳 Criando pagamento PIX", { restaurantId, amount, youtubeId, title: finalTitle });
        // notification_url deve ser público (defina via env em produção)
        // notification_url deve ser PÚBLICO (MP rejeita localhost). Usar apenas PUBLIC_BASE_URL se for válido.
        let notificationUrl;
        try {
            const baseFromEnv = process.env.PUBLIC_BASE_URL;
            if (baseFromEnv && baseFromEnv.trim().length > 0) {
                const full = `${baseFromEnv.replace(/\/$/, "")}/api/v1/payments/webhook`;
                const u = new URL(full);
                const host = u.hostname.toLowerCase();
                const isLocal = host === "localhost" || host === "127.0.0.1";
                if (!isLocal) {
                    notificationUrl = u.toString();
                }
                else {
                    logger_1.logger.warn("🔕 Omitindo notification_url: PUBLIC_BASE_URL aponta para host local (não aceito pelo Mercado Pago)", { full });
                }
            }
            else {
                logger_1.logger.warn("🔕 Omitindo notification_url: PUBLIC_BASE_URL não definido; defina uma URL pública para habilitar webhooks");
            }
        }
        catch (e) {
            logger_1.logger.warn("🔕 Omitindo notification_url: valor inválido em PUBLIC_BASE_URL", { error: e.message });
        }
        // Cria cobrança PIX real
        let mpPayment;
        try {
            // Montar payer com fallback de email (algumas contas exigem email em PIX)
            // Normaliza um identificador curto e usa domínio seguro para evitar rejeição por email inválido
            const normId = String(sessionId || "anon").replace(/[^a-zA-Z0-9]/g, "").slice(0, 24) || "anon";
            const payer = {
                first_name: (clientName || "Cliente").toString().slice(0, 60),
                email: `customer+${normId}@example.com`,
            };
            mpPayment = await mp.createPixPayment({
                amount,
                description: `Sugestão prioritária: ${finalTitle}`,
                notificationUrl,
                payer,
                metadata: {
                    restaurantId,
                    youtubeId,
                    title: finalTitle,
                    artist: finalArtist,
                    sessionId,
                    tableNumber,
                    source: "supervote",
                    clientName,
                    clientMessage,
                },
            });
            logger_1.logger.info("✅ PIX criado no Mercado Pago", {
                paymentId: mpPayment.id,
                amount: mpPayment.transaction_amount,
                status: mpPayment.status
            });
        }
        catch (mpError) {
            logger_1.logger.error("❌ Erro do Mercado Pago:", {
                error: mpError.message,
                response: mpError.response?.data,
                status: mpError.response?.status
            });
            return res.status(400).json({
                success: false,
                error: "Erro ao criar pagamento PIX",
                details: mpError.response?.data || mpError.message,
                message: "Verifique as credenciais do Mercado Pago"
            });
        }
        const qr = mpPayment.point_of_interaction?.transaction_data || {};
        const pixCode = qr.qr_code || "";
        const qrCodeBase64 = qr.qr_code_base64 || (pixCode ? await generateQRCodeSafely(pixCode) : "");
        res.json({
            success: true,
            payment: {
                id: mpPayment.id,
                amount: mpPayment.transaction_amount,
                status: mpPayment.status,
                pixCode,
                qrCodeData: qrCodeBase64,
                ticketUrl: qr.ticket_url || null,
                description: `Sugestão prioritária: ${finalTitle}`,
                createdAt: mpPayment.date_created,
                restaurantId,
                youtubeId,
                title: finalTitle,
                artist: finalArtist,
            },
            message: "Código PIX gerado com sucesso!",
        });
    }
    catch (err) {
        // Intercepta erros do MP e retorna status apropriado ao invés de cair no notFound
        if (err?.isAxiosError) {
            const status = err.response?.status || 502;
            const dataMsg = err.response?.data?.message || err.response?.data?.error || err.message;
            return res.status(status).json({
                success: false,
                error: `Erro no Mercado Pago: ${dataMsg}`,
                details: err.response?.data || null,
            });
        }
        throw err;
    }
}));
/**
 * @swagger
 * /api/v1/payments/:paymentId/status:
 *   get:
 *     summary: Verificar status do pagamento PIX
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status do pagamento
 *       400:
 *         description: PaymentId inválido
 */
router.get("/:paymentId/status", [(0, validation_1.param)("paymentId").notEmpty().withMessage("PaymentId é obrigatório"), validation_1.checkValidationErrors], (0, asyncHandler_1.default)(async (req, res) => {
    const { paymentId } = req.params;
    const accessToken = process.env.MP_ACCESS_TOKEN || process.env.MERCADO_PAGO_ACCESS_TOKEN;
    if (!accessToken)
        throw new errors_1.ValidationError("Configuração de Mercado Pago ausente (MP_ACCESS_TOKEN)");
    const integratorId = process.env.MP_INTEGRATOR_ID;
    const mp = new MercadoPagoClient_1.MercadoPagoClient(accessToken, integratorId);
    const payment = await mp.getPayment(paymentId);
    const status = payment.status;
    res.json({
        success: true,
        payment: {
            id: String(payment.id),
            status: status === "approved" ? "paid" : status,
            paidAt: payment.date_approved || null,
            amount: payment.transaction_amount,
        },
    });
}));
/**
 * @swagger
 * /api/v1/payments/:paymentId/confirm:
 *   post:
 *     summary: Confirmar pagamento e criar sugestão prioritária
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - restaurantId
 *               - youtubeId
 *               - title
 *             properties:
 *               restaurantId:
 *                 type: string
 *               youtubeId:
 *                 type: string
 *               title:
 *                 type: string
 *               artist:
 *                 type: string
 *               clientName:
 *                 type: string
 *               tableNumber:
 *                 type: integer
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Sugestão prioritária criada com sucesso
 *       400:
 *         description: Pagamento não aprovado ou dados inválidos
 */
router.post("/:paymentId/confirm", [
    (0, validation_1.param)("paymentId").notEmpty().withMessage("PaymentId é obrigatório"),
    // Em modo real, os dados vêm do Mercado Pago (metadata). Mantemos no body como opcional para compatibilidade.
    (0, validation_1.body)("restaurantId").optional().isString(),
    (0, validation_1.body)("youtubeId").optional().isString(),
    (0, validation_1.body)("title").optional().isString(),
    (0, validation_1.body)("artist").optional().isString(),
    (0, validation_1.body)("clientName").optional().isString(),
    (0, validation_1.body)("clientMessage").optional().isString().isLength({ max: 200 }),
    (0, validation_1.body)("tableNumber").optional().isInt(),
    (0, validation_1.body)("sessionId").optional().isString(),
    validation_1.checkValidationErrors,
], (0, asyncHandler_1.default)(async (req, res) => {
    const { paymentId } = req.params;
    // Pegar dados reais do Mercado Pago
    const accessToken = process.env.MP_ACCESS_TOKEN || process.env.MERCADO_PAGO_ACCESS_TOKEN;
    if (!accessToken)
        throw new errors_1.ValidationError("Configuração de Mercado Pago ausente (MP_ACCESS_TOKEN)");
    const integratorId = process.env.MP_INTEGRATOR_ID;
    const mp = new MercadoPagoClient_1.MercadoPagoClient(accessToken, integratorId);
    const payment = await mp.getPayment(paymentId);
    const isApproved = payment.status === "approved";
    if (!isApproved) {
        return res.status(400).json({ success: false, error: `Pagamento não aprovado (status: ${payment.status})` });
    }
    // Idempotência: se já existir, retorna o registro
    const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
    const existing = await suggestionRepository.findOne({ where: { paymentId: String(paymentId) } });
    if (existing) {
        if (existing.paymentStatus !== "paid") {
            existing.paymentStatus = "paid";
            existing.paymentAmount = payment.transaction_amount;
            await suggestionRepository.save(existing);
        }
        return res.json({ success: true, suggestion: existing, message: "Pagamento confirmado (idempotente)" });
    }
    // Extrair metadados do pagamento (fonte da verdade)
    const md = payment.metadata || {};
    const metaRestaurantId = md.restaurantId || req.body.restaurantId;
    const metaYoutubeId = md.youtubeId || req.body.youtubeId;
    const metaTitle = md.title || req.body.title;
    const metaArtist = md.artist || req.body.artist || "Artista Desconhecido";
    const metaClientName = md.clientName || req.body.clientName || "Anônimo";
    const metaClientMessage = md.clientMessage || req.body.clientMessage || null;
    const metaTableNumber = md.tableNumber ?? req.body.tableNumber ?? null;
    const metaSessionId = md.sessionId || req.body.sessionId || null;
    if (!metaRestaurantId)
        throw new errors_1.ValidationError("restaurantId ausente nos metadados do pagamento ou no corpo");
    if (!metaYoutubeId)
        throw new errors_1.ValidationError("youtubeId ausente nos metadados do pagamento ou no corpo");
    if (!metaTitle)
        throw new errors_1.ValidationError("title ausente nos metadados do pagamento ou no corpo");
    const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
    const restaurant = await restaurantRepository.findOne({ where: { id: metaRestaurantId } });
    if (!restaurant)
        throw new errors_1.NotFoundError("Restaurante não encontrado");
    // Criar sugestão prioritária com valores reais
    const suggestion = suggestionRepository.create({
        youtubeVideoId: metaYoutubeId,
        title: metaTitle,
        artist: metaArtist,
        clientName: metaClientName,
        clientMessage: metaClientMessage,
        tableNumber: metaTableNumber,
        clientSessionId: metaSessionId,
        status: Suggestion_1.SuggestionStatus.APPROVED,
        isPriority: true,
        paymentId: String(paymentId),
        paymentAmount: payment.transaction_amount,
        paymentStatus: "paid",
        restaurant: restaurant,
    });
    const savedSuggestion = await suggestionRepository.save(suggestion);
    logger_1.logger.info("Sugestão prioritária criada a partir de pagamento aprovado", {
        suggestionId: savedSuggestion.id,
        paymentId: String(paymentId),
        restaurantId: metaRestaurantId,
        amount: payment.transaction_amount,
        title: metaTitle,
    });
    res.json({ success: true, suggestion: savedSuggestion, message: "Sugestão prioritária criada com sucesso!" });
}));
/**
 * @swagger
 * /api/v1/payments/history:
 *   get:
 *     summary: Obter histórico de pagamentos de um restaurante
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: restaurantId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *     responses:
 *       200:
 *         description: Histórico de pagamentos
 */
router.get("/history", [
    (0, validation_1.query)("restaurantId").notEmpty().withMessage("RestaurantId é obrigatório"),
    (0, validation_1.query)("limit").optional().isInt({ min: 1, max: 100 }),
    validation_1.checkValidationErrors,
], (0, asyncHandler_1.default)(async (req, res) => {
    const { restaurantId, limit = 50 } = req.query;
    // Buscar sugestões pagas do restaurante
    const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
    const paidSuggestions = await suggestionRepository.find({
        where: {
            restaurant: { id: restaurantId },
            paymentStatus: "paid",
            isPriority: true,
        },
        order: { createdAt: "DESC" },
        take: Number(limit),
    });
    const payments = paidSuggestions.map((suggestion) => ({
        id: suggestion.paymentId || suggestion.id,
        amount: suggestion.paymentAmount || 2.0,
        title: suggestion.title,
        artist: suggestion.artist,
        clientName: suggestion.clientName,
        tableNumber: suggestion.tableNumber,
        status: suggestion.paymentStatus || "paid",
        createdAt: suggestion.createdAt,
    }));
    res.json({
        success: true,
        payments,
        total: payments.length,
        restaurantId,
    });
}));
/**
 * @swagger
 * /api/v1/payments/webhook:
 *   post:
 *     summary: Webhook do Mercado Pago (placeholder)
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Webhook processado
 */
router.post("/webhook", (0, asyncHandler_1.default)(async (req, res) => {
    // Webhook do Mercado Pago: recebemos tópico e id do recurso
    // Documentação: https://www.mercadopago.com.br/developers/pt/docs/your-integrations/notifications/webhooks
    try {
        const topic = (req.query.topic || req.query.type || req.body?.type);
        const dataId = (req.query.id || req.body?.data?.id || req.body?.resource?.id);
        logger_1.logger.info("🔔 Webhook recebido", { topic, dataId, body: req.body });
        if (!dataId) {
            return res.status(200).json({ success: true });
        }
        const accessToken = process.env.MP_ACCESS_TOKEN || process.env.MERCADO_PAGO_ACCESS_TOKEN;
        const integratorId = process.env.MP_INTEGRATOR_ID;
        if (!accessToken) {
            logger_1.logger.warn("Webhook recebido, mas MP_ACCESS_TOKEN ausente");
            return res.status(200).json({ success: true });
        }
        const mp = new MercadoPagoClient_1.MercadoPagoClient(accessToken, integratorId);
        const payment = await mp.getPayment(String(dataId));
        // Mapear status e, se aprovado, podemos emitir evento ou atualizar banco
        const normalized = {
            id: String(payment.id),
            status: payment.status === "approved" ? "paid" : payment.status,
            amount: payment.transaction_amount,
            date_approved: payment.date_approved || null,
            metadata: payment.metadata || {},
        };
        logger_1.logger.info("📩 Webhook processado", normalized);
        // Atualizar/gerar Suggestion com base no pagamento aprovado
        if (normalized.status === "paid") {
            const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
            let suggestion = await suggestionRepository.findOne({ where: { paymentId: String(payment.id) } });
            if (suggestion) {
                suggestion.paymentStatus = "paid";
                suggestion.paymentAmount = normalized.amount;
                await suggestionRepository.save(suggestion);
            }
            else {
                // Tentar criar com base nos metadados
                const md = normalized.metadata || {};
                const restaurantId = md.restaurantId;
                const youtubeId = md.youtubeId;
                const title = md.title;
                const artist = md.artist || "Artista Desconhecido";
                const clientName = md.clientName || "Anônimo";
                const clientMessage = md.clientMessage || null;
                const tableNumber = md.tableNumber ?? null;
                const sessionId = md.sessionId || null;
                if (restaurantId && youtubeId && title) {
                    const restaurantRepo = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
                    const restaurant = await restaurantRepo.findOne({ where: { id: restaurantId } });
                    if (restaurant) {
                        suggestion = suggestionRepository.create({
                            youtubeVideoId: youtubeId,
                            title,
                            artist,
                            clientName,
                            clientMessage,
                            tableNumber,
                            clientSessionId: sessionId,
                            status: Suggestion_1.SuggestionStatus.APPROVED,
                            isPriority: true,
                            paymentId: String(payment.id),
                            paymentAmount: normalized.amount,
                            paymentStatus: "paid",
                            restaurant,
                        });
                        await suggestionRepository.save(suggestion);
                    }
                }
            }
        }
        return res.status(200).json({ success: true });
    }
    catch (err) {
        logger_1.logger.error("❌ Erro ao processar webhook:", err);
        return res.status(200).json({ success: true });
    }
}));
/**
 * @swagger
 * /api/v1/payments/test:
 *   post:
 *     summary: Criar pagamento de teste
 *     tags: [Payments]
 *     responses:
 *       201:
 *         description: Pagamento de teste criado
 */
router.post("/test", (0, asyncHandler_1.default)(async (req, res) => {
    // Criar um pagamento de teste com dados mock
    const pixCode = "00020126580014BR.GOV.BCB.PIX0136123e4567-e12b-12d1-a456-426614174000520400005303986540502.005802BR5913Fulano de Tal6008BRASILIA62070503***63041D3D";
    const testPayment = {
        id: `test_${Date.now()}`,
        amount: 2.0,
        status: "pending",
        pixCode,
        qrCodeData: await QRCode.toDataURL(pixCode, {
            errorCorrectionLevel: "M",
            type: "image/png",
            margin: 2,
            color: {
                dark: "#000000",
                light: "#FFFFFF",
            },
            width: 256,
        }),
        description: "Pagamento de teste",
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
    };
    res.status(201).json({
        success: true,
        message: "Pagamento de teste criado",
        payment: testPayment,
        note: "Este é um pagamento de teste. Use apenas para desenvolvimento.",
    });
}));
exports.default = router;
//# sourceMappingURL=payments.js.map