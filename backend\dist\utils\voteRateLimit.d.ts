/**
 * VOTE RATE LIMIT
 * Bloqueia votos normais consecutivos por sessão/cliente dentro de uma janela.
 *
 * Configuração por env:
 * - NORMAL_VOTE_RATE_LIMIT (default: 1)
 * - NORMAL_VOTE_RATE_WINDOW_SECONDS (default: 180)
 */
export interface VoteRateResult {
    allowed: boolean;
    remaining: number;
    retryAfter?: number;
}
/**
 * Aplica rate limit por (restaurantId + clientSessionId OU ip) para votos normais.
 * Retorna allowed=false se exceder o limite.
 */
export declare function checkNormalVoteRateLimit(restaurantId: string, clientSessionId?: string, clientIp?: string): Promise<VoteRateResult>;
declare const _default: {
    checkNormalVoteRateLimit: typeof checkNormalVoteRateLimit;
};
export default _default;
//# sourceMappingURL=voteRateLimit.d.ts.map