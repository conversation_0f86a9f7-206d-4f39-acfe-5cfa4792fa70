{"version": 3, "file": "YouTubeAuthManager-18e1f41f.js", "sources": ["../../src/components/restaurant/YouTubeAuthManager.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Youtube,\r\n  CheckCircle,\r\n  AlertCircle,\r\n  Play,\r\n  TrendingUp,\r\n  RefreshCw,\r\n} from \"lucide-react\";\r\nimport toast from \"react-hot-toast\";\r\nimport { buildApiUrl, getAuthHeaders } from \"../../config/api\";\r\n\r\ntype YouTubeAuthStatus = {\r\n  success?: boolean;\r\n  isAuthenticated: boolean;\r\n  capabilities: string[];\r\n  message: string;\r\n};\r\n\r\ntype PlaylistReorderResult = {\r\n  success: boolean;\r\n  message: string;\r\n  tracksReordered: number;\r\n  newOrder: Array<{\r\n    title: string;\r\n    artist: string;\r\n    voteScore: number;\r\n    upvotes: number;\r\n    downvotes: number;\r\n    positionChange: number;\r\n  }>;\r\n};\r\n\r\ninterface YouTubeAuthManagerProps {\r\n  restaurantId: string;\r\n  onAuthStatusChange?: (isAuthenticated: boolean) => void;\r\n}\r\n\r\nexport const YouTubeAuthManager: React.FC<YouTubeAuthManagerProps> = ({\r\n  restaurantId,\r\n  onAuthStatusChange,\r\n}) => {\r\n  console.log(\r\n    \"🎵 YouTubeAuthManager renderizado com restaurantId:\",\r\n    restaurantId\r\n  );\r\n\r\n  const [authStatus, setAuthStatus] = useState<YouTubeAuthStatus | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [reorderLoading, setReorderLoading] = useState(false);\r\n  const [showCreatePlaylist, setShowCreatePlaylist] = useState(false);\r\n  const [newPlaylistTitle, setNewPlaylistTitle] = useState(\"\");\r\n  const [newPlaylistDescription, setNewPlaylistDescription] = useState(\"\");\r\n\r\n  const checkAuthStatus = useCallback(async () => {\r\n    if (!restaurantId) {\r\n      toast.error(\"Restaurant ID is required\");\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      \"🎵 Verificando status do YouTube para restaurantId:\",\r\n      restaurantId\r\n    );\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        buildApiUrl(`/youtube-auth/${restaurantId}/status`),\r\n        { headers: getAuthHeaders() }\r\n      );\r\n      const data = await response.json();\r\n\r\n      console.log(\"🎵 Resposta da API YouTube Status:\", {\r\n        status: response.status,\r\n        data,\r\n      });\r\n\r\n      if (response.ok && data.success) {\r\n        setAuthStatus(data);\r\n        onAuthStatusChange?.(data.isAuthenticated);\r\n      } else {\r\n        setAuthStatus({\r\n          isAuthenticated: false,\r\n          capabilities: [],\r\n          message: data.message || \"Não autenticado\",\r\n        });\r\n        onAuthStatusChange?.(false);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao verificar status:\", error);\r\n      setAuthStatus({\r\n        isAuthenticated: false,\r\n        capabilities: [],\r\n        message: \"Erro ao verificar status\",\r\n      });\r\n      onAuthStatusChange?.(false);\r\n      toast.error(\"Erro ao verificar status da autenticação\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId, onAuthStatusChange]);\r\n\r\n  const startAuthorization = useCallback(async () => {\r\n    if (!restaurantId) {\r\n      toast.error(\"Restaurant ID is required\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        buildApiUrl(`/youtube-auth/${restaurantId}/authorize`),\r\n        { headers: getAuthHeaders() }\r\n      );\r\n      const data = await response.json();\r\n\r\n      if (data.success && data.authUrl) {\r\n        window.open(data.authUrl, \"youtube-auth\", \"width=600,height=700\");\r\n        toast.success(\"Janela de autorização aberta! Siga as instruções.\");\r\n\r\n        const checkInterval = setInterval(async () => {\r\n          await checkAuthStatus();\r\n          if (authStatus?.isAuthenticated) {\r\n            clearInterval(checkInterval);\r\n            toast.success(\"Autenticação concluída com sucesso!\");\r\n          }\r\n        }, 3000);\r\n\r\n        setTimeout(() => clearInterval(checkInterval), 300000);\r\n      } else {\r\n        toast.error(data.message || \"Erro ao iniciar autorização\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao iniciar autorização:\", error);\r\n      toast.error(\"Erro ao iniciar processo de autorização\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId, authStatus, checkAuthStatus]);\r\n\r\n  const createPlaylist = useCallback(async () => {\r\n    if (!newPlaylistTitle.trim()) {\r\n      toast.error(\"Título da playlist é obrigatório\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await fetch(\r\n        buildApiUrl(`/youtube-auth/${restaurantId}/create-playlist`),\r\n        {\r\n          method: \"POST\",\r\n          headers: getAuthHeaders(\"application/json\"),\r\n          body: JSON.stringify({\r\n            title: newPlaylistTitle,\r\n            description: newPlaylistDescription,\r\n          }),\r\n        }\r\n      );\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        toast.success(\"Playlist criada com sucesso!\");\r\n        setShowCreatePlaylist(false);\r\n        setNewPlaylistTitle(\"\");\r\n        setNewPlaylistDescription(\"\");\r\n\r\n        if (data.playlistUrl) {\r\n          toast.success(\r\n            <div>\r\n              <p>Playlist criada!</p>\r\n              <a\r\n                href={data.playlistUrl}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"text-blue-500 underline\"\r\n              >\r\n                Ver no YouTube\r\n              </a>\r\n            </div>,\r\n            { duration: 5000 }\r\n          );\r\n        }\r\n      } else {\r\n        toast.error(data.message || \"Erro ao criar playlist\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar playlist:\", error);\r\n      toast.error(\"Erro ao criar playlist\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId, newPlaylistTitle, newPlaylistDescription]);\r\n\r\n  const reorderPlaylistByVotes = useCallback(\r\n    async (playlistId: string) => {\r\n      if (!playlistId) {\r\n        toast.error(\"Playlist ID is required\");\r\n        return;\r\n      }\r\n\r\n      setReorderLoading(true);\r\n      try {\r\n    const response = await fetch(\r\n          buildApiUrl(\r\n            `/youtube-auth/${restaurantId}/playlists/${playlistId}/reorder`\r\n          ),\r\n          {\r\n            method: \"POST\",\r\n      headers: getAuthHeaders(),\r\n          }\r\n        );\r\n\r\n        const data: PlaylistReorderResult = await response.json();\r\n\r\n        if (data.success) {\r\n          if (data.tracksReordered > 0) {\r\n            toast.success(\r\n              `${data.tracksReordered} músicas reordenadas baseado nos votos!`\r\n            );\r\n          } else {\r\n            toast.success(\"Playlist já está na ordem ideal!\");\r\n          }\r\n        } else {\r\n          toast.error(data.message || \"Erro ao reordenar playlist\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Erro ao reordenar playlist:\", error);\r\n        toast.error(\"Erro ao reordenar playlist\");\r\n      } finally {\r\n        setReorderLoading(false);\r\n      }\r\n    },\r\n    [restaurantId]\r\n  );\r\n\r\n  useEffect(() => {\r\n    console.log(\r\n      \"🎵 useEffect YouTubeAuthManager executado para restaurantId:\",\r\n      restaurantId\r\n    );\r\n    checkAuthStatus();\r\n    return () => {\r\n      // Cleanup any intervals or pending operations if component unmounts\r\n    };\r\n  }, [checkAuthStatus]);\r\n\r\n  if (loading && !authStatus) {\r\n    return (\r\n      <div className=\"flex items-center justify-center p-8\">\r\n        <RefreshCw className=\"w-6 h-6 animate-spin text-purple-500\" />\r\n        <span className=\"ml-2\">Verificando autenticação...</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center\">\r\n          <Youtube className=\"w-8 h-8 text-red-500 mr-3\" />\r\n          <div>\r\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\r\n              Controle de Playlist YouTube\r\n            </h2>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Gerencie playlists com controle total baseado em votações\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <button\r\n          onClick={checkAuthStatus}\r\n          disabled={loading}\r\n          className=\"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\r\n          aria-label=\"Refresh authentication status\"\r\n        >\r\n          <RefreshCw className={`w-5 h-5 ${loading ? \"animate-spin\" : \"\"}`} />\r\n        </button>\r\n      </div>\r\n\r\n      {authStatus ? (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3 }}\r\n          className=\"space-y-6\"\r\n        >\r\n          <div\r\n            className={`p-4 rounded-lg border-2 ${\r\n              authStatus.isAuthenticated\r\n                ? \"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20\"\r\n                : \"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20\"\r\n            }`}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              {authStatus.isAuthenticated ? (\r\n                <CheckCircle className=\"w-6 h-6 text-green-500 mr-3\" />\r\n              ) : (\r\n                <AlertCircle className=\"w-6 h-6 text-yellow-500 mr-3\" />\r\n              )}\r\n              <div>\r\n                <h3\r\n                  className={`font-semibold ${\r\n                    authStatus.isAuthenticated\r\n                      ? \"text-green-800 dark:text-green-200\"\r\n                      : \"text-yellow-800 dark:text-yellow-200\"\r\n                  }`}\r\n                >\r\n                  {authStatus.isAuthenticated\r\n                    ? \"Autenticado com YouTube\"\r\n                    : \"Não Autenticado\"}\r\n                </h3>\r\n                <p\r\n                  className={`text-sm ${\r\n                    authStatus.isAuthenticated\r\n                      ? \"text-green-600 dark:text-green-300\"\r\n                      : \"text-yellow-600 dark:text-yellow-300\"\r\n                  }`}\r\n                >\r\n                  {authStatus.message}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            {authStatus.capabilities.length > 0 ? (\r\n              authStatus.capabilities.map((capability, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\"\r\n                >\r\n                  <div className=\"w-2 h-2 bg-purple-500 rounded-full mr-3\"></div>\r\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                    {capability}\r\n                  </span>\r\n                </div>\r\n              ))\r\n            ) : (\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Nenhuma capacidade disponível\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap gap-4\">\r\n            {!authStatus.isAuthenticated ? (\r\n              <button\r\n                onClick={startAuthorization}\r\n                disabled={loading}\r\n                className=\"flex items-center px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\r\n              >\r\n                <Youtube className=\"w-5 h-5 mr-2\" />\r\n                {loading ? \"Processando...\" : \"Conectar com YouTube\"}\r\n              </button>\r\n            ) : (\r\n              <>\r\n                <button\r\n                  onClick={() => setShowCreatePlaylist(true)}\r\n                  className=\"flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors\"\r\n                >\r\n                  <Play className=\"w-4 h-4 mr-2\" />\r\n                  Criar Playlist\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => reorderPlaylistByVotes(\"current-playlist-id\")}\r\n                  disabled={reorderLoading}\r\n                  className=\"flex items-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 transition-colors\"\r\n                >\r\n                  <TrendingUp\r\n                    className={`w-4 h-4 mr-2 ${\r\n                      reorderLoading ? \"animate-spin\" : \"\"\r\n                    }`}\r\n                  />\r\n                  {reorderLoading ? \"Reordenando...\" : \"Reordenar por Votos\"}\r\n                </button>\r\n              </>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      ) : (\r\n        <div className=\"p-4 rounded-lg border-2 border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800\">\r\n          <div className=\"flex items-center\">\r\n            <AlertCircle className=\"w-6 h-6 text-gray-500 mr-3\" />\r\n            <div>\r\n              <h3 className=\"font-semibold text-gray-800 dark:text-gray-200\">\r\n                Carregando Status\r\n              </h3>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Verificando conexão com YouTube...\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4\">\r\n            <button\r\n              onClick={checkAuthStatus}\r\n              disabled={loading}\r\n              className=\"flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors\"\r\n            >\r\n              <RefreshCw\r\n                className={`w-4 h-4 mr-2 ${loading ? \"animate-spin\" : \"\"}`}\r\n              />\r\n              {loading ? \"Verificando...\" : \"Tentar Novamente\"}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showCreatePlaylist && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.3 }}\r\n            className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\"\r\n          >\r\n            <h3 className=\"text-lg font-bold text-gray-900 dark:text-white mb-4\">\r\n              Criar Nova Playlist\r\n            </h3>\r\n\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <label\r\n                  htmlFor=\"playlist-title\"\r\n                  className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n                >\r\n                  Título da Playlist *\r\n                </label>\r\n                <input\r\n                  id=\"playlist-title\"\r\n                  type=\"text\"\r\n                  value={newPlaylistTitle}\r\n                  onChange={(e) => setNewPlaylistTitle(e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\"\r\n                  placeholder=\"Ex: Playlist Interativa - Restaurante\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label\r\n                  htmlFor=\"playlist-description\"\r\n                  className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n                >\r\n                  Descrição\r\n                </label>\r\n                <textarea\r\n                  id=\"playlist-description\"\r\n                  value={newPlaylistDescription}\r\n                  onChange={(e) => setNewPlaylistDescription(e.target.value)}\r\n                  rows={3}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white\"\r\n                  placeholder=\"Playlist controlada pelos clientes através de votações...\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex justify-end space-x-3 mt-6\">\r\n              <button\r\n                onClick={() => setShowCreatePlaylist(false)}\r\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\"\r\n              >\r\n                Cancelar\r\n              </button>\r\n              <button\r\n                onClick={createPlaylist}\r\n                disabled={loading || !newPlaylistTitle.trim()}\r\n                className=\"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n              >\r\n                {loading ? \"Criando...\" : \"Criar Playlist\"}\r\n              </button>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": ["YouTubeAuthManager", "restaurantId", "onAuthStatusChange", "authStatus", "setAuthStatus", "useState", "loading", "setLoading", "reorderLoading", "setReorderLoading", "showCreatePlaylist", "setShowCreatePlaylist", "newPlaylistTitle", "setNewPlaylistTitle", "newPlaylistDescription", "setNewPlaylistDescription", "checkAuthStatus", "useCallback", "toast", "response", "buildApiUrl", "getAuthHeaders", "data", "error", "startAuthorization", "checkInterval", "createPlaylist", "jsx", "reorderPlaylistByVotes", "playlistId", "useEffect", "jsxs", "RefreshCw", "Youtube", "motion", "CheckCircle", "AlertCircle", "capability", "index", "Fragment", "Play", "TrendingUp", "e"], "mappings": "uLAuCO,MAAMA,EAAwD,CAAC,CACpE,aAAAC,EACA,mBAAAC,CACF,IAAM,CACI,QAAA,IACN,sDACAD,CAAA,EAGF,KAAM,CAACE,EAAYC,CAAa,EAAIC,WAAmC,IAAI,EACrE,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAK,EACtC,CAACG,EAAgBC,CAAiB,EAAIJ,WAAS,EAAK,EACpD,CAACK,EAAoBC,CAAqB,EAAIN,WAAS,EAAK,EAC5D,CAACO,EAAkBC,CAAmB,EAAIR,WAAS,EAAE,EACrD,CAACS,EAAwBC,CAAyB,EAAIV,WAAS,EAAE,EAEjEW,EAAkBC,EAAAA,YAAY,SAAY,CAC9C,GAAI,CAAChB,EAAc,CACjBiB,EAAM,MAAM,2BAA2B,EACvC,MACF,CAEQ,QAAA,IACN,sDACAjB,CAAA,EAEFM,EAAW,EAAI,EACX,GAAA,CACF,MAAMY,EAAW,MAAM,MACrBC,EAAY,iBAAiBnB,CAAY,SAAS,EAClD,CAAE,QAASoB,GAAiB,CAAA,EAExBC,EAAO,MAAMH,EAAS,OAE5B,QAAQ,IAAI,qCAAsC,CAChD,OAAQA,EAAS,OACjB,KAAAG,CAAA,CACD,EAEGH,EAAS,IAAMG,EAAK,SACtBlB,EAAckB,CAAI,EAClBpB,GAAA,MAAAA,EAAqBoB,EAAK,mBAEZlB,EAAA,CACZ,gBAAiB,GACjB,aAAc,CAAC,EACf,QAASkB,EAAK,SAAW,iBAAA,CAC1B,EACDpB,GAAA,MAAAA,EAAqB,WAEhBqB,EAAO,CACN,QAAA,MAAM,4BAA6BA,CAAK,EAClCnB,EAAA,CACZ,gBAAiB,GACjB,aAAc,CAAC,EACf,QAAS,0BAAA,CACV,EACDF,GAAA,MAAAA,EAAqB,IACrBgB,EAAM,MAAM,0CAA0C,CAAA,QACtD,CACAX,EAAW,EAAK,CAClB,CAAA,EACC,CAACN,EAAcC,CAAkB,CAAC,EAE/BsB,EAAqBP,EAAAA,YAAY,SAAY,CACjD,GAAI,CAAChB,EAAc,CACjBiB,EAAM,MAAM,2BAA2B,EACvC,MACF,CAEAX,EAAW,EAAI,EACX,GAAA,CAKI,MAAAe,EAAO,MAJI,MAAM,MACrBF,EAAY,iBAAiBnB,CAAY,YAAY,EACrD,CAAE,QAASoB,GAAiB,CAAA,GAEF,OAExB,GAAAC,EAAK,SAAWA,EAAK,QAAS,CAChC,OAAO,KAAKA,EAAK,QAAS,eAAgB,sBAAsB,EAChEJ,EAAM,QAAQ,mDAAmD,EAE3D,MAAAO,EAAgB,YAAY,SAAY,CAC5C,MAAMT,EAAgB,EAClBb,GAAA,MAAAA,EAAY,kBACd,cAAcsB,CAAa,EAC3BP,EAAM,QAAQ,qCAAqC,IAEpD,GAAI,EAEP,WAAW,IAAM,cAAcO,CAAa,EAAG,GAAM,CAAA,MAE/CP,EAAA,MAAMI,EAAK,SAAW,6BAA6B,QAEpDC,EAAO,CACN,QAAA,MAAM,+BAAgCA,CAAK,EACnDL,EAAM,MAAM,yCAAyC,CAAA,QACrD,CACAX,EAAW,EAAK,CAClB,CACC,EAAA,CAACN,EAAcE,EAAYa,CAAe,CAAC,EAExCU,EAAiBT,EAAAA,YAAY,SAAY,CACzC,GAAA,CAACL,EAAiB,OAAQ,CAC5BM,EAAM,MAAM,kCAAkC,EAC9C,MACF,CAEAX,EAAW,EAAI,EACX,GAAA,CAaI,MAAAe,EAAO,MAZI,MAAM,MACrBF,EAAY,iBAAiBnB,CAAY,kBAAkB,EAC3D,CACE,OAAQ,OACR,QAASoB,EAAe,kBAAkB,EAC1C,KAAM,KAAK,UAAU,CACnB,MAAOT,EACP,YAAaE,CAAA,CACd,CACH,CAAA,GAG0B,OAExBQ,EAAK,SACPJ,EAAM,QAAQ,8BAA8B,EAC5CP,EAAsB,EAAK,EAC3BE,EAAoB,EAAE,EACtBE,EAA0B,EAAE,EAExBO,EAAK,aACDJ,EAAA,eACH,MACC,CAAA,SAAA,CAAAS,EAAAA,IAAC,KAAE,SAAgB,kBAAA,CAAA,EACnBA,EAAA,IAAC,IAAA,CACC,KAAML,EAAK,YACX,OAAO,SACP,IAAI,sBACJ,UAAU,0BACX,SAAA,gBAAA,CAED,CAAA,EACF,EACA,CAAE,SAAU,GAAK,CAAA,GAIfJ,EAAA,MAAMI,EAAK,SAAW,wBAAwB,QAE/CC,EAAO,CACN,QAAA,MAAM,0BAA2BA,CAAK,EAC9CL,EAAM,MAAM,wBAAwB,CAAA,QACpC,CACAX,EAAW,EAAK,CAClB,CACC,EAAA,CAACN,EAAcW,EAAkBE,CAAsB,CAAC,EAErDc,EAAyBX,EAAA,YAC7B,MAAOY,GAAuB,CAC5B,GAAI,CAACA,EAAY,CACfX,EAAM,MAAM,yBAAyB,EACrC,MACF,CAEAT,EAAkB,EAAI,EAClB,GAAA,CAWI,MAAAa,EAA8B,MAVvB,MAAM,MACjBF,EACE,iBAAiBnB,CAAY,cAAc4B,CAAU,UACvD,EACA,CACE,OAAQ,OACd,QAASR,EAAe,CACpB,CAAA,GAGiD,OAE/CC,EAAK,QACHA,EAAK,gBAAkB,EACnBJ,EAAA,QACJ,GAAGI,EAAK,eAAe,yCAAA,EAGzBJ,EAAM,QAAQ,kCAAkC,EAG5CA,EAAA,MAAMI,EAAK,SAAW,4BAA4B,QAEnDC,EAAO,CACN,QAAA,MAAM,8BAA+BA,CAAK,EAClDL,EAAM,MAAM,4BAA4B,CAAA,QACxC,CACAT,EAAkB,EAAK,CACzB,CACF,EACA,CAACR,CAAY,CAAA,EAcX,OAXJ6B,EAAAA,UAAU,KACA,QAAA,IACN,+DACA7B,CAAA,EAEce,IACT,IAAM,CAAA,GAGZ,CAACA,CAAe,CAAC,EAEhBV,GAAW,CAACH,EAEZ4B,EAAA,KAAC,MAAI,CAAA,UAAU,uCACb,SAAA,CAACJ,EAAAA,IAAAK,EAAA,CAAU,UAAU,sCAAuC,CAAA,EAC3DL,EAAA,IAAA,OAAA,CAAK,UAAU,OAAO,SAA2B,8BAAA,CACpD,CAAA,CAAA,EAKFI,EAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACJ,EAAAA,IAAAM,EAAA,CAAQ,UAAU,2BAA4B,CAAA,SAC9C,MACC,CAAA,SAAA,CAACN,EAAA,IAAA,KAAA,CAAG,UAAU,kDAAkD,SAEhE,+BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,4DAAA,CAAA,EACF,CAAA,EACF,EAEAA,EAAA,IAAC,SAAA,CACC,QAASX,EACT,SAAUV,EACV,UAAU,oFACV,aAAW,gCAEX,eAAC0B,EAAU,CAAA,UAAW,WAAW1B,EAAU,eAAiB,EAAE,GAAI,CAAA,CACpE,CAAA,EACF,EAECH,EACC4B,EAAA,KAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,YAEV,SAAA,CAAAP,EAAA,IAAC,MAAA,CACC,UAAW,2BACTxB,EAAW,gBACP,0EACA,6EACN,GAEA,SAAA4B,EAAA,KAAC,MAAI,CAAA,UAAU,oBACZ,SAAA,CAAW5B,EAAA,sBACTgC,EAAY,CAAA,UAAU,8BAA8B,EAErDR,EAAA,IAACS,EAAY,CAAA,UAAU,8BAA+B,CAAA,SAEvD,MACC,CAAA,SAAA,CAAAT,EAAA,IAAC,KAAA,CACC,UAAW,iBACTxB,EAAW,gBACP,qCACA,sCACN,GAEC,SAAAA,EAAW,gBACR,0BACA,iBAAA,CACN,EACAwB,EAAA,IAAC,IAAA,CACC,UAAW,WACTxB,EAAW,gBACP,qCACA,sCACN,GAEC,SAAWA,EAAA,OAAA,CACd,CAAA,EACF,CAAA,EACF,CAAA,CACF,EAECwB,EAAA,IAAA,MAAA,CAAI,UAAU,wCACZ,SAAWxB,EAAA,aAAa,OAAS,EAChCA,EAAW,aAAa,IAAI,CAACkC,EAAYC,IACvCP,EAAA,KAAC,MAAA,CAEC,UAAU,+DAEV,SAAA,CAACJ,EAAAA,IAAA,MAAA,CAAI,UAAU,yCAA0C,CAAA,EACxDA,EAAA,IAAA,OAAA,CAAK,UAAU,2CACb,SACHU,EAAA,CAAA,CAAA,EANKC,CAAA,CAQR,EAEDX,EAAAA,IAAC,KAAE,UAAU,2CAA2C,wCAExD,CAAA,EAEJ,QAEC,MAAI,CAAA,UAAU,uBACZ,SAACxB,EAAW,gBAWT4B,EAAA,KAAAQ,WAAA,CAAA,SAAA,CAAAR,EAAA,KAAC,SAAA,CACC,QAAS,IAAMpB,EAAsB,EAAI,EACzC,UAAU,wGAEV,SAAA,CAACgB,EAAAA,IAAAa,EAAA,CAAK,UAAU,cAAe,CAAA,EAAE,gBAAA,CAAA,CAEnC,EAEAT,EAAA,KAAC,SAAA,CACC,QAAS,IAAMH,EAAuB,qBAAqB,EAC3D,SAAUpB,EACV,UAAU,0HAEV,SAAA,CAAAmB,EAAA,IAACc,EAAA,CACC,UAAW,gBACTjC,EAAiB,eAAiB,EACpC,EAAA,CACF,EACCA,EAAiB,iBAAmB,qBAAA,CAAA,CACvC,CAAA,CAAA,CACF,EA9BAuB,EAAA,KAAC,SAAA,CACC,QAASP,EACT,SAAUlB,EACV,UAAU,kJAEV,SAAA,CAACqB,EAAAA,IAAAM,EAAA,CAAQ,UAAU,cAAe,CAAA,EACjC3B,EAAU,iBAAmB,sBAAA,CAAA,CAAA,CA0BpC,CAAA,CAAA,CAAA,CAGF,EAAAyB,EAAA,KAAC,MAAI,CAAA,UAAU,2FACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACJ,EAAAA,IAAAS,EAAA,CAAY,UAAU,4BAA6B,CAAA,SACnD,MACC,CAAA,SAAA,CAACT,EAAA,IAAA,KAAA,CAAG,UAAU,iDAAiD,SAE/D,oBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAExD,qCAAA,CAAA,EACF,CAAA,EACF,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,OACb,SAAAI,EAAA,KAAC,SAAA,CACC,QAASf,EACT,SAAUV,EACV,UAAU,wHAEV,SAAA,CAAAqB,EAAA,IAACK,EAAA,CACC,UAAW,gBAAgB1B,EAAU,eAAiB,EAAE,EAAA,CAC1D,EACCA,EAAU,iBAAmB,kBAAA,CAAA,CAAA,EAElC,CAAA,EACF,EAGDI,GACCiB,EAAA,IAAC,MAAI,CAAA,UAAU,6EACb,SAAAI,EAAA,KAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,MAAO,EAAI,EAClC,QAAS,CAAE,QAAS,EAAG,MAAO,CAAE,EAChC,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,gEAEV,SAAA,CAACP,EAAA,IAAA,KAAA,CAAG,UAAU,uDAAuD,SAErE,sBAAA,EAEAI,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAJ,EAAA,IAAC,QAAA,CACC,QAAQ,iBACR,UAAU,kEACX,SAAA,sBAAA,CAED,EACAA,EAAA,IAAC,QAAA,CACC,GAAG,iBACH,KAAK,OACL,MAAOf,EACP,SAAW8B,GAAM7B,EAAoB6B,EAAE,OAAO,KAAK,EACnD,UAAU,8IACV,YAAY,uCAAA,CACd,CAAA,EACF,SAEC,MACC,CAAA,SAAA,CAAAf,EAAA,IAAC,QAAA,CACC,QAAQ,uBACR,UAAU,kEACX,SAAA,WAAA,CAED,EACAA,EAAA,IAAC,WAAA,CACC,GAAG,uBACH,MAAOb,EACP,SAAW4B,GAAM3B,EAA0B2B,EAAE,OAAO,KAAK,EACzD,KAAM,EACN,UAAU,8IACV,YAAY,2DAAA,CACd,CAAA,EACF,CAAA,EACF,EAEAX,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAAAJ,EAAA,IAAC,SAAA,CACC,QAAS,IAAMhB,EAAsB,EAAK,EAC1C,UAAU,0FACX,SAAA,UAAA,CAED,EACAgB,EAAA,IAAC,SAAA,CACC,QAASD,EACT,SAAUpB,GAAW,CAACM,EAAiB,KAAK,EAC5C,UAAU,oHAET,WAAU,aAAe,gBAAA,CAC5B,CAAA,EACF,CAAA,CAAA,CAAA,EAEJ,CAEJ,CAAA,CAAA,CAEJ"}