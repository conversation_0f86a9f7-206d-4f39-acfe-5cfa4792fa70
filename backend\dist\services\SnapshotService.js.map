{"version": 3, "file": "SnapshotService.js", "sourceRoot": "", "sources": ["../../src/services/SnapshotService.ts"], "names": [], "mappings": ";;;AACA,iDAAmD;AACnD,qDAAkD;AAClD,+CAA4C;AAC5C,2DAAwD;AACxD,yDAAsD;AA+BtD,MAAa,eAAe;IAO1B;QAFQ,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAC;QAG1D,IAAI,CAAC,oBAAoB,GAAG,wBAAa,CAAC,aAAa,CAAC,uBAAU,CAAC,CAAC;QACpE,IAAI,CAAC,iBAAiB,GAAG,wBAAa,CAAC,aAAa,CAAC,iBAAO,CAAC,CAAC;QAC9D,IAAI,CAAC,uBAAuB,GAAG,wBAAa,CAAC,aAAa,CAAC,6BAAa,CAAC,CAAC;QAC5E,IAAI,CAAC,gBAAgB,GAAG,mCAAgB,CAAC,WAAW,EAAE,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,YAAoB;QACzC,IAAI;YACF,qDAAqD;YACrD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB;iBAChD,kBAAkB,CAAC,YAAY,CAAC;iBAChC,QAAQ,CAAC,UAAU,EAAE,SAAS,EAAE,mEAAmE,CAAC;iBACpG,SAAS,CAAC;gBACT,oDAAoD;gBACpD,mEAAmE;aACpE,CAAC;iBACD,KAAK,CAAC,0CAA0C,EAAE,EAAE,YAAY,EAAE,CAAC;iBACnE,QAAQ,CAAC,8CAA8C,CAAC;iBACxD,OAAO,CAAC,eAAe,CAAC;iBACxB,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;iBAC1B,UAAU,CAAC,gBAAgB,EAAE,MAAM,CAAC;iBAC1C,UAAU,CAAC,uBAAuB,EAAE,MAAM,CAAC;iBAC3C,UAAU,CAAC,2DAA2D,EAAE,KAAK,CAAC;iBACxE,iBAAiB,EAAE,CAAC;YAEvB,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,IAAI,SAAS,GAAG,IAAI,CAAC;YAErB,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,QAAQ,EAAE;gBAC7C,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC;gBACzE,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE,cAAc,IAAI,GAAG,CAAC,CAAC;gBAC7D,MAAM,MAAM,GAAG,GAAG,EAAE,OAAO,KAAK,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;gBAE1D,MAAM,IAAI,GAAG;oBACX,EAAE,EAAE,UAAU,CAAC,EAAE;oBACjB,cAAc,EAAE,UAAU,CAAC,cAAc;oBACzC,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,MAAM,EAAE,UAAU,CAAC,MAAM,IAAI,sBAAsB;oBACnD,SAAS,EAAE,UAAU,CAAC,SAAS,IAAI,CAAC;oBACpC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,aAAa,CAAC;oBACrD,MAAM;oBACN,aAAa;iBACd,CAAC;gBAEF,IAAI,MAAM,EAAE;oBACV,SAAS,CAAC,IAAI,CAAC;wBACb,GAAG,IAAI;wBACP,aAAa;qBACd,CAAC,CAAC;iBACJ;qBAAM;oBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACtB;gBAED,mDAAmD;gBACnD,IAAI,CAAC,SAAS,EAAE;oBACd,SAAS,GAAG,IAAI,CAAC;iBAClB;aACF;YAED,uDAAuD;YACvD,IAAI,CAAC,SAAS,EAAE;gBACd,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB;qBACjD,kBAAkB,CAAC,OAAO,CAAC;qBAC3B,SAAS,CAAC,gBAAgB,EAAE,UAAU,CAAC;qBACvC,KAAK,CAAC,wCAAwC,EAAE,EAAE,YAAY,EAAE,CAAC;qBACjE,QAAQ,CAAC,wBAAwB,CAAC;qBAClC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC;qBAChC,MAAM,EAAE,CAAC;gBAEZ,IAAI,SAAS,EAAE;oBACb,SAAS,GAAG;wBACV,EAAE,EAAE,SAAS,CAAC,EAAE;wBAChB,cAAc,EAAE,SAAS,CAAC,cAAc;wBACxC,KAAK,EAAE,SAAS,CAAC,KAAK;wBACtB,MAAM,EAAE,SAAS,CAAC,MAAM;wBACxB,KAAK,EAAE,CAAC;wBACR,MAAM,EAAE,KAAK;qBACd,CAAC;iBACH;aACF;YAED,MAAM,QAAQ,GAAoB;gBAChC,YAAY;gBACZ,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,iBAAiB;YACjB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE/C,iDAAiD;YACjD,IAAI,CAAC,gBAAgB,EAAE,qBAAqB,CAAC,YAAY,EAAE,kBAAkB,EAAE;gBACzE,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC1C,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;gBAC1C,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,wCAAwC,YAAY,GAAG,EAAE;gBACnE,SAAS,EAAE,SAAS,EAAE,KAAK,IAAI,MAAM;gBACrC,SAAS,EAAE,SAAS,CAAC,MAAM;gBAC3B,SAAS,EAAE,SAAS,CAAC,MAAM;aAC5B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;SACjB;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,4CAA4C,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;YAClF,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,cAAc,CAAC,UAAsB,EAAE,aAAqB;QAClE,qCAAqC;QACrC,IAAI,aAAa,GAAG,CAAC,EAAE;YACrB,OAAO,KAAK,GAAG,aAAa,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;SAClE;QAED,kDAAkD;QAClD,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,CAAC,CAAC;QAC5C,2CAA2C;QAC3C,MAAM,OAAO,GAAI,UAAkB,CAAC,SAAS,IAAK,UAAkB,CAAC,SAAS,IAAI,IAAI,IAAI,EAAE,CAAC;QAC7F,IAAI,SAAiB,CAAC;QACtB,IAAI;YACF,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,OAAc,CAAC,CAAC,OAAO,EAAE,CAAC;YAC7C,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;SAC1C;QAAC,MAAM;YACN,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SACxB;QACD,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,sBAAsB;QAElE,OAAO,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;IAED,WAAW,CAAC,YAAoB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;QAE1E,WAAW,CAAC,KAAK,IAAI,EAAE;YACrB,IAAI;gBACF,6BAA6B;gBAC7B,MAAM,WAAW,GAAG,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;;;;SAO7C,CAAC,CAAC;gBAEH,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;oBACpC,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;iBAC5C;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;aAChE;QACH,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,YAAY;IACjC,CAAC;IAED,KAAK,CAAC,uCAAuC;QAC3C,IAAI;YACF,MAAM,WAAW,GAAG,MAAM,wBAAa,CAAC,KAAK,CAAC;;;;OAI7C,CAAC,CAAC;YAEH,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE;gBACpC,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;aAC5C;SACF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,CAAC,KAAK,CAAC,iDAAiD,EAAE,KAAK,CAAC,CAAC;SACzE;IACH,CAAC;CACF;AA5LD,0CA4LC;AAED,qBAAqB;AACR,QAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC"}