"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkNormalVoteRateLimit = void 0;
const redis_1 = require("../config/redis");
function getWindowSeconds() {
    const s = Number(process.env.NORMAL_VOTE_RATE_WINDOW_SECONDS || 180);
    return Number.isFinite(s) && s > 0 ? s : 180;
}
function getLimit() {
    const n = Number(process.env.NORMAL_VOTE_RATE_LIMIT || 1);
    return Number.isFinite(n) && n > 0 ? n : 1;
}
/**
 * Aplica rate limit por (restaurantId + clientSessionId OU ip) para votos normais.
 * Retorna allowed=false se exceder o limite.
 */
async function checkNormalVoteRateLimit(restaurantId, clientSessionId, clientIp) {
    const limit = getLimit();
    const windowSec = getWindowSeconds();
    const identity = clientSessionId || clientIp || "anonymous";
    const key = `rate:vote:normal:${restaurantId}:${identity}`;
    const { allowed, remaining } = await redis_1.redisClient.checkRateLimit(key, limit, windowSec);
    if (allowed) {
        return { allowed, remaining };
    }
    // Calcular retryAfter aproximado pelo TTL restante
    try {
        const ttl = await redis_1.redisClient.getClient().ttl(key);
        return { allowed, remaining, retryAfter: ttl > 0 ? ttl : windowSec };
    }
    catch {
        return { allowed, remaining, retryAfter: windowSec };
    }
}
exports.checkNormalVoteRateLimit = checkNormalVoteRateLimit;
exports.default = {
    checkNormalVoteRateLimit,
};
//# sourceMappingURL=voteRateLimit.js.map