{"version": 3, "file": "QRCodeManager-9e14fa3f.js", "sources": ["../../src/components/restaurant/QRCodeManager.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback, memo } from \"react\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport {\r\n  QrCode,\r\n  Plus,\r\n  Download,\r\n  Trash2,\r\n  ExternalLink,\r\n  Copy,\r\n  RefreshCw,\r\n  Users,\r\n  Table,\r\n  Building2,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON>otate<PERSON>w,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport apiService from \"@/services/api\";\r\n\r\nenum QRCodeType {\r\n  Table = \"table\",\r\n  Restaurant = \"restaurant\",\r\n}\r\n\r\ninterface QRCodeData {\r\n  id: string;\r\n  type: QRCodeType;\r\n  name: string;\r\n  url: string;\r\n  qrCodeDataURL: string;\r\n  createdAt: string;\r\n  isActive: boolean;\r\n  tableNumber?: string;\r\n  restaurantId: string;\r\n  restaurant: {\r\n    id: string;\r\n    name: string;\r\n  };\r\n}\r\n\r\n// This is the base64 of a 1x1 transparent PNG - used to detect placeholder images\r\nconst PLACEHOLDER_IMAGE =\r\n  \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==\";\r\n\r\nconst QRCodeManager: React.FC = memo(() => {\r\n  const { restaurantId } = useParams<{ restaurantId: string }>();\r\n  const [qrCodes, setQRCodes] = useState<QRCodeData[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [regenerating, setRegenerating] = useState<Record<string, boolean>>({});\r\n  const [showGenerator, setShowGenerator] = useState(false);\r\n  const [tableNumber, setTableNumber] = useState(\"\");\r\n  const [tableName, setTableName] = useState(\"\");\r\n  const [bulkCount, setBulkCount] = useState(5);\r\n  const [imageLoadErrors, setImageLoadErrors] = useState<\r\n    Record<string, boolean>\r\n  >({});\r\n\r\n  const finalRestaurantId = restaurantId || \"demo-restaurant\";\r\n\r\n  // Open Couvert view in a new tab (mobile-first friendly)\r\n  const openCouvert = useCallback(() => {\r\n    const url = `/couvert/${finalRestaurantId}`;\r\n    window.open(url, \"_blank\", \"noopener,noreferrer\");\r\n  }, [finalRestaurantId]);\r\n\r\n  // Open Display (ranked player) in a new tab as a fallback visualization\r\n  const openDisplay = useCallback(() => {\r\n    const url = `/display/${finalRestaurantId}`;\r\n    window.open(url, \"_blank\", \"noopener,noreferrer\");\r\n  }, [finalRestaurantId]);\r\n\r\n  const loadQRCodes = useCallback(async () => {\r\n    setLoading(true);\r\n    try {\r\n      console.log(\r\n        \"🔍 QR: Carregando QR codes para restaurante:\",\r\n        finalRestaurantId\r\n      );\r\n\r\n      // Use apiService to ensure proper headers and error handling\r\n      const response = await apiService.client.get(\r\n        `/qrcode/${finalRestaurantId}`\r\n      );\r\n\r\n      console.log(\"🔍 QR: Dados recebidos:\", response.data);\r\n\r\n      // Validate QR code data\r\n      const validQRCodes = (response.data.qrCodes || []).filter(\r\n        (qr: QRCodeData) => qr && qr.id && qr.qrCodeDataURL\r\n      );\r\n\r\n      if (validQRCodes.length < (response.data.qrCodes || []).length) {\r\n        console.warn(\r\n          \"🔍 QR: Alguns QR codes foram filtrados por dados inválidos\"\r\n        );\r\n      }\r\n\r\n      // Reset image load errors\r\n      setImageLoadErrors({});\r\n      setQRCodes(validQRCodes);\r\n\r\n      // Check for placeholder images\r\n      const placeholderCount = validQRCodes.filter(\r\n        (qr: QRCodeData) => qr.qrCodeDataURL === PLACEHOLDER_IMAGE\r\n      ).length;\r\n\r\n      if (placeholderCount > 0) {\r\n        console.warn(\r\n          `🔍 QR: ${placeholderCount} QR codes são imagens placeholder`\r\n        );\r\n        if (toast) {\r\n          toast(`${placeholderCount} QR codes precisam ser regenerados`, { icon: \"⚠️\" });\r\n        }\r\n      } else {\r\n        if (toast?.success) {\r\n          toast.success(`${validQRCodes.length} QR codes carregados`);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"🔍 QR: Erro ao carregar QR codes:\", error);\r\n      toast.error(\"Erro ao carregar QR codes\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [finalRestaurantId]);\r\n\r\n  useEffect(() => {\r\n    loadQRCodes();\r\n  }, [loadQRCodes]);\r\n\r\n  const handleImageError = useCallback((qrCodeId: string) => {\r\n    console.error(\r\n      `🔍 QR: Erro ao carregar imagem para QR code ID: ${qrCodeId}`\r\n    );\r\n    setImageLoadErrors((prev) => ({\r\n      ...prev,\r\n      [qrCodeId]: true,\r\n    }));\r\n  }, []);\r\n\r\n  const generateSingleTableQRCode = useCallback(async () => {\r\n    if (!tableNumber.trim()) {\r\n      toast.error(\"Número da mesa é obrigatório\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      console.log(\"🔍 QR: Gerando QR code para mesa:\", tableNumber);\r\n\r\n      // Use apiService for consistency\r\n      const response = await apiService.client.post(\"/qrcode/table\", {\r\n        restaurantId: finalRestaurantId,\r\n        tableNumber: tableNumber.trim(),\r\n        tableName: tableName.trim() || `Mesa ${tableNumber}`,\r\n      });\r\n\r\n      console.log(\"🔍 QR: Resposta da geração:\", response.data);\r\n\r\n      if (response.data.success) {\r\n        // Verify the QR code is not a placeholder\r\n        if (response.data.qrCode?.qrCodeDataURL === PLACEHOLDER_IMAGE) {\r\n          toast(\"QR Code gerado como placeholder, tente novamente\", { icon: \"⚠️\" });\r\n        } else {\r\n          toast.success(\"QR Code gerado com sucesso!\");\r\n          setTableNumber(\"\");\r\n          setTableName(\"\");\r\n          setShowGenerator(false);\r\n          await loadQRCodes();\r\n        }\r\n      } else {\r\n        toast.error(response.data.error || \"Erro ao gerar QR Code\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao gerar QR code:\", error);\r\n      toast.error(\"Erro ao gerar QR Code\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [finalRestaurantId, tableNumber, tableName, loadQRCodes]);\r\n\r\n  const generateBulkQRCodes = useCallback(async () => {\r\n    if (bulkCount < 1 || bulkCount > 50) {\r\n      toast.error(\"Número de mesas deve estar entre 1 e 50\");\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    try {\r\n      const response = await apiService.client.post(\"/qrcode/bulk-tables\", {\r\n        restaurantId: finalRestaurantId,\r\n        tableCount: bulkCount,\r\n        tablePrefix: \"Mesa\",\r\n      });\r\n\r\n      console.log(\"🔍 QR: Resposta da geração em lote:\", response.data);\r\n\r\n      if (response.data.success) {\r\n        toast.success(\r\n          `${response.data.totalGenerated} QR Codes gerados com sucesso!`\r\n        );\r\n        setShowGenerator(false);\r\n        await loadQRCodes();\r\n      } else {\r\n        toast.error(response.data.error || \"Erro ao gerar QR Codes\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao gerar QR codes em lote:\", error);\r\n      toast.error(\"Erro ao gerar QR Codes\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [finalRestaurantId, bulkCount, loadQRCodes]);\r\n\r\n  const generateRestaurantQRCode = useCallback(async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await apiService.client.post(\"/qrcode/restaurant\", {\r\n        restaurantId: finalRestaurantId,\r\n        restaurantName: \"Restaurante Demo\",\r\n      });\r\n\r\n      console.log(\r\n        \"🔍 QR: Resposta da geração do QR do restaurante:\",\r\n        response.data\r\n      );\r\n\r\n      if (response.data.success) {\r\n        // Verify the QR code is not a placeholder\r\n        if (response.data.qrCode?.qrCodeDataURL === PLACEHOLDER_IMAGE) {\r\n          toast(\"QR Code gerado como placeholder, tente novamente\", { icon: \"⚠️\" });\r\n        } else {\r\n          toast.success(\"QR Code do restaurante gerado com sucesso!\");\r\n          setShowGenerator(false);\r\n          await loadQRCodes();\r\n        }\r\n      } else {\r\n        toast.error(response.data.error || \"Erro ao gerar QR Code\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao gerar QR code do restaurante:\", error);\r\n      toast.error(\"Erro ao gerar QR Code\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [finalRestaurantId, loadQRCodes]);\r\n\r\n  // New function to regenerate a QR code\r\n  const regenerateQRCode = useCallback(\r\n    async (qrCode: QRCodeData) => {\r\n      setRegenerating((prev) => ({ ...prev, [qrCode.id]: true }));\r\n\r\n      try {\r\n        let response;\r\n\r\n        if (qrCode.type === QRCodeType.Table) {\r\n          response = await apiService.client.post(\"/qrcode/regenerate/table\", {\r\n            qrCodeId: qrCode.id,\r\n            restaurantId: finalRestaurantId,\r\n            tableNumber: qrCode.tableNumber || \"1\",\r\n            tableName: qrCode.name,\r\n          });\r\n        } else {\r\n          response = await apiService.client.post(\r\n            \"/qrcode/regenerate/restaurant\",\r\n            {\r\n              qrCodeId: qrCode.id,\r\n              restaurantId: finalRestaurantId,\r\n              restaurantName: qrCode.name,\r\n            }\r\n          );\r\n        }\r\n\r\n        console.log(\"🔍 QR: Resposta da regeneração:\", response.data);\r\n\r\n        if (response.data.success) {\r\n          toast.success(\"QR Code regenerado com sucesso!\");\r\n          await loadQRCodes();\r\n        } else {\r\n          toast.error(response.data.error || \"Erro ao regenerar QR Code\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Erro ao regenerar QR code:\", error);\r\n        toast.error(\"Erro ao regenerar QR Code\");\r\n      } finally {\r\n        setRegenerating((prev) => ({ ...prev, [qrCode.id]: false }));\r\n      }\r\n    },\r\n    [finalRestaurantId, loadQRCodes]\r\n  );\r\n\r\n  // Alternative way to generate QR using client-side QR code generation\r\n  const generateClientSideQR = useCallback(\r\n    async (qrCode: QRCodeData) => {\r\n      // Using a dynamic import to load the QR code library only when needed\r\n      try {\r\n        setRegenerating((prev) => ({ ...prev, [qrCode.id]: true }));\r\n\r\n        // Try to use a direct API call to the public QR code generation service\r\n        const qrData = encodeURIComponent(qrCode.url);\r\n        const size = 300;\r\n\r\n        // Generate QR code using a public API service\r\n        const qrServiceUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${qrData}&size=${size}x${size}&margin=10`;\r\n\r\n        // Convert to base64 to match your existing format\r\n        const response = await fetch(qrServiceUrl);\r\n        const blob = await response.blob();\r\n\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(blob);\r\n        reader.onloadend = async () => {\r\n          const base64data = reader.result as string;\r\n\r\n          // Now update the QR code on the server with the new data URL\r\n          try {\r\n            const updateResponse = await apiService.client.post(\r\n              \"/qrcode/update-image\",\r\n              {\r\n                qrCodeId: qrCode.id,\r\n                qrCodeDataURL: base64data,\r\n              }\r\n            );\r\n\r\n            if (updateResponse.data.success) {\r\n              toast.success(\"QR Code regenerado com sucesso!\");\r\n              await loadQRCodes();\r\n            } else {\r\n              toast.error(\"Erro ao atualizar QR Code no servidor\");\r\n            }\r\n          } catch (error) {\r\n            console.error(\"Erro ao atualizar QR code no servidor:\", error);\r\n            toast.error(\"Erro ao atualizar QR Code\");\r\n          }\r\n\r\n          setRegenerating((prev) => ({ ...prev, [qrCode.id]: false }));\r\n        };\r\n      } catch (error) {\r\n        console.error(\"Erro ao gerar QR code localmente:\", error);\r\n        toast.error(\"Erro ao gerar QR Code localmente\");\r\n        setRegenerating((prev) => ({ ...prev, [qrCode.id]: false }));\r\n      }\r\n    },\r\n    [loadQRCodes]\r\n  );\r\n\r\n  const copyToClipboard = useCallback((text: string) => {\r\n    navigator.clipboard.writeText(text);\r\n    toast.success(\"URL copiada para a área de transferência!\");\r\n  }, []);\r\n\r\n  const deleteQRCode = useCallback(\r\n    async (id: string) => {\r\n      if (!window.confirm(\"Tem certeza que deseja deletar este QR Code?\")) {\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const response = await apiService.client.delete(`/qrcode/${id}`);\r\n        if (response.data.success) {\r\n          toast.success(\"QR Code deletado com sucesso!\");\r\n          await loadQRCodes();\r\n        } else {\r\n          toast.error(response.data.error || \"Erro ao deletar QR Code\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Erro ao deletar QR code:\", error);\r\n        toast.error(\"Erro ao deletar QR Code\");\r\n      }\r\n    },\r\n    [loadQRCodes]\r\n  );\r\n\r\n  const downloadQRCode = useCallback((qrCode: QRCodeData) => {\r\n    // Check if QR code data URL is valid and not a placeholder\r\n    if (!qrCode.qrCodeDataURL || qrCode.qrCodeDataURL === PLACEHOLDER_IMAGE) {\r\n      toast.error(\"Este QR Code precisa ser regenerado antes de baixar\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const link = document.createElement(\"a\");\r\n      link.href = qrCode.qrCodeDataURL;\r\n      link.download = `${qrCode.name}-${qrCode.id}.png`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      toast.success(\"QR Code baixado com sucesso!\");\r\n    } catch (error) {\r\n      console.error(\"Erro ao fazer download do QR code:\", error);\r\n      toast.error(\"Erro ao baixar QR Code\");\r\n    }\r\n  }, []);\r\n\r\n  // Function to render QR code image or fallback\r\n  const renderQRCodeImage = useCallback(\r\n    (qrCode: QRCodeData) => {\r\n      const hasError = imageLoadErrors[qrCode.id];\r\n      const isPlaceholder = qrCode.qrCodeDataURL === PLACEHOLDER_IMAGE;\r\n      const isRegenerating = regenerating[qrCode.id];\r\n\r\n      if (hasError || !qrCode.qrCodeDataURL || isPlaceholder) {\r\n        return (\r\n          <div className=\"w-32 h-32 mx-auto flex flex-col items-center justify-center bg-gray-100 dark:bg-gray-700 rounded-lg mb-3\">\r\n            <div className=\"text-center\">\r\n              <AlertTriangle className=\"w-8 h-8 text-yellow-500 mx-auto mb-2\" />\r\n              <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                QR indisponível\r\n              </span>\r\n              <button\r\n                onClick={() => generateClientSideQR(qrCode)}\r\n                disabled={isRegenerating}\r\n                className=\"mt-2 px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-1 mx-auto\"\r\n              >\r\n                {isRegenerating ? (\r\n                  <RefreshCw className=\"w-3 h-3 animate-spin\" />\r\n                ) : (\r\n                  <RotateCw className=\"w-3 h-3\" />\r\n                )}\r\n                <span>Regenerar</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        );\r\n      }\r\n\r\n      return (\r\n        <img\r\n          src={qrCode.qrCodeDataURL}\r\n          alt={`QR Code para ${qrCode.name}`}\r\n          className=\"w-32 h-32 mx-auto rounded-lg mb-3\"\r\n          loading=\"lazy\"\r\n          onError={() => handleImageError(qrCode.id)}\r\n        />\r\n      );\r\n    },\r\n    [imageLoadErrors, regenerating, handleImageError, generateClientSideQR]\r\n  );\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            QR Codes\r\n          </h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Gerencie os QR Codes para acesso dos clientes\r\n          </p>\r\n        </div>\r\n        <div className=\"flex flex-wrap items-center gap-2\">\r\n          <button\r\n            onClick={openCouvert}\r\n            onKeyDown={(e) => e.key === \"Enter\" && openCouvert()}\r\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n            aria-label=\"Abrir Couvert do restaurante em nova aba\"\r\n          >\r\n            <ExternalLink className=\"w-4 h-4\" />\r\n            <span className=\"uppercase\">COUVERT</span>\r\n          </button>\r\n          <button\r\n            onClick={openDisplay}\r\n            onKeyDown={(e) => e.key === \"Enter\" && openDisplay()}\r\n            className=\"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\r\n            aria-label=\"Abrir Display do restaurante em nova aba\"\r\n          >\r\n            <ExternalLink className=\"w-4 h-4\" />\r\n            <span className=\"uppercase\">DISPLAY</span>\r\n          </button>\r\n          <button\r\n            onClick={() => setShowGenerator(true)}\r\n            onKeyDown={(e) => e.key === \"Enter\" && setShowGenerator(true)}\r\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n            aria-label=\"Gerar novo QR Code\"\r\n          >\r\n            <Plus className=\"w-4 h-4\" />\r\n            <span>Gerar QR Code</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Generator Modal */}\r\n      <AnimatePresence>\r\n        {showGenerator && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\"\r\n          >\r\n            <motion.div\r\n              initial={{ scale: 0.9, opacity: 0 }}\r\n              animate={{ scale: 1, opacity: 1 }}\r\n              exit={{ scale: 0.9, opacity: 0 }}\r\n              transition={{ duration: 0.2 }}\r\n              className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\"\r\n            >\r\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n                Gerar QR Code\r\n              </h3>\r\n              <div className=\"space-y-4\">\r\n                {/* Single Table */}\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\r\n                    Mesa Individual\r\n                  </h4>\r\n                  <div className=\"space-y-3\">\r\n                    <input\r\n                      type=\"text\"\r\n                      value={tableNumber}\r\n                      onChange={(e) => setTableNumber(e.target.value)}\r\n                      placeholder=\"Número da mesa (ex: 1, 2, 3...)\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      aria-label=\"Número da mesa\"\r\n                    />\r\n                    <input\r\n                      type=\"text\"\r\n                      value={tableName}\r\n                      onChange={(e) => setTableName(e.target.value)}\r\n                      placeholder=\"Nome da mesa (opcional)\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      aria-label=\"Nome da mesa\"\r\n                    />\r\n                    <button\r\n                      onClick={generateSingleTableQRCode}\r\n                      disabled={loading || !tableNumber.trim()}\r\n                      className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors\"\r\n                      aria-label=\"Gerar QR Code para mesa individual\"\r\n                    >\r\n                      {loading ? (\r\n                        <RefreshCw className=\"w-4 h-4 animate-spin\" />\r\n                      ) : (\r\n                        <Table className=\"w-4 h-4\" />\r\n                      )}\r\n                      <span>Gerar Mesa</span>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Bulk Tables */}\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\r\n                    Múltiplas Mesas\r\n                  </h4>\r\n                  <div className=\"space-y-3\">\r\n                    <input\r\n                      type=\"number\"\r\n                      value={bulkCount}\r\n                      onChange={(e) =>\r\n                        setBulkCount(\r\n                          Math.max(\r\n                            1,\r\n                            Math.min(50, parseInt(e.target.value) || 1)\r\n                          )\r\n                        )\r\n                      }\r\n                      min=\"1\"\r\n                      max=\"50\"\r\n                      placeholder=\"Quantidade de mesas\"\r\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                      aria-label=\"Quantidade de mesas\"\r\n                    />\r\n                    <button\r\n                      onClick={generateBulkQRCodes}\r\n                      disabled={loading}\r\n                      className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors\"\r\n                      aria-label={`Gerar ${bulkCount} QR Codes para mesas`}\r\n                    >\r\n                      {loading ? (\r\n                        <RefreshCw className=\"w-4 h-4 animate-spin\" />\r\n                      ) : (\r\n                        <Users className=\"w-4 h-4\" />\r\n                      )}\r\n                      <span>Gerar {bulkCount} Mesas</span>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Restaurant QR Code */}\r\n                <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\r\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\r\n                    QR Code Geral do Restaurante\r\n                  </h4>\r\n                  <button\r\n                    onClick={generateRestaurantQRCode}\r\n                    disabled={loading}\r\n                    className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center justify-center space-x-2 transition-colors\"\r\n                    aria-label=\"Gerar QR Code geral do restaurante\"\r\n                  >\r\n                    {loading ? (\r\n                      <RefreshCw className=\"w-4 h-4 animate-spin\" />\r\n                    ) : (\r\n                      <Building2 className=\"w-4 h-4\" />\r\n                    )}\r\n                    <span>Gerar QR Geral</span>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex space-x-3 mt-6\">\r\n                <button\r\n                  onClick={() => setShowGenerator(false)}\r\n                  onKeyDown={(e) =>\r\n                    e.key === \"Enter\" && setShowGenerator(false)\r\n                  }\r\n                  className=\"flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors\"\r\n                  aria-label=\"Cancelar geração de QR Code\"\r\n                >\r\n                  Cancelar\r\n                </button>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n\r\n      {/* QR Codes List */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\r\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-700\">\r\n          <div className=\"flex justify-between items-center\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\r\n              QR Codes Criados\r\n            </h3>\r\n            <button\r\n              onClick={loadQRCodes}\r\n              onKeyDown={(e) => e.key === \"Enter\" && loadQRCodes()}\r\n              className=\"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1 transition-colors\"\r\n              aria-label=\"Atualizar lista de QR Codes\"\r\n            >\r\n              <RefreshCw\r\n                className={`w-4 h-4 ${loading ? \"animate-spin\" : \"\"}`}\r\n              />\r\n              <span>{loading ? \"Carregando...\" : \"Atualizar\"}</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div className=\"p-6\">\r\n          {loading && qrCodes.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <RefreshCw className=\"w-12 h-12 text-gray-400 mx-auto mb-4 animate-spin\" />\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n                Carregando QR Codes...\r\n              </h3>\r\n            </div>\r\n          ) : qrCodes.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <QrCode className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n                Nenhum QR Code criado\r\n              </h3>\r\n              <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\r\n                Crie QR Codes para que os clientes possam acessar o sistema\r\n              </p>\r\n              <button\r\n                onClick={() => setShowGenerator(true)}\r\n                onKeyDown={(e) => e.key === \"Enter\" && setShowGenerator(true)}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n                aria-label=\"Criar primeiro QR Code\"\r\n              >\r\n                Criar Primeiro QR Code\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n              {qrCodes.map((qrCode) => (\r\n                <motion.div\r\n                  key={qrCode.id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.3 }}\r\n                  className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\"\r\n                >\r\n                  <div className=\"text-center mb-4\">\r\n                    {renderQRCodeImage(qrCode)}\r\n                    <h4 className=\"font-medium text-gray-900 dark:text-white truncate\">\r\n                      {qrCode.name}\r\n                    </h4>\r\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                      {qrCode.type === QRCodeType.Table\r\n                        ? \"Mesa\"\r\n                        : \"Restaurante\"}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400 break-all\">\r\n                      {qrCode.url}\r\n                    </div>\r\n                    <div className=\"flex space-x-2\">\r\n                      <button\r\n                        onClick={() => window.open(qrCode.url, \"_blank\")}\r\n                        onKeyDown={(e) =>\r\n                          e.key === \"Enter\" && window.open(qrCode.url, \"_blank\")\r\n                        }\r\n                        className=\"flex-1 px-2 py-1 bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 rounded text-xs hover:bg-blue-200 dark:hover:bg-blue-900/30 flex items-center justify-center space-x-1 transition-colors\"\r\n                        aria-label={`Testar QR Code ${qrCode.name}`}\r\n                      >\r\n                        <ExternalLink className=\"w-3 h-3\" />\r\n                        <span>Testar</span>\r\n                      </button>\r\n                      <button\r\n                        onClick={() => copyToClipboard(qrCode.url)}\r\n                        onKeyDown={(e) =>\r\n                          e.key === \"Enter\" && copyToClipboard(qrCode.url)\r\n                        }\r\n                        className=\"flex-1 px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400 rounded text-xs hover:bg-green-200 dark:hover:bg-green-900/30 flex items-center justify-center space-x-1 transition-colors\"\r\n                        aria-label={`Copiar URL do QR Code ${qrCode.name}`}\r\n                      >\r\n                        <Copy className=\"w-3 h-3\" />\r\n                        <span>Copiar</span>\r\n                      </button>\r\n                      <button\r\n                        onClick={() => downloadQRCode(qrCode)}\r\n                        onKeyDown={(e) =>\r\n                          e.key === \"Enter\" && downloadQRCode(qrCode)\r\n                        }\r\n                        className=\"flex-1 px-2 py-1 bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 rounded text-xs hover:bg-purple-200 dark:hover:bg-purple-900/30 flex items-center justify-center space-x-1 transition-colors\"\r\n                        aria-label={`Baixar QR Code ${qrCode.name}`}\r\n                        disabled={qrCode.qrCodeDataURL === PLACEHOLDER_IMAGE}\r\n                      >\r\n                        <Download className=\"w-3 h-3\" />\r\n                        <span>Baixar</span>\r\n                      </button>\r\n                      <button\r\n                        onClick={() => deleteQRCode(qrCode.id)}\r\n                        onKeyDown={(e) =>\r\n                          e.key === \"Enter\" && deleteQRCode(qrCode.id)\r\n                        }\r\n                        className=\"px-2 py-1 bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400 rounded text-xs hover:bg-red-200 dark:hover:bg-red-900/30 transition-colors\"\r\n                        aria-label={`Deletar QR Code ${qrCode.name}`}\r\n                      >\r\n                        <Trash2 className=\"w-3 h-3\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\nexport default QRCodeManager;\r\n"], "names": ["PLACEHOLDER_IMAGE", "QRCodeManager", "memo", "restaurantId", "useParams", "qrCodes", "setQRCodes", "useState", "loading", "setLoading", "regenerating", "setRegenerating", "showGenerator", "setShowGenerator", "tableNumber", "setTableNumber", "tableName", "setTableName", "bulkCount", "setBulkCount", "imageLoadErrors", "setImageLoadErrors", "finalRestaurantId", "openCouvert", "useCallback", "url", "openDisplay", "loadQRCodes", "response", "apiService", "validQRCodes", "qr", "placeholderCount", "toast", "error", "useEffect", "handleImageError", "qrCodeId", "prev", "generateSingleTableQRCode", "_a", "generateBulkQRCodes", "generateRestaurantQRCode", "qrCode", "generateClientSideQR", "qrData", "size", "qrServiceUrl", "blob", "reader", "base64data", "copyToClipboard", "text", "deleteQRCode", "id", "downloadQRCode", "link", "renderQRCodeImage", "<PERSON><PERSON><PERSON><PERSON>", "isPlaceholder", "isRegenerating", "jsxs", "jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RefreshCw", "RotateCw", "ExternalLink", "Plus", "AnimatePresence", "motion", "Table", "Users", "Building2", "QrCode", "e", "Copy", "Download", "Trash2"], "mappings": "6SA0CA,MAAMA,EACJ,yHAEIC,GAA0BC,OAAK,IAAM,CACnC,KAAA,CAAE,aAAAC,GAAiBC,IACnB,CAACC,EAASC,CAAU,EAAIC,EAAA,SAAuB,CAAE,CAAA,EACjD,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAK,EACtC,CAACG,EAAcC,CAAe,EAAIJ,EAAA,SAAkC,CAAE,CAAA,EACtE,CAACK,EAAeC,CAAgB,EAAIN,WAAS,EAAK,EAClD,CAACO,EAAaC,CAAc,EAAIR,WAAS,EAAE,EAC3C,CAACS,EAAWC,CAAY,EAAIV,WAAS,EAAE,EACvC,CAACW,EAAWC,CAAY,EAAIZ,WAAS,CAAC,EACtC,CAACa,EAAiBC,CAAkB,EAAId,EAAA,SAE5C,CAAE,CAAA,EAEEe,EAAoBnB,GAAgB,kBAGpCoB,EAAcC,EAAAA,YAAY,IAAM,CAC9B,MAAAC,EAAM,YAAYH,CAAiB,GAClC,OAAA,KAAKG,EAAK,SAAU,qBAAqB,CAAA,EAC/C,CAACH,CAAiB,CAAC,EAGhBI,EAAcF,EAAAA,YAAY,IAAM,CAC9B,MAAAC,EAAM,YAAYH,CAAiB,GAClC,OAAA,KAAKG,EAAK,SAAU,qBAAqB,CAAA,EAC/C,CAACH,CAAiB,CAAC,EAEhBK,EAAcH,EAAAA,YAAY,SAAY,OAC1Cf,EAAW,EAAI,EACX,GAAA,CACM,QAAA,IACN,+CACAa,CAAA,EAII,MAAAM,EAAW,MAAMC,EAAW,OAAO,IACvC,WAAWP,CAAiB,EAAA,EAGtB,QAAA,IAAI,0BAA2BM,EAAS,IAAI,EAGpD,MAAME,GAAgBF,EAAS,KAAK,SAAW,CAAI,GAAA,OAChDG,GAAmBA,GAAMA,EAAG,IAAMA,EAAG,aAAA,EAGpCD,EAAa,QAAUF,EAAS,KAAK,SAAW,IAAI,QAC9C,QAAA,KACN,4DAAA,EAKJP,EAAmB,CAAE,CAAA,EACrBf,EAAWwB,CAAY,EAGvB,MAAME,EAAmBF,EAAa,OACnCC,GAAmBA,EAAG,gBAAkB/B,CACzC,EAAA,OAEEgC,EAAmB,GACb,QAAA,KACN,UAAUA,CAAgB,mCAAA,EAExBC,GACFA,EAAM,GAAGD,CAAgB,qCAAsC,CAAE,KAAM,KAAM,IAG3EC,EAAAA,IAAAA,MAAAA,EAAO,SACTA,EAAM,QAAQ,GAAGH,EAAa,MAAM,sBAAsB,QAGvDI,EAAO,CACN,QAAA,MAAM,oCAAqCA,CAAK,EACxDD,EAAM,MAAM,2BAA2B,CAAA,QACvC,CACAxB,EAAW,EAAK,CAClB,CAAA,EACC,CAACa,CAAiB,CAAC,EAEtBa,EAAAA,UAAU,IAAM,CACFR,GAAA,EACX,CAACA,CAAW,CAAC,EAEV,MAAAS,EAAmBZ,cAAaa,GAAqB,CACjD,QAAA,MACN,mDAAmDA,CAAQ,EAAA,EAE7DhB,EAAoBiB,IAAU,CAC5B,GAAGA,EACH,CAACD,CAAQ,EAAG,EACZ,EAAA,CACJ,EAAG,CAAE,CAAA,EAECE,EAA4Bf,EAAAA,YAAY,SAAY,OACpD,GAAA,CAACV,EAAY,OAAQ,CACvBmB,EAAM,MAAM,8BAA8B,EAC1C,MACF,CAEAxB,EAAW,EAAI,EACX,GAAA,CACM,QAAA,IAAI,oCAAqCK,CAAW,EAG5D,MAAMc,EAAW,MAAMC,EAAW,OAAO,KAAK,gBAAiB,CAC7D,aAAcP,EACd,YAAaR,EAAY,KAAK,EAC9B,UAAWE,EAAU,KAAK,GAAK,QAAQF,CAAW,EAAA,CACnD,EAEO,QAAA,IAAI,8BAA+Bc,EAAS,IAAI,EAEpDA,EAAS,KAAK,UAEZY,EAAAZ,EAAS,KAAK,SAAd,YAAAY,EAAsB,iBAAkBxC,EAC1CiC,EAAM,mDAAoD,CAAE,KAAM,IAAM,CAAA,GAExEA,EAAM,QAAQ,6BAA6B,EAC3ClB,EAAe,EAAE,EACjBE,EAAa,EAAE,EACfJ,EAAiB,EAAK,EACtB,MAAMc,EAAY,GAGpBM,EAAM,MAAML,EAAS,KAAK,OAAS,uBAAuB,QAErDM,EAAO,CACN,QAAA,MAAM,yBAA0BA,CAAK,EAC7CD,EAAM,MAAM,uBAAuB,CAAA,QACnC,CACAxB,EAAW,EAAK,CAClB,GACC,CAACa,EAAmBR,EAAaE,EAAWW,CAAW,CAAC,EAErDc,EAAsBjB,EAAAA,YAAY,SAAY,CAC9C,GAAAN,EAAY,GAAKA,EAAY,GAAI,CACnCe,EAAM,MAAM,yCAAyC,EACrD,MACF,CAEAxB,EAAW,EAAI,EACX,GAAA,CACF,MAAMmB,EAAW,MAAMC,EAAW,OAAO,KAAK,sBAAuB,CACnE,aAAcP,EACd,WAAYJ,EACZ,YAAa,MAAA,CACd,EAEO,QAAA,IAAI,sCAAuCU,EAAS,IAAI,EAE5DA,EAAS,KAAK,SACVK,EAAA,QACJ,GAAGL,EAAS,KAAK,cAAc,gCAAA,EAEjCf,EAAiB,EAAK,EACtB,MAAMc,EAAY,GAElBM,EAAM,MAAML,EAAS,KAAK,OAAS,wBAAwB,QAEtDM,EAAO,CACN,QAAA,MAAM,kCAAmCA,CAAK,EACtDD,EAAM,MAAM,wBAAwB,CAAA,QACpC,CACAxB,EAAW,EAAK,CAClB,CACC,EAAA,CAACa,EAAmBJ,EAAWS,CAAW,CAAC,EAExCe,EAA2BlB,EAAAA,YAAY,SAAY,OACvDf,EAAW,EAAI,EACX,GAAA,CACF,MAAMmB,EAAW,MAAMC,EAAW,OAAO,KAAK,qBAAsB,CAClE,aAAcP,EACd,eAAgB,kBAAA,CACjB,EAEO,QAAA,IACN,mDACAM,EAAS,IAAA,EAGPA,EAAS,KAAK,UAEZY,EAAAZ,EAAS,KAAK,SAAd,YAAAY,EAAsB,iBAAkBxC,EAC1CiC,EAAM,mDAAoD,CAAE,KAAM,IAAM,CAAA,GAExEA,EAAM,QAAQ,4CAA4C,EAC1DpB,EAAiB,EAAK,EACtB,MAAMc,EAAY,GAGpBM,EAAM,MAAML,EAAS,KAAK,OAAS,uBAAuB,QAErDM,EAAO,CACN,QAAA,MAAM,wCAAyCA,CAAK,EAC5DD,EAAM,MAAM,uBAAuB,CAAA,QACnC,CACAxB,EAAW,EAAK,CAClB,CAAA,EACC,CAACa,EAAmBK,CAAW,CAAC,EAGVH,EAAA,YACvB,MAAOmB,GAAuB,CACZhC,EAAC2B,IAAU,CAAE,GAAGA,EAAM,CAACK,EAAO,EAAE,EAAG,EAAO,EAAA,EAEtD,GAAA,CACE,IAAAf,EAEAe,EAAO,OAAS,QAClBf,EAAW,MAAMC,EAAW,OAAO,KAAK,2BAA4B,CAClE,SAAUc,EAAO,GACjB,aAAcrB,EACd,YAAaqB,EAAO,aAAe,IACnC,UAAWA,EAAO,IAAA,CACnB,EAEUf,EAAA,MAAMC,EAAW,OAAO,KACjC,gCACA,CACE,SAAUc,EAAO,GACjB,aAAcrB,EACd,eAAgBqB,EAAO,IACzB,CAAA,EAII,QAAA,IAAI,kCAAmCf,EAAS,IAAI,EAExDA,EAAS,KAAK,SAChBK,EAAM,QAAQ,iCAAiC,EAC/C,MAAMN,EAAY,GAElBM,EAAM,MAAML,EAAS,KAAK,OAAS,2BAA2B,QAEzDM,EAAO,CACN,QAAA,MAAM,6BAA8BA,CAAK,EACjDD,EAAM,MAAM,2BAA2B,CAAA,QACvC,CACgBtB,EAAC2B,IAAU,CAAE,GAAGA,EAAM,CAACK,EAAO,EAAE,EAAG,EAAQ,EAAA,CAC7D,CACF,EACA,CAACrB,EAAmBK,CAAW,CACjC,EAGA,MAAMiB,EAAuBpB,EAAA,YAC3B,MAAOmB,GAAuB,CAExB,GAAA,CACchC,EAAC2B,IAAU,CAAE,GAAGA,EAAM,CAACK,EAAO,EAAE,EAAG,EAAO,EAAA,EAGpD,MAAAE,EAAS,mBAAmBF,EAAO,GAAG,EACtCG,EAAO,IAGPC,EAAe,oDAAoDF,CAAM,SAASC,CAAI,IAAIA,CAAI,aAI9FE,EAAO,MADI,MAAM,MAAMD,CAAY,GACb,OAEtBE,EAAS,IAAI,WACnBA,EAAO,cAAcD,CAAI,EACzBC,EAAO,UAAY,SAAY,CAC7B,MAAMC,EAAaD,EAAO,OAGtB,GAAA,EACqB,MAAMpB,EAAW,OAAO,KAC7C,uBACA,CACE,SAAUc,EAAO,GACjB,cAAeO,CACjB,CAAA,GAGiB,KAAK,SACtBjB,EAAM,QAAQ,iCAAiC,EAC/C,MAAMN,EAAY,GAElBM,EAAM,MAAM,uCAAuC,QAE9CC,EAAO,CACN,QAAA,MAAM,yCAA0CA,CAAK,EAC7DD,EAAM,MAAM,2BAA2B,CACzC,CAEgBtB,EAAC2B,IAAU,CAAE,GAAGA,EAAM,CAACK,EAAO,EAAE,EAAG,EAAQ,EAAA,CAAA,QAEtDT,EAAO,CACN,QAAA,MAAM,oCAAqCA,CAAK,EACxDD,EAAM,MAAM,kCAAkC,EAC9BtB,EAAC2B,IAAU,CAAE,GAAGA,EAAM,CAACK,EAAO,EAAE,EAAG,EAAQ,EAAA,CAC7D,CACF,EACA,CAAChB,CAAW,CAAA,EAGRwB,EAAkB3B,cAAa4B,GAAiB,CAC1C,UAAA,UAAU,UAAUA,CAAI,EAClCnB,EAAM,QAAQ,2CAA2C,CAC3D,EAAG,CAAE,CAAA,EAECoB,EAAe7B,EAAA,YACnB,MAAO8B,GAAe,CACpB,GAAK,OAAO,QAAQ,8CAA8C,EAI9D,GAAA,CACF,MAAM1B,EAAW,MAAMC,EAAW,OAAO,OAAO,WAAWyB,CAAE,EAAE,EAC3D1B,EAAS,KAAK,SAChBK,EAAM,QAAQ,+BAA+B,EAC7C,MAAMN,EAAY,GAElBM,EAAM,MAAML,EAAS,KAAK,OAAS,yBAAyB,QAEvDM,EAAO,CACN,QAAA,MAAM,2BAA4BA,CAAK,EAC/CD,EAAM,MAAM,yBAAyB,CACvC,CACF,EACA,CAACN,CAAW,CAAA,EAGR4B,EAAiB/B,cAAamB,GAAuB,CAEzD,GAAI,CAACA,EAAO,eAAiBA,EAAO,gBAAkB3C,EAAmB,CACvEiC,EAAM,MAAM,qDAAqD,EACjE,MACF,CAEI,GAAA,CACI,MAAAuB,EAAO,SAAS,cAAc,GAAG,EACvCA,EAAK,KAAOb,EAAO,cACnBa,EAAK,SAAW,GAAGb,EAAO,IAAI,IAAIA,EAAO,EAAE,OAClC,SAAA,KAAK,YAAYa,CAAI,EAC9BA,EAAK,MAAM,EACF,SAAA,KAAK,YAAYA,CAAI,EAC9BvB,EAAM,QAAQ,8BAA8B,QACrCC,EAAO,CACN,QAAA,MAAM,qCAAsCA,CAAK,EACzDD,EAAM,MAAM,wBAAwB,CACtC,CACF,EAAG,CAAE,CAAA,EAGCwB,EAAoBjC,EAAA,YACvBmB,GAAuB,CAChB,MAAAe,EAAWtC,EAAgBuB,EAAO,EAAE,EACpCgB,EAAgBhB,EAAO,gBAAkB3C,EACzC4D,EAAiBlD,EAAaiC,EAAO,EAAE,EAE7C,OAAIe,GAAY,CAACf,EAAO,eAAiBgB,QAEpC,MAAI,CAAA,UAAU,2GACb,SAACE,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAc,UAAU,sCAAuC,CAAA,EAC/DD,EAAA,IAAA,OAAA,CAAK,UAAU,2CAA2C,SAE3D,kBAAA,EACAD,EAAA,KAAC,SAAA,CACC,QAAS,IAAMjB,EAAqBD,CAAM,EAC1C,SAAUiB,EACV,UAAU,iJAET,SAAA,CACCA,EAAAE,EAAA,IAACE,GAAU,UAAU,sBAAA,CAAuB,EAE3CF,EAAAA,IAAAG,EAAA,CAAS,UAAU,SAAU,CAAA,EAEhCH,EAAAA,IAAC,QAAK,SAAS,WAAA,CAAA,CAAA,CAAA,CACjB,CAAA,CACF,CAAA,CACF,CAAA,EAKFA,EAAA,IAAC,MAAA,CACC,IAAKnB,EAAO,cACZ,IAAK,gBAAgBA,EAAO,IAAI,GAChC,UAAU,oCACV,QAAQ,OACR,QAAS,IAAMP,EAAiBO,EAAO,EAAE,CAAA,CAAA,CAG/C,EACA,CAACvB,EAAiBV,EAAc0B,EAAkBQ,CAAoB,CAAA,EAItE,OAAAiB,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAEjE,WAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,gDAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAAStC,EACT,UAAY,GAAM,EAAE,MAAQ,SAAWA,EAAY,EACnD,UAAU,8GACV,aAAW,2CAEX,SAAA,CAACuC,EAAAA,IAAAI,EAAA,CAAa,UAAU,SAAU,CAAA,EACjCJ,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAO,UAAA,CAAA,CAAA,CACrC,EACAD,EAAA,KAAC,SAAA,CACC,QAASnC,EACT,UAAY,GAAM,EAAE,MAAQ,SAAWA,EAAY,EACnD,UAAU,kHACV,aAAW,2CAEX,SAAA,CAACoC,EAAAA,IAAAI,EAAA,CAAa,UAAU,SAAU,CAAA,EACjCJ,EAAA,IAAA,OAAA,CAAK,UAAU,YAAY,SAAO,UAAA,CAAA,CAAA,CACrC,EACAD,EAAA,KAAC,SAAA,CACC,QAAS,IAAMhD,EAAiB,EAAI,EACpC,UAAY,GAAM,EAAE,MAAQ,SAAWA,EAAiB,EAAI,EAC5D,UAAU,8GACV,aAAW,qBAEX,SAAA,CAACiD,EAAAA,IAAAK,EAAA,CAAK,UAAU,SAAU,CAAA,EAC1BL,EAAAA,IAAC,QAAK,SAAa,eAAA,CAAA,CAAA,CAAA,CACrB,CAAA,EACF,CAAA,EACF,EAGAA,EAAAA,IAACM,GACE,SACCxD,GAAAkD,EAAA,IAACO,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,CAAE,EACtB,QAAS,CAAE,QAAS,CAAE,EACtB,KAAM,CAAE,QAAS,CAAE,EACnB,UAAU,6EAEV,SAAAR,EAAA,KAACQ,EAAO,IAAP,CACC,QAAS,CAAE,MAAO,GAAK,QAAS,CAAE,EAClC,QAAS,CAAE,MAAO,EAAG,QAAS,CAAE,EAChC,KAAM,CAAE,MAAO,GAAK,QAAS,CAAE,EAC/B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,gEAEV,SAAA,CAACP,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,gBAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,6DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iDAAiD,SAE/D,kBAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAOhD,EACP,SAAW,GAAMC,EAAe,EAAE,OAAO,KAAK,EAC9C,YAAY,kCACZ,UAAU,4LACV,aAAW,gBAAA,CACb,EACA+C,EAAA,IAAC,QAAA,CACC,KAAK,OACL,MAAO9C,EACP,SAAW,GAAMC,EAAa,EAAE,OAAO,KAAK,EAC5C,YAAY,0BACZ,UAAU,4LACV,aAAW,cAAA,CACb,EACA4C,EAAA,KAAC,SAAA,CACC,QAAStB,EACT,SAAU/B,GAAW,CAACM,EAAY,KAAK,EACvC,UAAU,wJACV,aAAW,qCAEV,SAAA,CACCN,EAAAsD,EAAA,IAACE,GAAU,UAAU,sBAAA,CAAuB,EAE3CF,EAAAA,IAAAQ,EAAA,CAAM,UAAU,SAAU,CAAA,EAE7BR,EAAAA,IAAC,QAAK,SAAU,YAAA,CAAA,CAAA,CAAA,CAClB,CAAA,EACF,CAAA,EACF,EAGAD,EAAAA,KAAC,MAAI,CAAA,UAAU,6DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iDAAiD,SAE/D,kBAAA,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAAC,QAAA,CACC,KAAK,SACL,MAAO5C,EACP,SAAW,GACTC,EACE,KAAK,IACH,EACA,KAAK,IAAI,GAAI,SAAS,EAAE,OAAO,KAAK,GAAK,CAAC,CAC5C,CACF,EAEF,IAAI,IACJ,IAAI,KACJ,YAAY,sBACZ,UAAU,4LACV,aAAW,qBAAA,CACb,EACA0C,EAAA,KAAC,SAAA,CACC,QAASpB,EACT,SAAUjC,EACV,UAAU,0JACV,aAAY,SAASU,CAAS,uBAE7B,SAAA,CACCV,EAAAsD,EAAA,IAACE,GAAU,UAAU,sBAAA,CAAuB,EAE3CF,EAAAA,IAAAS,EAAA,CAAM,UAAU,SAAU,CAAA,SAE5B,OAAK,CAAA,SAAA,CAAA,SAAOrD,EAAU,QAAA,EAAM,CAAA,CAAA,CAC/B,CAAA,EACF,CAAA,EACF,EAGA2C,EAAAA,KAAC,MAAI,CAAA,UAAU,6DACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,iDAAiD,SAE/D,+BAAA,EACAD,EAAA,KAAC,SAAA,CACC,QAASnB,EACT,SAAUlC,EACV,UAAU,4JACV,aAAW,qCAEV,SAAA,CACCA,EAAAsD,EAAA,IAACE,GAAU,UAAU,sBAAA,CAAuB,EAE3CF,EAAAA,IAAAU,GAAA,CAAU,UAAU,SAAU,CAAA,EAEjCV,EAAAA,IAAC,QAAK,SAAc,gBAAA,CAAA,CAAA,CAAA,CACtB,CAAA,EACF,CAAA,EACF,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,sBACb,SAAAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMjD,EAAiB,EAAK,EACrC,UAAY,GACV,EAAE,MAAQ,SAAWA,EAAiB,EAAK,EAE7C,UAAU,uJACV,aAAW,8BACZ,SAAA,UAAA,CAAA,EAGH,CAAA,CAAA,CACF,CAAA,CAAA,EAGN,EAGAgD,EAAAA,KAAC,MAAI,CAAA,UAAU,8CACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,oDACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAEpE,mBAAA,EACAD,EAAA,KAAC,SAAA,CACC,QAASlC,EACT,UAAY,GAAM,EAAE,MAAQ,SAAWA,EAAY,EACnD,UAAU,8HACV,aAAW,8BAEX,SAAA,CAAAmC,EAAA,IAACE,EAAA,CACC,UAAW,WAAWxD,EAAU,eAAiB,EAAE,EAAA,CACrD,EACCsD,EAAA,IAAA,OAAA,CAAM,SAAUtD,EAAA,gBAAkB,YAAY,CAAA,CAAA,CACjD,CAAA,CAAA,CACF,CACF,CAAA,EACAsD,EAAA,IAAC,MAAI,CAAA,UAAU,MACZ,SAAAtD,GAAWH,EAAQ,SAAW,EAC7BwD,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAACC,EAAAA,IAAAE,EAAA,CAAU,UAAU,mDAAoD,CAAA,EACxEF,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,yBAAA,CAAA,EACF,EACEzD,EAAQ,SAAW,EACpBwD,OAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAAA,IAAAW,GAAA,CAAO,UAAU,sCAAuC,CAAA,EACxDX,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,8DAAA,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMjD,EAAiB,EAAI,EACpC,UAAY,GAAM,EAAE,MAAQ,SAAWA,EAAiB,EAAI,EAC5D,UAAU,kFACV,aAAW,yBACZ,SAAA,wBAAA,CAED,CACF,CAAA,CAAA,QAEC,MAAI,CAAA,UAAU,uDACZ,SAAQR,EAAA,IAAKsC,GACZkB,EAAA,KAACQ,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,6DAEV,SAAA,CAACR,EAAAA,KAAA,MAAA,CAAI,UAAU,mBACZ,SAAA,CAAAJ,EAAkBd,CAAM,EACxBmB,EAAA,IAAA,KAAA,CAAG,UAAU,qDACX,WAAO,KACV,EACAA,EAAAA,IAAC,KAAE,UAAU,2CACV,WAAO,OAAS,QACb,OACA,aACN,CAAA,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,qDACZ,SAAAnB,EAAO,IACV,EACAkB,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,QAAS,IAAM,OAAO,KAAKlB,EAAO,IAAK,QAAQ,EAC/C,UAAY+B,GACVA,EAAE,MAAQ,SAAW,OAAO,KAAK/B,EAAO,IAAK,QAAQ,EAEvD,UAAU,6MACV,aAAY,kBAAkBA,EAAO,IAAI,GAEzC,SAAA,CAACmB,EAAAA,IAAAI,EAAA,CAAa,UAAU,SAAU,CAAA,EAClCJ,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CACd,EACAD,EAAA,KAAC,SAAA,CACC,QAAS,IAAMV,EAAgBR,EAAO,GAAG,EACzC,UAAY+B,GACVA,EAAE,MAAQ,SAAWvB,EAAgBR,EAAO,GAAG,EAEjD,UAAU,mNACV,aAAY,yBAAyBA,EAAO,IAAI,GAEhD,SAAA,CAACmB,EAAAA,IAAAa,GAAA,CAAK,UAAU,SAAU,CAAA,EAC1Bb,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CACd,EACAD,EAAA,KAAC,SAAA,CACC,QAAS,IAAMN,EAAeZ,CAAM,EACpC,UAAY+B,GACVA,EAAE,MAAQ,SAAWnB,EAAeZ,CAAM,EAE5C,UAAU,yNACV,aAAY,kBAAkBA,EAAO,IAAI,GACzC,SAAUA,EAAO,gBAAkB3C,EAEnC,SAAA,CAAC8D,EAAAA,IAAAc,GAAA,CAAS,UAAU,SAAU,CAAA,EAC9Bd,EAAAA,IAAC,QAAK,SAAM,QAAA,CAAA,CAAA,CAAA,CACd,EACAA,EAAA,IAAC,SAAA,CACC,QAAS,IAAMT,EAAaV,EAAO,EAAE,EACrC,UAAY+B,GACVA,EAAE,MAAQ,SAAWrB,EAAaV,EAAO,EAAE,EAE7C,UAAU,qJACV,aAAY,mBAAmBA,EAAO,IAAI,GAE1C,SAAAmB,EAAAA,IAACe,GAAO,CAAA,UAAU,SAAU,CAAA,CAAA,CAC9B,CAAA,EACF,CAAA,EACF,CAAA,CAAA,EAnEKlC,EAAO,EAAA,CAqEf,EACH,CAEJ,CAAA,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,CAAC"}