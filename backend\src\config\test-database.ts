import { DataSource } from "typeorm";
import { Client as PgClient } from "pg";
import { config } from "dotenv";

// Carregar variáveis de ambiente
config();

// Importar entidades
import { Restaurant } from "../models/Restaurant";
import { Playlist } from "../models/Playlist";
import { Suggestion } from "../models/Suggestion";
import { Vote } from "../models/Vote";
import { ModerationRule } from "../models/ModerationRule";
import { PlayHistory } from "../models/PlayHistory";
import { ClientSession } from "../models/ClientSession";
import { AnalyticsDaily } from "../models/AnalyticsDaily";
import { User } from "../models/User";
import { PlaylistSchedule } from "../models/PlaylistSchedule";
import { PlaylistTrack } from "../models/PlaylistTrack";
import { CompetitiveVote } from "../models/CompetitiveVote";
import { Payment } from "../models/Payment";
import { Reward } from "../models/Reward";
import { Lyrics } from "../models/Lyrics";
import { QRCode } from "../models/QRCode";
import { Genre } from "../models/Genre";
import { QueueItem } from "../models/QueueItem";

export const TestDataSource = new DataSource({
  type: "postgres",
  host: process.env.TEST_DB_HOST || process.env.DB_HOST || "localhost",
  port: parseInt(process.env.TEST_DB_PORT || process.env.DB_PORT || "8002"), // Porta do PostgreSQL no Docker
  username: process.env.TEST_DB_USERNAME || process.env.DB_USERNAME || "restaurant_user",
  password: process.env.TEST_DB_PASSWORD || process.env.DB_PASSWORD || "restaurant_pass",
  // Use SEMPRE um banco de testes separado (nunca DB_NAME padrão)
  database: process.env.TEST_DB_NAME || "restaurant_playlist_test",
  synchronize: true,
  logging: false, // Desabilitar logs durante testes
  entities: [
    Restaurant,
    Playlist,
    PlaylistSchedule,
    PlaylistTrack,
  QueueItem,
    Suggestion,
    Vote,
    ModerationRule,
    PlayHistory,
    ClientSession,
    AnalyticsDaily,
    User,
    CompetitiveVote,
    Payment,
    Reward,
    Lyrics,
    QRCode,
    Genre,
  ],
  migrations: [],
  subscribers: [],
  ssl: false,
  extra: {
    connectionLimit: 5, // Menor para testes
    acquireTimeout: 30000,
    timeout: 30000,
  },
});

// Função para inicializar conexão de teste
export const initializeTestDatabase = async (): Promise<void> => {
  try {
    // Segurança: só permite uso com NODE_ENV=test
    if (process.env.NODE_ENV !== 'test') {
      throw new Error("TestDataSource só pode ser utilizado com NODE_ENV=test");
    }

    const dbName = process.env.TEST_DB_NAME || "restaurant_playlist_test";
    if (!/test/i.test(dbName)) {
      throw new Error(`Nome do banco de teste inválido: ${dbName}. Ele deve conter 'test'.`);
    }

    // Antes de inicializar o DataSource, garantir que o DB de teste exista
    try {
      const adminClient = new PgClient({
        host: process.env.TEST_DB_HOST || process.env.DB_HOST || "localhost",
        port: parseInt(process.env.TEST_DB_PORT || process.env.DB_PORT || "8002"),
        user: process.env.TEST_DB_USERNAME || process.env.DB_USERNAME || "restaurant_user",
        password: process.env.TEST_DB_PASSWORD || process.env.DB_PASSWORD || "restaurant_pass",
        database: process.env.TEST_DB_ADMIN_DB || "postgres",
      });
      await adminClient.connect();
      const existsRes = await adminClient.query("SELECT 1 FROM pg_database WHERE datname = $1", [dbName]);
      if (existsRes.rowCount === 0) {
        try {
          await adminClient.query(`CREATE DATABASE ${dbName}`);
          console.log(`🆕 Banco de teste criado: ${dbName}`);
        } catch (e) {
          console.warn(`⚠️ Não foi possível criar o banco de teste ${dbName} (sem permissão?). Prosseguindo se já existir...`, e);
        }
      }
      await adminClient.end();
    } catch (e) {
      console.warn("⚠️ Falha ao verificar/criar DB de teste. Prosseguindo com tentativa de conexão direta.", e);
    }

    if (!TestDataSource.isInitialized) {
      await TestDataSource.initialize();
      console.log("✅ Conexão de teste com PostgreSQL estabelecida");
  // synchronize=true já cria o schema com base nas entidades
    }
  } catch (error) {
    console.error("❌ Erro ao conectar com PostgreSQL para testes:", error);
    throw error;
  }
};

// Função para limpar dados de teste
export const cleanTestData = async (): Promise<void> => {
  try {
    // Segurança: só roda em ambiente de teste e com DB contendo 'test'
    if (process.env.NODE_ENV !== 'test') {
      console.warn('⚠️ cleanTestData ignorado: NODE_ENV != test');
      return;
    }
    const currentDb = (TestDataSource.options as any)?.database as string;
    if (!/test/i.test(currentDb || '')) {
      console.warn(`⚠️ cleanTestData ignorado: database atual não é de teste (${currentDb})`);
      return;
    }
    // Limpar em ordem específica para evitar conflitos de foreign key
    // Usar nomes exatos das tabelas (@Entity)
    const tablesToClear = [
      'queue_items',
      'votes',
      'competitive_votes',
      'payments',
      'suggestions',
      'playlist_schedules',
      'play_history',
      'playlists',
      'client_sessions',
      'qr_codes',
      'lyrics',
      'rewards',
      'genres',
      'users',
      'restaurants'
    ];

    for (const table of tablesToClear) {
      try {
        // Evitar DELETE em tabela inexistente para não poluir logs do Postgres
  const reg = await TestDataSource.query(`SELECT to_regclass('public.${table}') as reg`);
        const exists = reg?.[0]?.reg;
        if (!exists) {
          // tabela não existe: seguir sem erro
          continue;
        }
  await TestDataSource.query(`DELETE FROM ${table}`);
      } catch (error) {
        // Ignorar se a tabela não existir
        console.warn(`⚠️ Tabela ${table} não encontrada ou já vazia`);
      }
    }
    
    console.log("🧹 Dados de teste limpos");
  } catch (error) {
    console.error("❌ Erro ao limpar dados de teste:", error);
    throw error;
  }
};

// Função para fechar conexão de teste
export const closeTestDatabase = async (): Promise<void> => {
  try {
    if (TestDataSource.isInitialized) {
      await TestDataSource.destroy();
      console.log("✅ Conexão de teste com PostgreSQL fechada");
    }
  } catch (error) {
    console.error("❌ Erro ao fechar conexão de teste:", error);
    throw error;
  }
};
