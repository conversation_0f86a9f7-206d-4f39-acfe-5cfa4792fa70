import { buildApiUrl } from "@/config/api";

export interface CooldownInfo {
  isInCooldown: boolean;
  cooldownTimeLeft?: number; // em segundos
}

export interface SongWithCooldown {
  youtubeVideoId: string;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number;
}

// Flag para desabilitar completamente chamadas ao backend de cooldown
const DISABLE_BACKEND_COOLDOWN = true;

class CooldownService {
  // Armazenamos o instante do cache (cachedAt) e o TTL base (ttlAtCache) para computar o tempo restante corretamente
  private cooldownCache: Map<string, { isInCooldown: boolean; cachedAt: number; ttlAtCache: number; expiresAt: number; lastTimeLeft?: number }> = new Map();
  private cacheExpiry = 30000; // 30 segundos
  private intervalId: any = null;

  /**
   * Verifica se uma música está em cooldown
   */
  async checkSongCooldown(restaurantId: string, youtubeVideoId: string): Promise<CooldownInfo> {
    if (DISABLE_BACKEND_COOLDOWN) {
      // Sem backend: sempre sem cooldown; UI usa apenas realtime/local
      return { isInCooldown: false };
    }
  const cacheKey = `${restaurantId}:${youtubeVideoId}`;
  const cached = this.cooldownCache.get(cacheKey);
    
    // Verificar cache válido
    if (cached && Date.now() < cached.expiresAt) {
      if (!cached.isInCooldown) {
        return { isInCooldown: false };
      }
      const elapsed = Math.floor((Date.now() - cached.cachedAt) / 1000);
      const timeLeft = Math.max(0, cached.ttlAtCache - elapsed);
      // armazenar leitura mais recente
      cached.lastTimeLeft = timeLeft;
      this.cooldownCache.set(cacheKey, cached);
      return { isInCooldown: timeLeft > 0, cooldownTimeLeft: timeLeft > 0 ? timeLeft : undefined };
    }

  try {
      const response = await fetch(
        buildApiUrl(`/collaborative-playlist/${restaurantId}/cooldown/${youtubeVideoId}`),
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const isInCooldown = !!data.isInCooldown;
        const ttl = Math.max(0, Number(data.cooldownTimeLeft || 0));
        // Cache por 30 segundos
        this.cooldownCache.set(cacheKey, {
          isInCooldown,
          cachedAt: Date.now(),
          ttlAtCache: ttl,
          expiresAt: Date.now() + this.cacheExpiry,
          lastTimeLeft: ttl,
        });

        return {
          isInCooldown,
          cooldownTimeLeft: isInCooldown ? ttl : undefined,
        };
      }
    } catch (error) {
      console.warn("Erro ao verificar cooldown:", error);
    }

    // Fallback: assumir que não está em cooldown
    return { isInCooldown: false };
  }

  /**
   * Verifica cooldown para múltiplas músicas
   */
  async checkMultipleSongsCooldown(restaurantId: string, songs: SongWithCooldown[]): Promise<SongWithCooldown[]> {
    if (DISABLE_BACKEND_COOLDOWN) {
      return songs.map((song) => ({ ...song, isInCooldown: false, cooldownTimeLeft: undefined }));
    }
    const promises = songs.map(async (song) => {
      const cooldownInfo = await this.checkSongCooldown(restaurantId, song.youtubeVideoId);
      return {
        ...song,
        isInCooldown: cooldownInfo.isInCooldown,
        cooldownTimeLeft: cooldownInfo.cooldownTimeLeft
      };
    });

    return Promise.all(promises);
  }

  /**
   * Marca uma música como em cooldown localmente (para otimização)
   */
  markSongInCooldown(restaurantId: string, youtubeVideoId: string, cooldownMinutes: number = 10): void {
    const cacheKey = `${restaurantId}:${youtubeVideoId}`;
    const cooldownSeconds = cooldownMinutes * 60;

    this.cooldownCache.set(cacheKey, {
      isInCooldown: true,
      cachedAt: Date.now(),
      ttlAtCache: cooldownSeconds,
      expiresAt: Date.now() + this.cacheExpiry,
      lastTimeLeft: cooldownSeconds,
    });
  }

  /**
   * Remove uma música do cooldown localmente
   */
  removeSongFromCooldown(restaurantId: string, youtubeVideoId: string): void {
    const cacheKey = `${restaurantId}:${youtubeVideoId}`;
    this.cooldownCache.delete(cacheKey);
  }

  /**
   * Limpa o cache de cooldown
   */
  clearCache(): void {
    this.cooldownCache.clear();
  }

  /**
   * Atualiza o tempo restante de cooldown para todas as músicas em cache
   */
  updateCooldownTimes(): void {
    const now = Date.now();

    for (const [key, cached] of this.cooldownCache.entries()) {
      if (!cached.isInCooldown) continue;
      const elapsed = Math.floor((now - cached.cachedAt) / 1000);
      const timeLeft = Math.max(0, cached.ttlAtCache - elapsed);
      if (timeLeft <= 0) {
        this.cooldownCache.set(key, {
          ...cached,
          isInCooldown: false,
          lastTimeLeft: 0,
          // mantém cachedAt/ttlAtCache para referência; próxima leitura via API corrigirá
        });
      } else {
        this.cooldownCache.set(key, {
          ...cached,
          lastTimeLeft: timeLeft,
        });
      }
    }
  }

  /**
   * Inicia um timer para atualizar os tempos de cooldown
   */
  startCooldownTimer(): void {
    if (DISABLE_BACKEND_COOLDOWN) return; // Sem timer se não usamos backend/TTL
    if (this.intervalId) return; // evita múltiplos timers
    this.intervalId = setInterval(() => {
      this.updateCooldownTimes();
    }, 1000); // Atualizar a cada segundo
  }
}

export const cooldownService = new CooldownService();
export default cooldownService;
