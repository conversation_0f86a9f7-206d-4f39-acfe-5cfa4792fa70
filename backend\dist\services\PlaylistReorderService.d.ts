/**
 * 🔄 SERVIÇO DE REORDENAÇÃO AUTOMÁTICA DE PLAYLISTS
 *
 * Executa a cada 5 minutos para reordenar playlists do YouTube
 * baseado no ranking de supervotos em tempo real.
 *
 * Funcionalidades:
 * - Busca ranking de supervotos por restaurante
 * - Reordena playlist do YouTube automaticamente
 * - Logs de auditoria para monitoramento
 * - Tratamento de erros robusto
 */
export interface ReorderResult {
    success: boolean;
    restaurantId: string;
    playlistId?: string;
    tracksReordered: number;
    message: string;
    timestamp: Date;
    status?: "success" | "skipped" | "failed";
}
export interface PlaylistItem {
    videoId: string;
    position: number;
    title?: string;
    voteCount?: number;
    isPaid?: boolean;
    paymentAmount?: number;
}
export declare class PlaylistReorderService {
    private static instance;
    private restaurantRepository;
    private playlistRepository;
    private suggestionRepository;
    private scheduleRepository;
    private reorderInterval;
    private isRunning;
    private static readonly GRACE_WINDOW_SECONDS;
    private constructor();
    static getInstance(): PlaylistReorderService;
    /**
     * 🚀 INICIAR REORDENAÇÃO AUTOMÁTICA
     * Executa a cada 5 minutos (300.000ms)
     */
    startAutoReorder(): void;
    /**
     * 🛑 PARAR REORDENAÇÃO AUTOMÁTICA
     */
    stopAutoReorder(): void;
    /**
     * 🔄 EXECUTAR CICLO DE REORDENAÇÃO
     * Processa todos os restaurantes ativos
     */
    private executeReorderCycle;
    /**
     * 🏪 REORDENAR PLAYLISTS DE UM RESTAURANTE
     */
    private reorderRestaurantPlaylists;
    /**
     * 📊 OBTER STATUS DO SERVIÇO
     */
    getStatus(): {
        isRunning: boolean;
        nextExecution?: Date;
        uptime: number;
    };
    /**
     * 🔔 NOTIFICAR REORDENAÇÃO DE PLAYLIST
     */
    private notifyPlaylistReorder;
    /**
     * 🔧 REORDENAR MANUALMENTE (para testes)
     */
    manualReorder(restaurantId: string): Promise<ReorderResult>;
}
export declare const playlistReorderService: PlaylistReorderService;
//# sourceMappingURL=PlaylistReorderService.d.ts.map