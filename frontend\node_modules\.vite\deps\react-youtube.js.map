{"version": 3, "sources": ["../../fast-deep-equal/index.js", "../../sister/src/sister.js", "../../load-script/index.js", "../../youtube-player/dist/loadYouTubeIframeApi.js", "../../youtube-player/node_modules/ms/index.js", "../../youtube-player/node_modules/debug/src/debug.js", "../../youtube-player/node_modules/debug/src/browser.js", "../../youtube-player/dist/functionNames.js", "../../youtube-player/dist/eventNames.js", "../../youtube-player/dist/constants/PlayerStates.js", "../../youtube-player/dist/FunctionStateMap.js", "../../youtube-player/dist/YouTubePlayer.js", "../../youtube-player/dist/index.js", "../../react-youtube/src/YouTube.tsx"], "sourcesContent": ["'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n", "'use strict';\n\nvar Sister;\n\n/**\n* @link https://github.com/gajus/sister for the canonical source repository\n* @license https://github.com/gajus/sister/blob/master/LICENSE BSD 3-Clause\n*/\nSister = function () {\n    var sister = {},\n        events = {};\n\n    /**\n     * @name handler\n     * @function\n     * @param {Object} data Event data.\n     */\n\n    /**\n     * @param {String} name Event name.\n     * @param {handler} handler\n     * @return {listener}\n     */\n    sister.on = function (name, handler) {\n        var listener = {name: name, handler: handler};\n        events[name] = events[name] || [];\n        events[name].unshift(listener);\n        return listener;\n    };\n\n    /**\n     * @param {listener}\n     */\n    sister.off = function (listener) {\n        var index = events[listener.name].indexOf(listener);\n\n        if (index !== -1) {\n            events[listener.name].splice(index, 1);\n        }\n    };\n\n    /**\n     * @param {String} name Event name.\n     * @param {Object} data Event data.\n     */\n    sister.trigger = function (name, data) {\n        var listeners = events[name],\n            i;\n\n        if (listeners) {\n            i = listeners.length;\n            while (i--) {\n                listeners[i].handler(data);\n            }\n        }\n    };\n\n    return sister;\n};\n\nmodule.exports = Sister;\n", "\nmodule.exports = function load (src, opts, cb) {\n  var head = document.head || document.getElementsByTagName('head')[0]\n  var script = document.createElement('script')\n\n  if (typeof opts === 'function') {\n    cb = opts\n    opts = {}\n  }\n\n  opts = opts || {}\n  cb = cb || function() {}\n\n  script.type = opts.type || 'text/javascript'\n  script.charset = opts.charset || 'utf8';\n  script.async = 'async' in opts ? !!opts.async : true\n  script.src = src\n\n  if (opts.attrs) {\n    setAttributes(script, opts.attrs)\n  }\n\n  if (opts.text) {\n    script.text = '' + opts.text\n  }\n\n  var onend = 'onload' in script ? stdOnEnd : ieOnEnd\n  onend(script, cb)\n\n  // some good legacy browsers (firefox) fail the 'in' detection above\n  // so as a fallback we always set onload\n  // old IE will ignore this and new IE will set onload\n  if (!script.onload) {\n    stdOnEnd(script, cb);\n  }\n\n  head.appendChild(script)\n}\n\nfunction setAttributes(script, attrs) {\n  for (var attr in attrs) {\n    script.setAttribute(attr, attrs[attr]);\n  }\n}\n\nfunction stdOnEnd (script, cb) {\n  script.onload = function () {\n    this.onerror = this.onload = null\n    cb(null, script)\n  }\n  script.onerror = function () {\n    // this.onload = null here is necessary\n    // because even IE9 works not like others\n    this.onerror = this.onload = null\n    cb(new Error('Failed to load ' + this.src), script)\n  }\n}\n\nfunction ieOnEnd (script, cb) {\n  script.onreadystatechange = function () {\n    if (this.readyState != 'complete' && this.readyState != 'loaded') return\n    this.onreadystatechange = null\n    cb(null, script) // there is no way to catch loading errors in IE8\n  }\n}\n", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _loadScript = require('load-script');\n\nvar _loadScript2 = _interopRequireDefault(_loadScript);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = function (emitter) {\n  /**\n   * A promise that is resolved when window.onYouTubeIframeAPIReady is called.\n   * The promise is resolved with a reference to window.YT object.\n   */\n  var iframeAPIReady = new Promise(function (resolve) {\n    if (window.YT && window.YT.Player && window.YT.Player instanceof Function) {\n      resolve(window.YT);\n\n      return;\n    } else {\n      var protocol = window.location.protocol === 'http:' ? 'http:' : 'https:';\n\n      (0, _loadScript2.default)(protocol + '//www.youtube.com/iframe_api', function (error) {\n        if (error) {\n          emitter.trigger('error', error);\n        }\n      });\n    }\n\n    var previous = window.onYouTubeIframeAPIReady;\n\n    // The API will call this function when page has finished downloading\n    // the JavaScript for the player API.\n    window.onYouTubeIframeAPIReady = function () {\n      if (previous) {\n        previous();\n      }\n\n      resolve(window.YT);\n    };\n  });\n\n  return iframeAPIReady;\n};\n\nmodule.exports = exports['default'];", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isNaN(val) === false) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^((?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  if (ms >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (ms >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (ms >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (ms >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  return plural(ms, d, 'day') ||\n    plural(ms, h, 'hour') ||\n    plural(ms, m, 'minute') ||\n    plural(ms, s, 'second') ||\n    ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, n, name) {\n  if (ms < n) {\n    return;\n  }\n  if (ms < n * 1.5) {\n    return Math.floor(ms / n) + ' ' + name;\n  }\n  return Math.ceil(ms / n) + ' ' + name + 's';\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = createDebug.debug = createDebug['default'] = createDebug;\nexports.coerce = coerce;\nexports.disable = disable;\nexports.enable = enable;\nexports.enabled = enabled;\nexports.humanize = require('ms');\n\n/**\n * The currently active debug mode names, and names to skip.\n */\n\nexports.names = [];\nexports.skips = [];\n\n/**\n * Map of special \"%n\" handling functions, for the debug \"format\" argument.\n *\n * Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n */\n\nexports.formatters = {};\n\n/**\n * Previous log timestamp.\n */\n\nvar prevTime;\n\n/**\n * Select a color.\n * @param {String} namespace\n * @return {Number}\n * @api private\n */\n\nfunction selectColor(namespace) {\n  var hash = 0, i;\n\n  for (i in namespace) {\n    hash  = ((hash << 5) - hash) + namespace.charCodeAt(i);\n    hash |= 0; // Convert to 32bit integer\n  }\n\n  return exports.colors[Math.abs(hash) % exports.colors.length];\n}\n\n/**\n * Create a debugger with the given `namespace`.\n *\n * @param {String} namespace\n * @return {Function}\n * @api public\n */\n\nfunction createDebug(namespace) {\n\n  function debug() {\n    // disabled?\n    if (!debug.enabled) return;\n\n    var self = debug;\n\n    // set `diff` timestamp\n    var curr = +new Date();\n    var ms = curr - (prevTime || curr);\n    self.diff = ms;\n    self.prev = prevTime;\n    self.curr = curr;\n    prevTime = curr;\n\n    // turn the `arguments` into a proper Array\n    var args = new Array(arguments.length);\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i];\n    }\n\n    args[0] = exports.coerce(args[0]);\n\n    if ('string' !== typeof args[0]) {\n      // anything else let's inspect with %O\n      args.unshift('%O');\n    }\n\n    // apply any `formatters` transformations\n    var index = 0;\n    args[0] = args[0].replace(/%([a-zA-Z%])/g, function(match, format) {\n      // if we encounter an escaped % then don't increase the array index\n      if (match === '%%') return match;\n      index++;\n      var formatter = exports.formatters[format];\n      if ('function' === typeof formatter) {\n        var val = args[index];\n        match = formatter.call(self, val);\n\n        // now we need to remove `args[index]` since it's inlined in the `format`\n        args.splice(index, 1);\n        index--;\n      }\n      return match;\n    });\n\n    // apply env-specific formatting (colors, etc.)\n    exports.formatArgs.call(self, args);\n\n    var logFn = debug.log || exports.log || console.log.bind(console);\n    logFn.apply(self, args);\n  }\n\n  debug.namespace = namespace;\n  debug.enabled = exports.enabled(namespace);\n  debug.useColors = exports.useColors();\n  debug.color = selectColor(namespace);\n\n  // env-specific initialization logic for debug instances\n  if ('function' === typeof exports.init) {\n    exports.init(debug);\n  }\n\n  return debug;\n}\n\n/**\n * Enables a debug mode by namespaces. This can include modes\n * separated by a colon and wildcards.\n *\n * @param {String} namespaces\n * @api public\n */\n\nfunction enable(namespaces) {\n  exports.save(namespaces);\n\n  exports.names = [];\n  exports.skips = [];\n\n  var split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n  var len = split.length;\n\n  for (var i = 0; i < len; i++) {\n    if (!split[i]) continue; // ignore empty strings\n    namespaces = split[i].replace(/\\*/g, '.*?');\n    if (namespaces[0] === '-') {\n      exports.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n    } else {\n      exports.names.push(new RegExp('^' + namespaces + '$'));\n    }\n  }\n}\n\n/**\n * Disable debug output.\n *\n * @api public\n */\n\nfunction disable() {\n  exports.enable('');\n}\n\n/**\n * Returns true if the given mode name is enabled, false otherwise.\n *\n * @param {String} name\n * @return {Boolean}\n * @api public\n */\n\nfunction enabled(name) {\n  var i, len;\n  for (i = 0, len = exports.skips.length; i < len; i++) {\n    if (exports.skips[i].test(name)) {\n      return false;\n    }\n  }\n  for (i = 0, len = exports.names.length; i < len; i++) {\n    if (exports.names[i].test(name)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Coerce `val`.\n *\n * @param {Mixed} val\n * @return {Mixed}\n * @api private\n */\n\nfunction coerce(val) {\n  if (val instanceof Error) return val.stack || val.message;\n  return val;\n}\n", "/**\n * This is the web browser implementation of `debug()`.\n *\n * Expose `debug()` as the module.\n */\n\nexports = module.exports = require('./debug');\nexports.log = log;\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = 'undefined' != typeof chrome\n               && 'undefined' != typeof chrome.storage\n                  ? chrome.storage.local\n                  : localstorage();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n  'lightseagreen',\n  'forestgreen',\n  'goldenrod',\n  'dodgerblue',\n  'darkorchid',\n  'crimson'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\nfunction useColors() {\n  // NB: In an Electron preload script, document will be defined but not fully\n  // initialized. Since we know we're in Chrome, we'll just detect this case\n  // explicitly\n  if (typeof window !== 'undefined' && window.process && window.process.type === 'renderer') {\n    return true;\n  }\n\n  // is webkit? http://stackoverflow.com/a/16459606/376773\n  // document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n  return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n    // is firebug? http://stackoverflow.com/a/398120/376773\n    (typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n    // is firefox >= v31?\n    // https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n    // double check webkit in userAgent just in case we are in a worker\n    (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nexports.formatters.j = function(v) {\n  try {\n    return JSON.stringify(v);\n  } catch (err) {\n    return '[UnexpectedJSONParseError]: ' + err.message;\n  }\n};\n\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n  var useColors = this.useColors;\n\n  args[0] = (useColors ? '%c' : '')\n    + this.namespace\n    + (useColors ? ' %c' : ' ')\n    + args[0]\n    + (useColors ? '%c ' : ' ')\n    + '+' + exports.humanize(this.diff);\n\n  if (!useColors) return;\n\n  var c = 'color: ' + this.color;\n  args.splice(1, 0, c, 'color: inherit')\n\n  // the final \"%c\" is somewhat tricky, because there could be other\n  // arguments passed either before or after the %c, so we need to\n  // figure out the correct index to insert the CSS into\n  var index = 0;\n  var lastC = 0;\n  args[0].replace(/%[a-zA-Z%]/g, function(match) {\n    if ('%%' === match) return;\n    index++;\n    if ('%c' === match) {\n      // we only are interested in the *last* %c\n      // (the user may have provided their own)\n      lastC = index;\n    }\n  });\n\n  args.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.log()` when available.\n * No-op when `console.log` is not a \"function\".\n *\n * @api public\n */\n\nfunction log() {\n  // this hackery is required for IE8/9, where\n  // the `console.log` function doesn't have 'apply'\n  return 'object' === typeof console\n    && console.log\n    && Function.prototype.apply.call(console.log, console, arguments);\n}\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\n\nfunction save(namespaces) {\n  try {\n    if (null == namespaces) {\n      exports.storage.removeItem('debug');\n    } else {\n      exports.storage.debug = namespaces;\n    }\n  } catch(e) {}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\n\nfunction load() {\n  var r;\n  try {\n    r = exports.storage.debug;\n  } catch(e) {}\n\n  // If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n  if (!r && typeof process !== 'undefined' && 'env' in process) {\n    r = process.env.DEBUG;\n  }\n\n  return r;\n}\n\n/**\n * Enable namespaces listed in `localStorage.debug` initially.\n */\n\nexports.enable(load());\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n  try {\n    return window.localStorage;\n  } catch (e) {}\n}\n", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\n\n/**\n * @see https://developers.google.com/youtube/iframe_api_reference#Functions\n */\nexports.default = ['cueVideoById', 'loadVideoById', 'cueVideoByUrl', 'loadVideoByUrl', 'playVideo', 'pauseVideo', 'stopVideo', 'getVideoLoadedFraction', 'cuePlaylist', 'loadPlaylist', 'nextVideo', 'previousVideo', 'playVideoAt', 'setShuffle', 'setLoop', 'getPlaylist', 'getPlaylistIndex', 'setOption', 'mute', 'unMute', 'isMuted', 'setVolume', 'getVolume', 'seekTo', 'getPlayerState', 'getPlaybackRate', 'setPlaybackRate', 'getAvailablePlaybackRates', 'getPlaybackQuality', 'setPlaybackQuality', 'getAvailableQualityLevels', 'getCurrentTime', 'getDuration', 'removeEventListener', 'getVideoUrl', 'getVideoEmbedCode', 'getOptions', 'getOption', 'addEventListener', 'destroy', 'setSize', 'getIframe'];\nmodule.exports = exports['default'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\n\n/**\n * @see https://developers.google.com/youtube/iframe_api_reference#Events\n * `volumeChange` is not officially supported but seems to work\n * it emits an object: `{volume: 82.6923076923077, muted: false}`\n */\nexports.default = ['ready', 'stateChange', 'playbackQualityChange', 'playbackRateChange', 'error', 'apiChange', 'volumeChange'];\nmodule.exports = exports['default'];", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = {\n  BUFFERING: 3,\n  ENDED: 0,\n  PAUSED: 2,\n  PLAYING: 1,\n  UNSTARTED: -1,\n  VIDEO_CUED: 5\n};\nmodule.exports = exports[\"default\"];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _PlayerStates = require('./constants/PlayerStates');\n\nvar _PlayerStates2 = _interopRequireDefault(_PlayerStates);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = {\n  pauseVideo: {\n    acceptableStates: [_PlayerStates2.default.ENDED, _PlayerStates2.default.PAUSED],\n    stateChangeRequired: false\n  },\n  playVideo: {\n    acceptableStates: [_PlayerStates2.default.ENDED, _PlayerStates2.default.PLAYING],\n    stateChangeRequired: false\n  },\n  seekTo: {\n    acceptableStates: [_PlayerStates2.default.ENDED, _PlayerStates2.default.PLAYING, _PlayerStates2.default.PAUSED],\n    stateChangeRequired: true,\n\n    // TRICKY: `seekTo` may not cause a state change if no buffering is\n    // required.\n    timeout: 3000\n  }\n};\nmodule.exports = exports['default'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _debug = require('debug');\n\nvar _debug2 = _interopRequireDefault(_debug);\n\nvar _functionNames = require('./functionNames');\n\nvar _functionNames2 = _interopRequireDefault(_functionNames);\n\nvar _eventNames = require('./eventNames');\n\nvar _eventNames2 = _interopRequireDefault(_eventNames);\n\nvar _FunctionStateMap = require('./FunctionStateMap');\n\nvar _FunctionStateMap2 = _interopRequireDefault(_FunctionStateMap);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/* eslint-disable promise/prefer-await-to-then */\n\nvar debug = (0, _debug2.default)('youtube-player');\n\nvar YouTubePlayer = {};\n\n/**\n * Construct an object that defines an event handler for all of the YouTube\n * player events. Proxy captured events through an event emitter.\n *\n * @todo Capture event parameters.\n * @see https://developers.google.com/youtube/iframe_api_reference#Events\n */\nYouTubePlayer.proxyEvents = function (emitter) {\n  var events = {};\n\n  var _loop = function _loop(eventName) {\n    var onEventName = 'on' + eventName.slice(0, 1).toUpperCase() + eventName.slice(1);\n\n    events[onEventName] = function (event) {\n      debug('event \"%s\"', onEventName, event);\n\n      emitter.trigger(eventName, event);\n    };\n  };\n\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = _eventNames2.default[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var eventName = _step.value;\n\n      _loop(eventName);\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n\n  return events;\n};\n\n/**\n * Delays player API method execution until player state is ready.\n *\n * @todo Proxy all of the methods using Object.keys.\n * @todo See TRICKY below.\n * @param playerAPIReady Promise that resolves when player is ready.\n * @param strictState A flag designating whether or not to wait for\n * an acceptable state when calling supported functions.\n * @returns {Object}\n */\nYouTubePlayer.promisifyPlayer = function (playerAPIReady) {\n  var strictState = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var functions = {};\n\n  var _loop2 = function _loop2(functionName) {\n    if (strictState && _FunctionStateMap2.default[functionName]) {\n      functions[functionName] = function () {\n        for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n          args[_key] = arguments[_key];\n        }\n\n        return playerAPIReady.then(function (player) {\n          var stateInfo = _FunctionStateMap2.default[functionName];\n          var playerState = player.getPlayerState();\n\n          // eslint-disable-next-line no-warning-comments\n          // TODO: Just spread the args into the function once Babel is fixed:\n          // https://github.com/babel/babel/issues/4270\n          //\n          // eslint-disable-next-line prefer-spread\n          var value = player[functionName].apply(player, args);\n\n          // TRICKY: For functions like `seekTo`, a change in state must be\n          // triggered given that the resulting state could match the initial\n          // state.\n          if (stateInfo.stateChangeRequired ||\n\n          // eslint-disable-next-line no-extra-parens\n          Array.isArray(stateInfo.acceptableStates) && stateInfo.acceptableStates.indexOf(playerState) === -1) {\n            return new Promise(function (resolve) {\n              var onPlayerStateChange = function onPlayerStateChange() {\n                var playerStateAfterChange = player.getPlayerState();\n\n                var timeout = void 0;\n\n                if (typeof stateInfo.timeout === 'number') {\n                  timeout = setTimeout(function () {\n                    player.removeEventListener('onStateChange', onPlayerStateChange);\n\n                    resolve();\n                  }, stateInfo.timeout);\n                }\n\n                if (Array.isArray(stateInfo.acceptableStates) && stateInfo.acceptableStates.indexOf(playerStateAfterChange) !== -1) {\n                  player.removeEventListener('onStateChange', onPlayerStateChange);\n\n                  clearTimeout(timeout);\n\n                  resolve();\n                }\n              };\n\n              player.addEventListener('onStateChange', onPlayerStateChange);\n            }).then(function () {\n              return value;\n            });\n          }\n\n          return value;\n        });\n      };\n    } else {\n      functions[functionName] = function () {\n        for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          args[_key2] = arguments[_key2];\n        }\n\n        return playerAPIReady.then(function (player) {\n          // eslint-disable-next-line no-warning-comments\n          // TODO: Just spread the args into the function once Babel is fixed:\n          // https://github.com/babel/babel/issues/4270\n          //\n          // eslint-disable-next-line prefer-spread\n          return player[functionName].apply(player, args);\n        });\n      };\n    }\n  };\n\n  var _iteratorNormalCompletion2 = true;\n  var _didIteratorError2 = false;\n  var _iteratorError2 = undefined;\n\n  try {\n    for (var _iterator2 = _functionNames2.default[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {\n      var functionName = _step2.value;\n\n      _loop2(functionName);\n    }\n  } catch (err) {\n    _didIteratorError2 = true;\n    _iteratorError2 = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion2 && _iterator2.return) {\n        _iterator2.return();\n      }\n    } finally {\n      if (_didIteratorError2) {\n        throw _iteratorError2;\n      }\n    }\n  }\n\n  return functions;\n};\n\nexports.default = YouTubePlayer;\nmodule.exports = exports['default'];", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _sister = require('sister');\n\nvar _sister2 = _interopRequireDefault(_sister);\n\nvar _loadYouTubeIframeApi = require('./loadYouTubeIframeApi');\n\nvar _loadYouTubeIframeApi2 = _interopRequireDefault(_loadYouTubeIframeApi);\n\nvar _YouTubePlayer = require('./YouTubePlayer');\n\nvar _YouTubePlayer2 = _interopRequireDefault(_YouTubePlayer);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * @typedef YT.Player\n * @see https://developers.google.com/youtube/iframe_api_reference\n * */\n\n/**\n * @see https://developers.google.com/youtube/iframe_api_reference#Loading_a_Video_Player\n */\nvar youtubeIframeAPI = void 0;\n\n/**\n * A factory function used to produce an instance of YT.Player and queue function calls and proxy events of the resulting object.\n *\n * @param maybeElementId Either An existing YT.Player instance,\n * the DOM element or the id of the HTML element where the API will insert an <iframe>.\n * @param options See `options` (Ignored when using an existing YT.Player instance).\n * @param strictState A flag designating whether or not to wait for\n * an acceptable state when calling supported functions. Default: `false`.\n * See `FunctionStateMap.js` for supported functions and acceptable states.\n */\n\nexports.default = function (maybeElementId) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var strictState = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var emitter = (0, _sister2.default)();\n\n  if (!youtubeIframeAPI) {\n    youtubeIframeAPI = (0, _loadYouTubeIframeApi2.default)(emitter);\n  }\n\n  if (options.events) {\n    throw new Error('Event handlers cannot be overwritten.');\n  }\n\n  if (typeof maybeElementId === 'string' && !document.getElementById(maybeElementId)) {\n    throw new Error('Element \"' + maybeElementId + '\" does not exist.');\n  }\n\n  options.events = _YouTubePlayer2.default.proxyEvents(emitter);\n\n  var playerAPIReady = new Promise(function (resolve) {\n    if ((typeof maybeElementId === 'undefined' ? 'undefined' : _typeof(maybeElementId)) === 'object' && maybeElementId.playVideo instanceof Function) {\n      var player = maybeElementId;\n\n      resolve(player);\n    } else {\n      // asume maybeElementId can be rendered inside\n      // eslint-disable-next-line promise/catch-or-return\n      youtubeIframeAPI.then(function (YT) {\n        // eslint-disable-line promise/prefer-await-to-then\n        var player = new YT.Player(maybeElementId, options);\n\n        emitter.on('ready', function () {\n          resolve(player);\n        });\n\n        return null;\n      });\n    }\n  });\n\n  var playerApi = _YouTubePlayer2.default.promisifyPlayer(playerAPIReady, strictState);\n\n  playerApi.on = emitter.on;\n  playerApi.off = emitter.off;\n\n  return playerApi;\n};\n\nmodule.exports = exports['default'];", "/** @jsxRuntime classic */\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport isEqual from 'fast-deep-equal';\nimport youTubePlayer from 'youtube-player';\nimport type { YouTubePlayer, Options } from 'youtube-player/dist/types';\n\n/**\n * Check whether a `props` change should result in the video being updated.\n */\nfunction shouldUpdateVideo(prevProps: YouTubeProps, props: YouTubeProps) {\n  // A changing video should always trigger an update\n  if (prevProps.videoId !== props.videoId) {\n    return true;\n  }\n\n  // Otherwise, a change in the start/end time playerVars also requires a player\n  // update.\n  const prevVars = prevProps.opts?.playerVars || {};\n  const vars = props.opts?.playerVars || {};\n\n  return prevVars.start !== vars.start || prevVars.end !== vars.end;\n}\n\n/**\n * Neutralize API options that only require a video update, leaving only options\n * that require a player reset. The results can then be compared to see if a\n * player reset is necessary.\n */\nfunction filterResetOptions(opts: Options = {}) {\n  return {\n    ...opts,\n    height: 0,\n    width: 0,\n    playerVars: {\n      ...opts.playerVars,\n      autoplay: 0,\n      start: 0,\n      end: 0,\n    },\n  };\n}\n\n/**\n * Check whether a `props` change should result in the player being reset.\n * The player is reset when the `props.opts` change, except if the only change\n * is in the `start` and `end` playerVars, because a video update can deal with\n * those.\n */\nfunction shouldResetPlayer(prevProps: YouTubeProps, props: YouTubeProps) {\n  return (\n    prevProps.videoId !== props.videoId || !isEqual(filterResetOptions(prevProps.opts), filterResetOptions(props.opts))\n  );\n}\n\n/**\n * Check whether a props change should result in an update of player.\n */\nfunction shouldUpdatePlayer(prevProps: YouTubeProps, props: YouTubeProps) {\n  return (\n    prevProps.id !== props.id ||\n    prevProps.className !== props.className ||\n    prevProps.opts?.width !== props.opts?.width ||\n    prevProps.opts?.height !== props.opts?.height ||\n    prevProps.iframeClassName !== props.iframeClassName ||\n    prevProps.title !== props.title\n  );\n}\n\ntype YoutubePlayerCueVideoOptions = {\n  videoId: string;\n  startSeconds?: number;\n  endSeconds?: number;\n  suggestedQuality?: string;\n};\n\nexport { YouTubePlayer };\n\nexport type YouTubeEvent<T = any> = {\n  data: T;\n  target: YouTubePlayer;\n};\n\nexport type YouTubeProps = {\n  /**\n   * The YouTube video ID.\n   *\n   * Examples\n   * - https://www.youtube.com/watch?v=XxVg_s8xAms (`XxVg_s8xAms` is the ID)\n   * - https://www.youtube.com/embed/-DX3vJiqxm4 (`-DX3vJiqxm4` is the ID)\n   */\n  videoId?: string;\n  /**\n   * Custom ID for the player element\n   */\n  id?: string;\n  /**\n   * Custom class name for the player element\n   */\n  className?: string;\n  /**\n   * Custom class name for the iframe element\n   */\n  iframeClassName?: string;\n  /**\n   * Custom style for the player container element\n   */\n  style?: React.CSSProperties;\n  /**\n   * Title of the video for the iframe's title tag.\n   */\n  title?: React.IframeHTMLAttributes<HTMLIFrameElement>['title'];\n  /**\n   * Indicates how the browser should load the iframe\n   * {@link https://developer.mozilla.org/en-US/docs/Web/HTML/Element/iframe#attr-loading}\n   */\n  loading?: React.IframeHTMLAttributes<HTMLIFrameElement>['loading'];\n  /**\n   * An object that specifies player options\n   * {@link https://developers.google.com/youtube/iframe_api_reference#Loading_a_Video_Player}\n   */\n  opts?: Options;\n  /**\n   * This event fires whenever a player has finished loading and is ready to begin receiving API calls.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onReady}\n   */\n  onReady?: (event: YouTubeEvent) => void;\n  /**\n   * This event fires if an error occurs in the player.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onError}\n   */\n  onError?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.PLAYING.\n   */\n  onPlay?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.PAUSED.\n   */\n  onPause?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires when the layer's state changes to PlayerState.ENDED.\n   */\n  onEnd?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the player's state changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onStateChange}\n   */\n  onStateChange?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the video playback quality changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange}\n   */\n  onPlaybackRateChange?: (event: YouTubeEvent<number>) => void;\n  /**\n   * This event fires whenever the video playback rate changes.\n   * {@link https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange}\n   */\n  onPlaybackQualityChange?: (event: YouTubeEvent<string>) => void;\n};\n\nconst defaultProps: YouTubeProps = {\n  videoId: '',\n  id: '',\n  className: '',\n  iframeClassName: '',\n  style: {},\n  title: '',\n  loading: undefined,\n  opts: {},\n  onReady: () => {},\n  onError: () => {},\n  onPlay: () => {},\n  onPause: () => {},\n  onEnd: () => {},\n  onStateChange: () => {},\n  onPlaybackRateChange: () => {},\n  onPlaybackQualityChange: () => {},\n};\n\nconst propTypes = {\n  videoId: PropTypes.string,\n  id: PropTypes.string,\n  className: PropTypes.string,\n  iframeClassName: PropTypes.string,\n  style: PropTypes.object,\n  title: PropTypes.string,\n  loading: PropTypes.oneOf(['lazy', 'eager']),\n  opts: PropTypes.objectOf(PropTypes.any),\n  onReady: PropTypes.func,\n  onError: PropTypes.func,\n  onPlay: PropTypes.func,\n  onPause: PropTypes.func,\n  onEnd: PropTypes.func,\n  onStateChange: PropTypes.func,\n  onPlaybackRateChange: PropTypes.func,\n  onPlaybackQualityChange: PropTypes.func,\n};\n\nclass YouTube extends React.Component<YouTubeProps> {\n  static propTypes = propTypes;\n  static defaultProps = defaultProps;\n\n  /**\n   * Expose PlayerState constants for convenience. These constants can also be\n   * accessed through the global YT object after the YouTube IFrame API is instantiated.\n   * https://developers.google.com/youtube/iframe_api_reference#onStateChange\n   */\n  static PlayerState = {\n    UNSTARTED: -1,\n    ENDED: 0,\n    PLAYING: 1,\n    PAUSED: 2,\n    BUFFERING: 3,\n    CUED: 5,\n  };\n\n  container: HTMLDivElement | null;\n  internalPlayer: YouTubePlayer | null;\n\n  constructor(props: any) {\n    super(props);\n\n    this.container = null;\n    this.internalPlayer = null;\n  }\n\n  /**\n   * Note: The `youtube-player` package that is used promisifies all YouTube\n   * Player API calls, which introduces a delay of a tick before it actually\n   * gets destroyed.\n   *\n   * The promise to destroy the player is stored here so we can make sure to\n   * only re-create the Player after it's been destroyed.\n   *\n   * See: https://github.com/tjallingt/react-youtube/issues/355\n   */\n  destroyPlayerPromise: Promise<void> | undefined = undefined;\n\n  componentDidMount() {\n    this.createPlayer();\n  }\n\n  async componentDidUpdate(prevProps: YouTubeProps) {\n    if (shouldUpdatePlayer(prevProps, this.props)) {\n      this.updatePlayer();\n    }\n\n    if (shouldResetPlayer(prevProps, this.props)) {\n      await this.resetPlayer();\n    }\n\n    if (shouldUpdateVideo(prevProps, this.props)) {\n      this.updateVideo();\n    }\n  }\n\n  componentWillUnmount() {\n    this.destroyPlayer();\n  }\n\n  /**\n   * This event fires whenever a player has finished loading and is ready to begin receiving API calls.\n   * https://developers.google.com/youtube/iframe_api_reference#onReady\n   */\n  onPlayerReady = (event: YouTubeEvent) => this.props.onReady?.(event);\n\n  /**\n   * This event fires if an error occurs in the player.\n   * https://developers.google.com/youtube/iframe_api_reference#onError\n   */\n  onPlayerError = (event: YouTubeEvent<number>) => this.props.onError?.(event);\n\n  /**\n   * This event fires whenever the video playback quality changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onStateChange\n   */\n  onPlayerStateChange = (event: YouTubeEvent<number>) => {\n    this.props.onStateChange?.(event);\n    // @ts-ignore\n    switch (event.data) {\n      case YouTube.PlayerState.ENDED:\n        this.props.onEnd?.(event);\n        break;\n\n      case YouTube.PlayerState.PLAYING:\n        this.props.onPlay?.(event);\n        break;\n\n      case YouTube.PlayerState.PAUSED:\n        this.props.onPause?.(event);\n        break;\n\n      default:\n    }\n  };\n\n  /**\n   * This event fires whenever the video playback quality changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onPlaybackRateChange\n   */\n  onPlayerPlaybackRateChange = (event: YouTubeEvent<number>) => this.props.onPlaybackRateChange?.(event);\n\n  /**\n   * This event fires whenever the video playback rate changes.\n   * https://developers.google.com/youtube/iframe_api_reference#onPlaybackQualityChange\n   */\n  onPlayerPlaybackQualityChange = (event: YouTubeEvent<string>) => this.props.onPlaybackQualityChange?.(event);\n\n  /**\n   * Destroy the YouTube Player using its async API and store the promise so we\n   * can await before re-creating it.\n   */\n  destroyPlayer = () => {\n    if (this.internalPlayer) {\n      this.destroyPlayerPromise = this.internalPlayer.destroy().then(() => (this.destroyPlayerPromise = undefined));\n      return this.destroyPlayerPromise;\n    }\n    return Promise.resolve();\n  };\n\n  /**\n   * Initialize the YouTube Player API on the container and attach event handlers\n   */\n  createPlayer = () => {\n    // do not attempt to create a player server-side, it won't work\n    if (typeof document === 'undefined') return;\n    if (this.destroyPlayerPromise) {\n      // We need to first await the existing player to be destroyed before\n      // we can re-create it.\n      this.destroyPlayerPromise.then(this.createPlayer);\n      return;\n    }\n    // create player\n    const playerOpts: Options = {\n      ...this.props.opts,\n      // preload the `videoId` video if one is already given\n      videoId: this.props.videoId,\n    };\n    this.internalPlayer = youTubePlayer(this.container!, playerOpts);\n    // attach event handlers\n    this.internalPlayer.on('ready', this.onPlayerReady as any);\n    this.internalPlayer.on('error', this.onPlayerError as any);\n    this.internalPlayer.on('stateChange', this.onPlayerStateChange as any);\n    this.internalPlayer.on('playbackRateChange', this.onPlayerPlaybackRateChange as any);\n    this.internalPlayer.on('playbackQualityChange', this.onPlayerPlaybackQualityChange as any);\n    if (this.props.title || this.props.loading) {\n      this.internalPlayer.getIframe().then((iframe) => {\n        if (this.props.title) iframe.setAttribute('title', this.props.title);\n        if (this.props.loading) iframe.setAttribute('loading', this.props.loading);\n      });\n    }\n  };\n\n  /**\n   * Shorthand for destroying and then re-creating the YouTube Player\n   */\n  resetPlayer = () => this.destroyPlayer().then(this.createPlayer);\n\n  /**\n   * Method to update the id and class of the YouTube Player iframe.\n   * React should update this automatically but since the YouTube Player API\n   * replaced the DIV that is mounted by React we need to do this manually.\n   */\n  updatePlayer = () => {\n    this.internalPlayer?.getIframe().then((iframe) => {\n      if (this.props.id) iframe.setAttribute('id', this.props.id);\n      else iframe.removeAttribute('id');\n      if (this.props.iframeClassName) iframe.setAttribute('class', this.props.iframeClassName);\n      else iframe.removeAttribute('class');\n      if (this.props.opts && this.props.opts.width) iframe.setAttribute('width', this.props.opts.width.toString());\n      else iframe.removeAttribute('width');\n      if (this.props.opts && this.props.opts.height) iframe.setAttribute('height', this.props.opts.height.toString());\n      else iframe.removeAttribute('height');\n      if (this.props.title) iframe.setAttribute('title', this.props.title);\n      else iframe.setAttribute('title', 'YouTube video player');\n      if (this.props.loading) iframe.setAttribute('loading', this.props.loading);\n      else iframe.removeAttribute('loading');\n    });\n  };\n\n  /**\n   *  Method to return the internalPlayer object.\n   */\n  getInternalPlayer = () => {\n    return this.internalPlayer;\n  };\n\n  /**\n   * Call YouTube Player API methods to update the currently playing video.\n   * Depending on the `opts.playerVars.autoplay` this function uses one of two\n   * YouTube Player API methods to update the video.\n   */\n  updateVideo = () => {\n    if (typeof this.props.videoId === 'undefined' || this.props.videoId === null) {\n      this.internalPlayer?.stopVideo();\n      return;\n    }\n\n    // set queueing options\n    let autoplay = false;\n    const opts: YoutubePlayerCueVideoOptions = {\n      videoId: this.props.videoId,\n    };\n\n    if (this.props.opts?.playerVars) {\n      autoplay = this.props.opts.playerVars.autoplay === 1;\n      if ('start' in this.props.opts.playerVars) {\n        opts.startSeconds = this.props.opts.playerVars.start;\n      }\n      if ('end' in this.props.opts.playerVars) {\n        opts.endSeconds = this.props.opts.playerVars.end;\n      }\n    }\n\n    // if autoplay is enabled loadVideoById\n    if (autoplay) {\n      this.internalPlayer?.loadVideoById(opts);\n      return;\n    }\n    // default behaviour just cues the video\n    this.internalPlayer?.cueVideoById(opts);\n  };\n\n  refContainer = (container: HTMLDivElement) => {\n    this.container = container;\n  };\n\n  render() {\n    return (\n      <div className={this.props.className} style={this.props.style}>\n        <div id={this.props.id} className={this.props.iframeClassName} ref={this.refContainer} />\n      </div>\n    );\n  }\n}\n\nexport default YouTube;\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA;AAMA,WAAO,UAAU,SAAS,MAAM,GAAG,GAAG;AACpC,UAAI,MAAM;AAAG,eAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE;AAAa,iBAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE;AAAQ,mBAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAG,qBAAO;AACjC,iBAAO;AAAA,QACT;AAIA,YAAI,EAAE,gBAAgB;AAAQ,iBAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAC5E,YAAI,EAAE,YAAY,OAAO,UAAU;AAAS,iBAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7E,YAAI,EAAE,aAAa,OAAO,UAAU;AAAU,iBAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAEjF,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,iBAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC;AAAG,mBAAO;AAEhE,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,cAAI,MAAM,KAAK,CAAC;AAEhB,cAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAG,mBAAO;AAAA,QACrC;AAEA,eAAO;AAAA,MACT;AAGA,aAAO,MAAI,KAAK,MAAI;AAAA,IACtB;AAAA;AAAA;;;AC7CA;AAAA;AAAA;AAEA,QAAI;AAMJ,aAAS,WAAY;AACjB,UAAI,SAAS,CAAC,GACV,SAAS,CAAC;AAad,aAAO,KAAK,SAAU,MAAM,SAAS;AACjC,YAAI,WAAW,EAAC,MAAY,QAAgB;AAC5C,eAAO,IAAI,IAAI,OAAO,IAAI,KAAK,CAAC;AAChC,eAAO,IAAI,EAAE,QAAQ,QAAQ;AAC7B,eAAO;AAAA,MACX;AAKA,aAAO,MAAM,SAAU,UAAU;AAC7B,YAAI,QAAQ,OAAO,SAAS,IAAI,EAAE,QAAQ,QAAQ;AAElD,YAAI,UAAU,IAAI;AACd,iBAAO,SAAS,IAAI,EAAE,OAAO,OAAO,CAAC;AAAA,QACzC;AAAA,MACJ;AAMA,aAAO,UAAU,SAAU,MAAM,MAAM;AACnC,YAAI,YAAY,OAAO,IAAI,GACvB;AAEJ,YAAI,WAAW;AACX,cAAI,UAAU;AACd,iBAAO,KAAK;AACR,sBAAU,CAAC,EAAE,QAAQ,IAAI;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ;AAEA,aAAO;AAAA,IACX;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5DjB;AAAA;AACA,WAAO,UAAU,SAAS,KAAM,KAAK,MAAM,IAAI;AAC7C,UAAI,OAAO,SAAS,QAAQ,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACnE,UAAI,SAAS,SAAS,cAAc,QAAQ;AAE5C,UAAI,OAAO,SAAS,YAAY;AAC9B,aAAK;AACL,eAAO,CAAC;AAAA,MACV;AAEA,aAAO,QAAQ,CAAC;AAChB,WAAK,MAAM,WAAW;AAAA,MAAC;AAEvB,aAAO,OAAO,KAAK,QAAQ;AAC3B,aAAO,UAAU,KAAK,WAAW;AACjC,aAAO,QAAQ,WAAW,OAAO,CAAC,CAAC,KAAK,QAAQ;AAChD,aAAO,MAAM;AAEb,UAAI,KAAK,OAAO;AACd,sBAAc,QAAQ,KAAK,KAAK;AAAA,MAClC;AAEA,UAAI,KAAK,MAAM;AACb,eAAO,OAAO,KAAK,KAAK;AAAA,MAC1B;AAEA,UAAI,QAAQ,YAAY,SAAS,WAAW;AAC5C,YAAM,QAAQ,EAAE;AAKhB,UAAI,CAAC,OAAO,QAAQ;AAClB,iBAAS,QAAQ,EAAE;AAAA,MACrB;AAEA,WAAK,YAAY,MAAM;AAAA,IACzB;AAEA,aAAS,cAAc,QAAQ,OAAO;AACpC,eAAS,QAAQ,OAAO;AACtB,eAAO,aAAa,MAAM,MAAM,IAAI,CAAC;AAAA,MACvC;AAAA,IACF;AAEA,aAAS,SAAU,QAAQ,IAAI;AAC7B,aAAO,SAAS,WAAY;AAC1B,aAAK,UAAU,KAAK,SAAS;AAC7B,WAAG,MAAM,MAAM;AAAA,MACjB;AACA,aAAO,UAAU,WAAY;AAG3B,aAAK,UAAU,KAAK,SAAS;AAC7B,WAAG,IAAI,MAAM,oBAAoB,KAAK,GAAG,GAAG,MAAM;AAAA,MACpD;AAAA,IACF;AAEA,aAAS,QAAS,QAAQ,IAAI;AAC5B,aAAO,qBAAqB,WAAY;AACtC,YAAI,KAAK,cAAc,cAAc,KAAK,cAAc;AAAU;AAClE,aAAK,qBAAqB;AAC1B,WAAG,MAAM,MAAM;AAAA,MACjB;AAAA,IACF;AAAA;AAAA;;;AChEA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,cAAc;AAElB,QAAI,eAAe,uBAAuB,WAAW;AAErD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU,SAAU,SAAS;AAKnC,UAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS;AAClD,YAAI,OAAO,MAAM,OAAO,GAAG,UAAU,OAAO,GAAG,kBAAkB,UAAU;AACzE,kBAAQ,OAAO,EAAE;AAEjB;AAAA,QACF,OAAO;AACL,cAAI,WAAW,OAAO,SAAS,aAAa,UAAU,UAAU;AAEhE,WAAC,GAAG,aAAa,SAAS,WAAW,gCAAgC,SAAU,OAAO;AACpF,gBAAI,OAAO;AACT,sBAAQ,QAAQ,SAAS,KAAK;AAAA,YAChC;AAAA,UACF,CAAC;AAAA,QACH;AAEA,YAAI,WAAW,OAAO;AAItB,eAAO,0BAA0B,WAAY;AAC3C,cAAI,UAAU;AACZ,qBAAS;AAAA,UACX;AAEA,kBAAQ,OAAO,EAAE;AAAA,QACnB;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;AChDlC;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AAgBZ,WAAO,UAAU,SAAS,KAAK,SAAS;AACtC,gBAAU,WAAW,CAAC;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,IAAI,SAAS,GAAG;AACvC,eAAO,MAAM,GAAG;AAAA,MAClB,WAAW,SAAS,YAAY,MAAM,GAAG,MAAM,OAAO;AACpD,eAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MACnD;AACA,YAAM,IAAI;AAAA,QACR,0DACE,KAAK,UAAU,GAAG;AAAA,MACtB;AAAA,IACF;AAUA,aAAS,MAAM,KAAK;AAClB,YAAM,OAAO,GAAG;AAChB,UAAI,IAAI,SAAS,KAAK;AACpB;AAAA,MACF;AACA,UAAI,QAAQ,wHAAwH;AAAA,QAClI;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,IAAI,WAAW,MAAM,CAAC,CAAC;AAC3B,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,YAAY;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,aAAS,SAAS,IAAI;AACpB,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,MAAM,GAAG;AACX,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK;AAAA,IACd;AAUA,aAAS,QAAQ,IAAI;AACnB,aAAO,OAAO,IAAI,GAAG,KAAK,KACxB,OAAO,IAAI,GAAG,MAAM,KACpB,OAAO,IAAI,GAAG,QAAQ,KACtB,OAAO,IAAI,GAAG,QAAQ,KACtB,KAAK;AAAA,IACT;AAMA,aAAS,OAAO,IAAI,GAAG,MAAM;AAC3B,UAAI,KAAK,GAAG;AACV;AAAA,MACF;AACA,UAAI,KAAK,IAAI,KAAK;AAChB,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI,MAAM;AAAA,MACpC;AACA,aAAO,KAAK,KAAK,KAAK,CAAC,IAAI,MAAM,OAAO;AAAA,IAC1C;AAAA;AAAA;;;ACvJA;AAAA;AAQA,cAAU,OAAO,UAAU,YAAY,QAAQ,YAAY,SAAS,IAAI;AACxE,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAClB,YAAQ,SAAS;AACjB,YAAQ,UAAU;AAClB,YAAQ,WAAW;AAMnB,YAAQ,QAAQ,CAAC;AACjB,YAAQ,QAAQ,CAAC;AAQjB,YAAQ,aAAa,CAAC;AAMtB,QAAI;AASJ,aAAS,YAAY,WAAW;AAC9B,UAAI,OAAO,GAAG;AAEd,WAAK,KAAK,WAAW;AACnB,gBAAU,QAAQ,KAAK,OAAQ,UAAU,WAAW,CAAC;AACrD,gBAAQ;AAAA,MACV;AAEA,aAAO,QAAQ,OAAO,KAAK,IAAI,IAAI,IAAI,QAAQ,OAAO,MAAM;AAAA,IAC9D;AAUA,aAAS,YAAY,WAAW;AAE9B,eAAS,QAAQ;AAEf,YAAI,CAAC,MAAM;AAAS;AAEpB,YAAI,OAAO;AAGX,YAAI,OAAO,CAAC,oBAAI,KAAK;AACrB,YAAI,KAAK,QAAQ,YAAY;AAC7B,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,aAAK,OAAO;AACZ,mBAAW;AAGX,YAAI,OAAO,IAAI,MAAM,UAAU,MAAM;AACrC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,CAAC,IAAI,UAAU,CAAC;AAAA,QACvB;AAEA,aAAK,CAAC,IAAI,QAAQ,OAAO,KAAK,CAAC,CAAC;AAEhC,YAAI,aAAa,OAAO,KAAK,CAAC,GAAG;AAE/B,eAAK,QAAQ,IAAI;AAAA,QACnB;AAGA,YAAI,QAAQ;AACZ,aAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,SAAS,OAAO,QAAQ;AAEjE,cAAI,UAAU;AAAM,mBAAO;AAC3B;AACA,cAAI,YAAY,QAAQ,WAAW,MAAM;AACzC,cAAI,eAAe,OAAO,WAAW;AACnC,gBAAI,MAAM,KAAK,KAAK;AACpB,oBAAQ,UAAU,KAAK,MAAM,GAAG;AAGhC,iBAAK,OAAO,OAAO,CAAC;AACpB;AAAA,UACF;AACA,iBAAO;AAAA,QACT,CAAC;AAGD,gBAAQ,WAAW,KAAK,MAAM,IAAI;AAElC,YAAI,QAAQ,MAAM,OAAO,QAAQ,OAAO,QAAQ,IAAI,KAAK,OAAO;AAChE,cAAM,MAAM,MAAM,IAAI;AAAA,MACxB;AAEA,YAAM,YAAY;AAClB,YAAM,UAAU,QAAQ,QAAQ,SAAS;AACzC,YAAM,YAAY,QAAQ,UAAU;AACpC,YAAM,QAAQ,YAAY,SAAS;AAGnC,UAAI,eAAe,OAAO,QAAQ,MAAM;AACtC,gBAAQ,KAAK,KAAK;AAAA,MACpB;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,OAAO,YAAY;AAC1B,cAAQ,KAAK,UAAU;AAEvB,cAAQ,QAAQ,CAAC;AACjB,cAAQ,QAAQ,CAAC;AAEjB,UAAI,SAAS,OAAO,eAAe,WAAW,aAAa,IAAI,MAAM,QAAQ;AAC7E,UAAI,MAAM,MAAM;AAEhB,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,CAAC,MAAM,CAAC;AAAG;AACf,qBAAa,MAAM,CAAC,EAAE,QAAQ,OAAO,KAAK;AAC1C,YAAI,WAAW,CAAC,MAAM,KAAK;AACzB,kBAAQ,MAAM,KAAK,IAAI,OAAO,MAAM,WAAW,OAAO,CAAC,IAAI,GAAG,CAAC;AAAA,QACjE,OAAO;AACL,kBAAQ,MAAM,KAAK,IAAI,OAAO,MAAM,aAAa,GAAG,CAAC;AAAA,QACvD;AAAA,MACF;AAAA,IACF;AAQA,aAAS,UAAU;AACjB,cAAQ,OAAO,EAAE;AAAA,IACnB;AAUA,aAAS,QAAQ,MAAM;AACrB,UAAI,GAAG;AACP,WAAK,IAAI,GAAG,MAAM,QAAQ,MAAM,QAAQ,IAAI,KAAK,KAAK;AACpD,YAAI,QAAQ,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,WAAK,IAAI,GAAG,MAAM,QAAQ,MAAM,QAAQ,IAAI,KAAK,KAAK;AACpD,YAAI,QAAQ,MAAM,CAAC,EAAE,KAAK,IAAI,GAAG;AAC/B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAUA,aAAS,OAAO,KAAK;AACnB,UAAI,eAAe;AAAO,eAAO,IAAI,SAAS,IAAI;AAClD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACzMA;AAAA;AAMA,cAAU,OAAO,UAAU;AAC3B,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,eAAe,OAAO,UACtB,eAAe,OAAO,OAAO,UAC3B,OAAO,QAAQ,QACf,aAAa;AAMjC,YAAQ,SAAS;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAUA,aAAS,YAAY;AAInB,UAAI,OAAO,WAAW,eAAe,OAAO,WAAW,OAAO,QAAQ,SAAS,YAAY;AACzF,eAAO;AAAA,MACT;AAIA,aAAQ,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MAErI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAG1H,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,KAAK,SAAS,OAAO,IAAI,EAAE,KAAK;AAAA,MAEnJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IAC5H;AAMA,YAAQ,WAAW,IAAI,SAAS,GAAG;AACjC,UAAI;AACF,eAAO,KAAK,UAAU,CAAC;AAAA,MACzB,SAAS,KAAK;AACZ,eAAO,iCAAiC,IAAI;AAAA,MAC9C;AAAA,IACF;AASA,aAAS,WAAW,MAAM;AACxB,UAAIA,aAAY,KAAK;AAErB,WAAK,CAAC,KAAKA,aAAY,OAAO,MAC1B,KAAK,aACJA,aAAY,QAAQ,OACrB,KAAK,CAAC,KACLA,aAAY,QAAQ,OACrB,MAAM,QAAQ,SAAS,KAAK,IAAI;AAEpC,UAAI,CAACA;AAAW;AAEhB,UAAI,IAAI,YAAY,KAAK;AACzB,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAKrC,UAAI,QAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,SAAS,OAAO;AAC7C,YAAI,SAAS;AAAO;AACpB;AACA,YAAI,SAAS,OAAO;AAGlB,kBAAQ;AAAA,QACV;AAAA,MACF,CAAC;AAED,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACzB;AASA,aAAS,MAAM;AAGb,aAAO,aAAa,OAAO,WACtB,QAAQ,OACR,SAAS,UAAU,MAAM,KAAK,QAAQ,KAAK,SAAS,SAAS;AAAA,IACpE;AASA,aAAS,KAAK,YAAY;AACxB,UAAI;AACF,YAAI,QAAQ,YAAY;AACtB,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACpC,OAAO;AACL,kBAAQ,QAAQ,QAAQ;AAAA,QAC1B;AAAA,MACF,SAAQ,GAAG;AAAA,MAAC;AAAA,IACd;AASA,aAAS,OAAO;AACd,UAAI;AACJ,UAAI;AACF,YAAI,QAAQ,QAAQ;AAAA,MACtB,SAAQ,GAAG;AAAA,MAAC;AAGZ,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC5D,YAAI,QAAQ,IAAI;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAMA,YAAQ,OAAO,KAAK,CAAC;AAarB,aAAS,eAAe;AACtB,UAAI;AACF,eAAO,OAAO;AAAA,MAChB,SAAS,GAAG;AAAA,MAAC;AAAA,IACf;AAAA;AAAA;;;ACxLA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAMD,YAAQ,UAAU,CAAC,gBAAgB,iBAAiB,iBAAiB,kBAAkB,aAAa,cAAc,aAAa,0BAA0B,eAAe,gBAAgB,aAAa,iBAAiB,eAAe,cAAc,WAAW,eAAe,oBAAoB,aAAa,QAAQ,UAAU,WAAW,aAAa,aAAa,UAAU,kBAAkB,mBAAmB,mBAAmB,6BAA6B,sBAAsB,sBAAsB,6BAA6B,kBAAkB,eAAe,uBAAuB,eAAe,qBAAqB,cAAc,aAAa,oBAAoB,WAAW,WAAW,WAAW;AACzrB,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACXlC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAQD,YAAQ,UAAU,CAAC,SAAS,eAAe,yBAAyB,sBAAsB,SAAS,aAAa,cAAc;AAC9H,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACblC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAAA,MAChB,WAAW;AAAA,MACX,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,WAAW;AAAA,MACX,YAAY;AAAA,IACd;AACA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACblC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,YAAQ,UAAU;AAAA,MAChB,YAAY;AAAA,QACV,kBAAkB,CAAC,eAAe,QAAQ,OAAO,eAAe,QAAQ,MAAM;AAAA,QAC9E,qBAAqB;AAAA,MACvB;AAAA,MACA,WAAW;AAAA,QACT,kBAAkB,CAAC,eAAe,QAAQ,OAAO,eAAe,QAAQ,OAAO;AAAA,QAC/E,qBAAqB;AAAA,MACvB;AAAA,MACA,QAAQ;AAAA,QACN,kBAAkB,CAAC,eAAe,QAAQ,OAAO,eAAe,QAAQ,SAAS,eAAe,QAAQ,MAAM;AAAA,QAC9G,qBAAqB;AAAA;AAAA;AAAA,QAIrB,SAAS;AAAA,MACX;AAAA,IACF;AACA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;AC9BlC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,SAAS;AAEb,QAAI,UAAU,uBAAuB,MAAM;AAE3C,QAAI,iBAAiB;AAErB,QAAI,kBAAkB,uBAAuB,cAAc;AAE3D,QAAI,cAAc;AAElB,QAAI,eAAe,uBAAuB,WAAW;AAErD,QAAI,oBAAoB;AAExB,QAAI,qBAAqB,uBAAuB,iBAAiB;AAEjE,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAI9F,QAAI,SAAS,GAAG,QAAQ,SAAS,gBAAgB;AAEjD,QAAI,gBAAgB,CAAC;AASrB,kBAAc,cAAc,SAAU,SAAS;AAC7C,UAAI,SAAS,CAAC;AAEd,UAAI,QAAQ,SAASC,OAAMC,YAAW;AACpC,YAAI,cAAc,OAAOA,WAAU,MAAM,GAAG,CAAC,EAAE,YAAY,IAAIA,WAAU,MAAM,CAAC;AAEhF,eAAO,WAAW,IAAI,SAAU,OAAO;AACrC,gBAAM,cAAc,aAAa,KAAK;AAEtC,kBAAQ,QAAQA,YAAW,KAAK;AAAA,QAClC;AAAA,MACF;AAEA,UAAI,4BAA4B;AAChC,UAAI,oBAAoB;AACxB,UAAI,iBAAiB;AAErB,UAAI;AACF,iBAAS,YAAY,aAAa,QAAQ,OAAO,QAAQ,EAAE,GAAG,OAAO,EAAE,6BAA6B,QAAQ,UAAU,KAAK,GAAG,OAAO,4BAA4B,MAAM;AACrK,cAAI,YAAY,MAAM;AAEtB,gBAAM,SAAS;AAAA,QACjB;AAAA,MACF,SAAS,KAAK;AACZ,4BAAoB;AACpB,yBAAiB;AAAA,MACnB,UAAE;AACA,YAAI;AACF,cAAI,CAAC,6BAA6B,UAAU,QAAQ;AAClD,sBAAU,OAAO;AAAA,UACnB;AAAA,QACF,UAAE;AACA,cAAI,mBAAmB;AACrB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAYA,kBAAc,kBAAkB,SAAU,gBAAgB;AACxD,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEtF,UAAI,YAAY,CAAC;AAEjB,UAAI,SAAS,SAASC,QAAOC,eAAc;AACzC,YAAI,eAAe,mBAAmB,QAAQA,aAAY,GAAG;AAC3D,oBAAUA,aAAY,IAAI,WAAY;AACpC,qBAAS,OAAO,UAAU,QAAQ,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACnF,mBAAK,IAAI,IAAI,UAAU,IAAI;AAAA,YAC7B;AAEA,mBAAO,eAAe,KAAK,SAAU,QAAQ;AAC3C,kBAAI,YAAY,mBAAmB,QAAQA,aAAY;AACvD,kBAAI,cAAc,OAAO,eAAe;AAOxC,kBAAI,QAAQ,OAAOA,aAAY,EAAE,MAAM,QAAQ,IAAI;AAKnD,kBAAI,UAAU;AAAA,cAGd,MAAM,QAAQ,UAAU,gBAAgB,KAAK,UAAU,iBAAiB,QAAQ,WAAW,MAAM,IAAI;AACnG,uBAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,sBAAI,sBAAsB,SAASC,uBAAsB;AACvD,wBAAI,yBAAyB,OAAO,eAAe;AAEnD,wBAAI,UAAU;AAEd,wBAAI,OAAO,UAAU,YAAY,UAAU;AACzC,gCAAU,WAAW,WAAY;AAC/B,+BAAO,oBAAoB,iBAAiBA,oBAAmB;AAE/D,gCAAQ;AAAA,sBACV,GAAG,UAAU,OAAO;AAAA,oBACtB;AAEA,wBAAI,MAAM,QAAQ,UAAU,gBAAgB,KAAK,UAAU,iBAAiB,QAAQ,sBAAsB,MAAM,IAAI;AAClH,6BAAO,oBAAoB,iBAAiBA,oBAAmB;AAE/D,mCAAa,OAAO;AAEpB,8BAAQ;AAAA,oBACV;AAAA,kBACF;AAEA,yBAAO,iBAAiB,iBAAiB,mBAAmB;AAAA,gBAC9D,CAAC,EAAE,KAAK,WAAY;AAClB,yBAAO;AAAA,gBACT,CAAC;AAAA,cACH;AAEA,qBAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,oBAAUD,aAAY,IAAI,WAAY;AACpC,qBAAS,QAAQ,UAAU,QAAQ,OAAO,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AACzF,mBAAK,KAAK,IAAI,UAAU,KAAK;AAAA,YAC/B;AAEA,mBAAO,eAAe,KAAK,SAAU,QAAQ;AAM3C,qBAAO,OAAOA,aAAY,EAAE,MAAM,QAAQ,IAAI;AAAA,YAChD,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,UAAI,6BAA6B;AACjC,UAAI,qBAAqB;AACzB,UAAI,kBAAkB;AAEtB,UAAI;AACF,iBAAS,aAAa,gBAAgB,QAAQ,OAAO,QAAQ,EAAE,GAAG,QAAQ,EAAE,8BAA8B,SAAS,WAAW,KAAK,GAAG,OAAO,6BAA6B,MAAM;AAC9K,cAAI,eAAe,OAAO;AAE1B,iBAAO,YAAY;AAAA,QACrB;AAAA,MACF,SAAS,KAAK;AACZ,6BAAqB;AACrB,0BAAkB;AAAA,MACpB,UAAE;AACA,YAAI;AACF,cAAI,CAAC,8BAA8B,WAAW,QAAQ;AACpD,uBAAW,OAAO;AAAA,UACpB;AAAA,QACF,UAAE;AACA,cAAI,oBAAoB;AACtB,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU;AAClB,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;ACrMlC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,IAAK;AAE3Q,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,wBAAwB;AAE5B,QAAI,yBAAyB,uBAAuB,qBAAqB;AAEzE,QAAI,iBAAiB;AAErB,QAAI,kBAAkB,uBAAuB,cAAc;AAE3D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAU9F,QAAI,mBAAmB;AAavB,YAAQ,UAAU,SAAU,gBAAgB;AAC1C,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,cAAc,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEtF,UAAI,WAAW,GAAG,SAAS,SAAS;AAEpC,UAAI,CAAC,kBAAkB;AACrB,4BAAoB,GAAG,uBAAuB,SAAS,OAAO;AAAA,MAChE;AAEA,UAAI,QAAQ,QAAQ;AAClB,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAEA,UAAI,OAAO,mBAAmB,YAAY,CAAC,SAAS,eAAe,cAAc,GAAG;AAClF,cAAM,IAAI,MAAM,cAAc,iBAAiB,mBAAmB;AAAA,MACpE;AAEA,cAAQ,SAAS,gBAAgB,QAAQ,YAAY,OAAO;AAE5D,UAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS;AAClD,aAAK,OAAO,mBAAmB,cAAc,cAAc,QAAQ,cAAc,OAAO,YAAY,eAAe,qBAAqB,UAAU;AAChJ,cAAI,SAAS;AAEb,kBAAQ,MAAM;AAAA,QAChB,OAAO;AAGL,2BAAiB,KAAK,SAAU,IAAI;AAElC,gBAAIE,UAAS,IAAI,GAAG,OAAO,gBAAgB,OAAO;AAElD,oBAAQ,GAAG,SAAS,WAAY;AAC9B,sBAAQA,OAAM;AAAA,YAChB,CAAC;AAED,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAED,UAAI,YAAY,gBAAgB,QAAQ,gBAAgB,gBAAgB,WAAW;AAEnF,gBAAU,KAAK,QAAQ;AACvB,gBAAU,MAAM,QAAQ;AAExB,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,QAAQ,SAAS;AAAA;AAAA;;;AC3FlC,wBAAsB;AACtB,mBAAkB;AAClB,6BAAoB;AACpB,4BAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B,SAAS,kBAAkB,WAAyB,OAAqB;AAVzE,MAAA,IAAA;AAYE,MAAI,UAAU,YAAY,MAAM,SAAS;AACvC,WAAO;EACT;AAIA,QAAM,aAAW,KAAA,UAAU,SAAV,OAAA,SAAA,GAAgB,eAAc,CAAC;AAChD,QAAM,SAAO,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,eAAc,CAAC;AAExC,SAAO,SAAS,UAAU,KAAK,SAAS,SAAS,QAAQ,KAAK;AAChE;AAOA,SAAS,mBAAmB,OAAgB,CAAC,GAAG;AAC9C,SAAO,cAAA,eAAA,CAAA,GACF,IAAA,GADE;IAEL,QAAQ;IACR,OAAO;IACP,YAAY,cAAA,eAAA,CAAA,GACP,KAAK,UAAA,GADE;MAEV,UAAU;MACV,OAAO;MACP,KAAK;IACP,CAAA;EACF,CAAA;AACF;AAQA,SAAS,kBAAkB,WAAyB,OAAqB;AACvE,SACE,UAAU,YAAY,MAAM,WAAW,KAAC,uBAAAC,SAAQ,mBAAmB,UAAU,IAAI,GAAG,mBAAmB,MAAM,IAAI,CAAC;AAEtH;AAKA,SAAS,mBAAmB,WAAyB,OAAqB;AA1D1E,MAAA,IAAA,IAAA,IAAA;AA2DE,SACE,UAAU,OAAO,MAAM,MACvB,UAAU,cAAc,MAAM,eAC9B,KAAA,UAAU,SAAV,OAAA,SAAA,GAAgB,aAAU,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,YACtC,KAAA,UAAU,SAAV,OAAA,SAAA,GAAgB,cAAW,KAAA,MAAM,SAAN,OAAA,SAAA,GAAY,WACvC,UAAU,oBAAoB,MAAM,mBACpC,UAAU,UAAU,MAAM;AAE9B;AA8FA,IAAM,eAA6B;EACjC,SAAS;EACT,IAAI;EACJ,WAAW;EACX,iBAAiB;EACjB,OAAO,CAAC;EACR,OAAO;EACP,SAAS;EACT,MAAM,CAAC;EACP,SAAS,MAAM;EAAC;EAChB,SAAS,MAAM;EAAC;EAChB,QAAQ,MAAM;EAAC;EACf,SAAS,MAAM;EAAC;EAChB,OAAO,MAAM;EAAC;EACd,eAAe,MAAM;EAAC;EACtB,sBAAsB,MAAM;EAAC;EAC7B,yBAAyB,MAAM;EAAC;AAClC;AAEA,IAAM,YAAY;EAChB,SAAS,kBAAAC,QAAU;EACnB,IAAI,kBAAAA,QAAU;EACd,WAAW,kBAAAA,QAAU;EACrB,iBAAiB,kBAAAA,QAAU;EAC3B,OAAO,kBAAAA,QAAU;EACjB,OAAO,kBAAAA,QAAU;EACjB,SAAS,kBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;EAC1C,MAAM,kBAAAA,QAAU,SAAS,kBAAAA,QAAU,GAAG;EACtC,SAAS,kBAAAA,QAAU;EACnB,SAAS,kBAAAA,QAAU;EACnB,QAAQ,kBAAAA,QAAU;EAClB,SAAS,kBAAAA,QAAU;EACnB,OAAO,kBAAAA,QAAU;EACjB,eAAe,kBAAAA,QAAU;EACzB,sBAAsB,kBAAAA,QAAU;EAChC,yBAAyB,kBAAAA,QAAU;AACrC;AAEA,IAAM,WAAN,cAAsB,aAAAC,QAAM,UAAwB;EAqBlD,YAAY,OAAY;AACtB,UAAM,KAAK;AAgBb,SAAA,uBAAkD;AA4BlD,SAAA,gBAAgB,CAAC,UAAqB;AAzQxC,UAAA,IAAA;AAyQ2C,cAAA,MAAA,KAAA,KAAK,OAAM,YAAX,OAAA,SAAA,GAAA,KAAA,IAAqB,KAAA;IAAA;AAM9D,SAAA,gBAAgB,CAAC,UAA6B;AA/QhD,UAAA,IAAA;AA+QmD,cAAA,MAAA,KAAA,KAAK,OAAM,YAAX,OAAA,SAAA,GAAA,KAAA,IAAqB,KAAA;IAAA;AAMtE,SAAA,sBAAsB,CAAC,UAAgC;AArRzD,UAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA,IAAA;AAsRI,OAAA,MAAA,KAAA,KAAK,OAAM,kBAAX,OAAA,SAAA,GAAA,KAAA,IAA2B,KAAA;AAE3B,cAAQ,MAAM,MAAA;QAAA,KACP,SAAQ,YAAY;AACvB,WAAA,MAAA,KAAA,KAAK,OAAM,UAAX,OAAA,SAAA,GAAA,KAAA,IAAmB,KAAA;AACnB;QAAA,KAEG,SAAQ,YAAY;AACvB,WAAA,MAAA,KAAA,KAAK,OAAM,WAAX,OAAA,SAAA,GAAA,KAAA,IAAoB,KAAA;AACpB;QAAA,KAEG,SAAQ,YAAY;AACvB,WAAA,MAAA,KAAA,KAAK,OAAM,YAAX,OAAA,SAAA,GAAA,KAAA,IAAqB,KAAA;AACrB;QAAA;MAAA;IAIN;AAMA,SAAA,6BAA6B,CAAC,UAA6B;AA7S7D,UAAA,IAAA;AA6SgE,cAAA,MAAA,KAAA,KAAK,OAAM,yBAAX,OAAA,SAAA,GAAA,KAAA,IAAkC,KAAA;IAAA;AAMhG,SAAA,gCAAgC,CAAC,UAA6B;AAnThE,UAAA,IAAA;AAmTmE,cAAA,MAAA,KAAA,KAAK,OAAM,4BAAX,OAAA,SAAA,GAAA,KAAA,IAAqC,KAAA;IAAA;AAMtG,SAAA,gBAAgB,MAAM;AACpB,UAAI,KAAK,gBAAgB;AACvB,aAAK,uBAAuB,KAAK,eAAe,QAAQ,EAAE,KAAK,MAAO,KAAK,uBAAuB,MAAU;AAC5G,eAAO,KAAK;MACd;AACA,aAAO,QAAQ,QAAQ;IACzB;AAKA,SAAA,eAAe,MAAM;AAEnB,UAAI,OAAO,aAAa;AAAa;AACrC,UAAI,KAAK,sBAAsB;AAG7B,aAAK,qBAAqB,KAAK,KAAK,YAAY;AAChD;MACF;AAEA,YAAM,aAAsB,cAAA,eAAA,CAAA,GACvB,KAAK,MAAM,IAAA,GADY;QAG1B,SAAS,KAAK,MAAM;MACtB,CAAA;AACA,WAAK,qBAAiB,sBAAAC,SAAc,KAAK,WAAY,UAAU;AAE/D,WAAK,eAAe,GAAG,SAAS,KAAK,aAAoB;AACzD,WAAK,eAAe,GAAG,SAAS,KAAK,aAAoB;AACzD,WAAK,eAAe,GAAG,eAAe,KAAK,mBAA0B;AACrE,WAAK,eAAe,GAAG,sBAAsB,KAAK,0BAAiC;AACnF,WAAK,eAAe,GAAG,yBAAyB,KAAK,6BAAoC;AACzF,UAAI,KAAK,MAAM,SAAS,KAAK,MAAM,SAAS;AAC1C,aAAK,eAAe,UAAU,EAAE,KAAK,CAAC,WAAW;AAC/C,cAAI,KAAK,MAAM;AAAO,mBAAO,aAAa,SAAS,KAAK,MAAM,KAAK;AACnE,cAAI,KAAK,MAAM;AAAS,mBAAO,aAAa,WAAW,KAAK,MAAM,OAAO;QAC3E,CAAC;MACH;IACF;AAKA,SAAA,cAAc,MAAM,KAAK,cAAc,EAAE,KAAK,KAAK,YAAY;AAO/D,SAAA,eAAe,MAAM;AA5WvB,UAAA;AA6WI,OAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,UAAA,EAAY,KAAK,CAAC,WAAW;AAChD,YAAI,KAAK,MAAM;AAAI,iBAAO,aAAa,MAAM,KAAK,MAAM,EAAE;;AACrD,iBAAO,gBAAgB,IAAI;AAChC,YAAI,KAAK,MAAM;AAAiB,iBAAO,aAAa,SAAS,KAAK,MAAM,eAAe;;AAClF,iBAAO,gBAAgB,OAAO;AACnC,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAAO,iBAAO,aAAa,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;;AACtG,iBAAO,gBAAgB,OAAO;AACnC,YAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK;AAAQ,iBAAO,aAAa,UAAU,KAAK,MAAM,KAAK,OAAO,SAAS,CAAC;;AACzG,iBAAO,gBAAgB,QAAQ;AACpC,YAAI,KAAK,MAAM;AAAO,iBAAO,aAAa,SAAS,KAAK,MAAM,KAAK;;AAC9D,iBAAO,aAAa,SAAS,sBAAsB;AACxD,YAAI,KAAK,MAAM;AAAS,iBAAO,aAAa,WAAW,KAAK,MAAM,OAAO;;AACpE,iBAAO,gBAAgB,SAAS;MACvC,CAAA;IACF;AAKA,SAAA,oBAAoB,MAAM;AACxB,aAAO,KAAK;IACd;AAOA,SAAA,cAAc,MAAM;AAzYtB,UAAA,IAAA,IAAA,IAAA;AA0YI,UAAI,OAAO,KAAK,MAAM,YAAY,eAAe,KAAK,MAAM,YAAY,MAAM;AAC5E,SAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,UAAA;AACrB;MACF;AAGA,UAAI,WAAW;AACf,YAAM,OAAqC;QACzC,SAAS,KAAK,MAAM;MACtB;AAEA,WAAI,KAAA,KAAK,MAAM,SAAX,OAAA,SAAA,GAAiB,YAAY;AAC/B,mBAAW,KAAK,MAAM,KAAK,WAAW,aAAa;AACnD,YAAI,WAAW,KAAK,MAAM,KAAK,YAAY;AACzC,eAAK,eAAe,KAAK,MAAM,KAAK,WAAW;QACjD;AACA,YAAI,SAAS,KAAK,MAAM,KAAK,YAAY;AACvC,eAAK,aAAa,KAAK,MAAM,KAAK,WAAW;QAC/C;MACF;AAGA,UAAI,UAAU;AACZ,SAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,cAAc,IAAA;AACnC;MACF;AAEA,OAAA,KAAA,KAAK,mBAAL,OAAA,SAAA,GAAqB,aAAa,IAAA;IACpC;AAEA,SAAA,eAAe,CAAC,cAA8B;AAC5C,WAAK,YAAY;IACnB;AA3ME,SAAK,YAAY;AACjB,SAAK,iBAAiB;EACxB;EAcA,oBAAoB;AAClB,SAAK,aAAa;EACpB;EAEM,mBAAmB,WAAyB;AAAA,WAAA,QAAA,MAAA,MAAA,aAAA;AAChD,UAAI,mBAAmB,WAAW,KAAK,KAAK,GAAG;AAC7C,aAAK,aAAa;MACpB;AAEA,UAAI,kBAAkB,WAAW,KAAK,KAAK,GAAG;AAC5C,cAAM,KAAK,YAAY;MACzB;AAEA,UAAI,kBAAkB,WAAW,KAAK,KAAK,GAAG;AAC5C,aAAK,YAAY;MACnB;IACF,CAAA;EAAA;EAEA,uBAAuB;AACrB,SAAK,cAAc;EACrB;EAyKA,SAAS;AACP,WACE,aAAAD,QAAA,cAAC,OAAA;MAAI,WAAW,KAAK,MAAM;MAAW,OAAO,KAAK,MAAM;IAAA,GACtD,aAAAA,QAAA,cAAC,OAAA;MAAI,IAAI,KAAK,MAAM;MAAI,WAAW,KAAK,MAAM;MAAiB,KAAK,KAAK;IAAA,CAAc,CACzF;EAEJ;AACF;AA5OA,IAAM,UAAN;AAAM,QACG,YAAY;AADf,QAEG,eAAe;AAFlB,QASG,cAAc;EACnB,WAAW;EACX,OAAO;EACP,SAAS;EACT,QAAQ;EACR,WAAW;EACX,MAAM;AACR;AA8NF,IAAO,kBAAQ;", "names": ["useColors", "_loop", "eventName", "_loop2", "functionName", "onPlayerStateChange", "player", "isEqual", "PropTypes", "React", "youTubePlayer"]}