import { TestDataSource, initializeTestDatabase, cleanTestData, closeTestDatabase } from '../config/test-database';
import { AppDataSource } from '../config/database';
import { Repository } from 'typeorm';
import { Restaurant } from '../models/Restaurant';
import { Suggestion, SuggestionStatus } from '../models/Suggestion';
import PlaybackQueueService from '../services/PlaybackQueueService';
import { PlaybackService } from '../services/PlaybackService';

jest.mock('../services/WebSocketService', () => ({
  WebSocketService: { getInstance: () => ({ emitToRestaurant: jest.fn(), emitToAdmins: jest.fn() }) }
}));

jest.mock('../config/redis', () => ({
  redisClient: { isReady: false, getClient: () => ({ mGet: jest.fn().mockResolvedValue([]) }) }
}));

describe('Fila ignora sugestões antigas (só hoje)', () => {
  let restaurantRepo: Repository<Restaurant>;
  let suggestionRepo: Repository<Suggestion>;
  let rest: Restaurant;

  beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    await initializeTestDatabase();
    (AppDataSource as any).getRepository = (e: any) => TestDataSource.getRepository(e);
    (AppDataSource as any).isInitialized = true;

    restaurantRepo = TestDataSource.getRepository(Restaurant);
    suggestionRepo = TestDataSource.getRepository(Suggestion);
  });

  afterAll(async () => { await cleanTestData(); await closeTestDatabase(); });

  beforeEach(async () => {
    await cleanTestData();
    rest = restaurantRepo.create({ id: 'r-old', name: 'R' });
    await restaurantRepo.save(rest);
  });

  it('PlaybackQueueService: ignora sugestões que não são do dia atual', async () => {
    const old = suggestionRepo.create({
      youtubeVideoId: 'OLD111', title: 'Ontem', artist: 'A', duration: 180,
      restaurant: rest, status: SuggestionStatus.APPROVED,
      createdAt: new Date(Date.now() - 24*60*60*1000), updatedAt: new Date()
    } as any);
    await suggestionRepo.save(old);

    const qsvc = new PlaybackQueueService();
    const { queue } = await qsvc.getPlaybackQueue(rest.id);
    expect(queue.length).toBe(0);
  });

  it('PlaybackService: ignora sugestões que não são do dia atual', async () => {
    const old = suggestionRepo.create({
      youtubeVideoId: 'OLD222', title: 'Ontem2', artist: 'B', duration: 200,
      restaurant: rest, status: SuggestionStatus.APPROVED,
      createdAt: new Date(Date.now() - 24*60*60*1000), updatedAt: new Date()
    } as any);
    await suggestionRepo.save(old);

    const psvc = PlaybackService.getInstance();
    const state = await psvc.initializePlayback(rest.id);
    expect(state.queue.length).toBe(0);
  });
});
