{"version": 3, "file": "UnifiedAnalytics-164ae950.js", "sources": ["../../src/components/restaurant/CompetitiveAnalytics.tsx", "../../src/components/restaurant/UnifiedAnalytics.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { useRestaurantContext } from \"./RestaurantDashboard\";\r\nimport { buildApiUrl } from \"../../config/api\";\r\nimport {\r\n  TrendingUp,\r\n  DollarSign,\r\n  Star,\r\n  Users,\r\n  Trophy,\r\n  Clock,\r\n  CreditCard,\r\n  Vote,\r\n  Target,\r\n  BarChart3,\r\n  Pie<PERSON>hart,\r\n  Calendar,\r\n} from \"lucide-react\";\r\n\r\ninterface CompetitiveAnalytics {\r\n  period: string;\r\n  revenue: {\r\n    total: number;\r\n    totalPayments: number;\r\n    averageAmount: number;\r\n    dailyRevenue: Array<{ date: string; revenue: number; count: number }>;\r\n  };\r\n  voting: {\r\n    totalVotes: number;\r\n    totalPerformances: number;\r\n    averageRating: number;\r\n    topPerformers: Array<{\r\n      rank: number;\r\n      clientName: string;\r\n      tableName: string;\r\n      songTitle: string;\r\n      averageRating: number;\r\n      totalVotes: number;\r\n    }>;\r\n  };\r\n  queue: {\r\n    totalItems: number;\r\n    paidItems: number;\r\n    freeItems: number;\r\n    paidPercentage: number;\r\n  };\r\n  engagement: {\r\n    paymentConversionRate: number;\r\n    votingParticipationRate: number;\r\n    averageVotesPerSong: number;\r\n  };\r\n  trends: {\r\n    hourlyRevenue: Array<{ hour: number; revenue: number; count: number }>;\r\n    topTables: Array<{\r\n      tableName: string;\r\n      totalVotes: number;\r\n      performances: number;\r\n      averageRating: number;\r\n    }>;\r\n  };\r\n}\r\n\r\ninterface CompetitiveAnalyticsProps {\r\n  restaurantId?: string; // Opcional, pois será obtido do contexto\r\n}\r\n\r\nconst CompetitiveAnalytics: React.FC<CompetitiveAnalyticsProps> = ({\r\n  restaurantId: propRestaurantId,\r\n}) => {\r\n  const { restaurantId: contextRestaurantId } = useRestaurantContext();\r\n  const restaurantId = propRestaurantId || contextRestaurantId;\r\n\r\n  const [analytics, setAnalytics] = useState<CompetitiveAnalytics | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [period, setPeriod] = useState<\"1d\" | \"7d\" | \"30d\">(\"1d\");\r\n\r\n  useEffect(() => {\r\n    loadAnalytics();\r\n  }, [restaurantId, period]);\r\n\r\n  const loadAnalytics = async () => {\r\n    if (!restaurantId) {\r\n      console.error(\"Restaurant ID não encontrado\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n\r\n      const response = await fetch(\r\n        buildApiUrl(`/analytics/competitive/${restaurantId}?period=${period}`)\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setAnalytics(data.analytics);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar analytics competitivos:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatCurrency = (value: number): string => {\r\n    return new Intl.NumberFormat(\"pt-BR\", {\r\n      style: \"currency\",\r\n      currency: \"BRL\",\r\n    }).format(value);\r\n  };\r\n\r\n  const formatPercentage = (value: number): string => {\r\n    return `${value.toFixed(1)}%`;\r\n  };\r\n\r\n  const getPeriodLabel = (period: string): string => {\r\n    const labels = {\r\n      \"1d\": \"Últimas 24h\",\r\n      \"7d\": \"Últimos 7 dias\",\r\n      \"30d\": \"Últimos 30 dias\",\r\n    };\r\n    return labels[period as keyof typeof labels] || period;\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n        <div className=\"flex items-center justify-center space-x-2\">\r\n          <div className=\"w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin\" />\r\n          <span className=\"text-gray-600\">\r\n            Carregando analytics competitivos...\r\n          </span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!analytics) {\r\n    return (\r\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n        <div className=\"text-center text-gray-500\">\r\n          <BarChart3 className=\"w-12 h-12 mx-auto mb-3 opacity-50\" />\r\n          <p>Nenhum dado disponível</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h2 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">\r\n              <Trophy className=\"w-6 h-6 text-yellow-500\" />\r\n              <span>Analytics Competitivos</span>\r\n            </h2>\r\n            <p className=\"text-gray-600 mt-1\">\r\n              Métricas de votação, receita e engajamento -{\" \"}\r\n              {getPeriodLabel(period)}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            {([\"1d\", \"7d\", \"30d\"] as const).map((p) => (\r\n              <button\r\n                key={p}\r\n                onClick={() => setPeriod(p)}\r\n                className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${\r\n                  period === p\r\n                    ? \"bg-blue-600 text-white\"\r\n                    : \"bg-gray-100 text-gray-600 hover:bg-gray-200\"\r\n                }`}\r\n              >\r\n                {getPeriodLabel(p)}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* KPI Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n        {/* Receita Total */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"bg-white rounded-lg shadow-sm p-6\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">Receita Total</p>\r\n              <p className=\"text-2xl font-bold text-green-600\">\r\n                {formatCurrency(analytics.revenue.total)}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                {analytics.revenue.totalPayments} pagamentos\r\n              </p>\r\n            </div>\r\n            <div className=\"p-3 bg-green-100 rounded-full\">\r\n              <DollarSign className=\"w-6 h-6 text-green-600\" />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Total de Votos */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"bg-white rounded-lg shadow-sm p-6\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">\r\n                Total de Votos\r\n              </p>\r\n              <p className=\"text-2xl font-bold text-blue-600\">\r\n                {analytics.voting.totalVotes}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">\r\n                {analytics.voting.totalPerformances} performances\r\n              </p>\r\n            </div>\r\n            <div className=\"p-3 bg-blue-100 rounded-full\">\r\n              <Vote className=\"w-6 h-6 text-blue-600\" />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Avaliação Média */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n          className=\"bg-white rounded-lg shadow-sm p-6\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">\r\n                Avaliação Média\r\n              </p>\r\n              <p className=\"text-2xl font-bold text-yellow-600\">\r\n                {analytics.voting.averageRating.toFixed(1)}⭐\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">de 5.0 estrelas</p>\r\n            </div>\r\n            <div className=\"p-3 bg-yellow-100 rounded-full\">\r\n              <Star className=\"w-6 h-6 text-yellow-600\" />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Taxa de Conversão */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.3 }}\r\n          className=\"bg-white rounded-lg shadow-sm p-6\"\r\n        >\r\n          <div className=\"flex items-center justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-gray-600\">\r\n                Taxa de Conversão\r\n              </p>\r\n              <p className=\"text-2xl font-bold text-purple-600\">\r\n                {formatPercentage(analytics.engagement.paymentConversionRate)}\r\n              </p>\r\n              <p className=\"text-xs text-gray-500 mt-1\">pagamentos aprovados</p>\r\n            </div>\r\n            <div className=\"p-3 bg-purple-100 rounded-full\">\r\n              <Target className=\"w-6 h-6 text-purple-600\" />\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Charts Row */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Fila de Reprodução */}\r\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2\">\r\n            <PieChart className=\"w-5 h-5 text-blue-600\" />\r\n            <span>Fila de Reprodução</span>\r\n          </h3>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">Total de músicas</span>\r\n              <span className=\"font-semibold\">\r\n                {analytics.queue.totalItems}\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600 flex items-center space-x-1\">\r\n                <CreditCard className=\"w-3 h-3 text-yellow-500\" />\r\n                <span>Músicas pagas</span>\r\n              </span>\r\n              <span className=\"font-semibold text-yellow-600\">\r\n                {analytics.queue.paidItems} (\r\n                {formatPercentage(analytics.queue.paidPercentage)})\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">Músicas gratuitas</span>\r\n              <span className=\"font-semibold text-gray-600\">\r\n                {analytics.queue.freeItems}\r\n              </span>\r\n            </div>\r\n\r\n            {/* Progress Bar */}\r\n            <div className=\"mt-4\">\r\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                  className=\"bg-yellow-500 h-2 rounded-full transition-all duration-300\"\r\n                  style={{ width: `${analytics.queue.paidPercentage}%` }}\r\n                />\r\n              </div>\r\n              <p className=\"text-xs text-gray-500 mt-1 text-center\">\r\n                {formatPercentage(analytics.queue.paidPercentage)} músicas pagas\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Engajamento */}\r\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2\">\r\n            <TrendingUp className=\"w-5 h-5 text-green-600\" />\r\n            <span>Métricas de Engajamento</span>\r\n          </h3>\r\n\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">Taxa de Pagamento</span>\r\n              <span className=\"font-semibold text-green-600\">\r\n                {formatPercentage(analytics.engagement.paymentConversionRate)}\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">\r\n                Participação em Votação\r\n              </span>\r\n              <span className=\"font-semibold text-blue-600\">\r\n                {formatPercentage(analytics.engagement.votingParticipationRate)}\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">Votos por Música</span>\r\n              <span className=\"font-semibold text-purple-600\">\r\n                {analytics.engagement.averageVotesPerSong}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Top Performers */}\r\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2\">\r\n          <Trophy className=\"w-5 h-5 text-yellow-500\" />\r\n          <span>Top Performances</span>\r\n        </h3>\r\n\r\n        {analytics.voting.topPerformers.length > 0 ? (\r\n          <div className=\"space-y-3\">\r\n            {analytics.voting.topPerformers.map((performer, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\r\n              >\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <div\r\n                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\r\n                      index === 0\r\n                        ? \"bg-yellow-500 text-white\"\r\n                        : index === 1\r\n                        ? \"bg-gray-400 text-white\"\r\n                        : index === 2\r\n                        ? \"bg-orange-500 text-white\"\r\n                        : \"bg-gray-200 text-gray-600\"\r\n                    }`}\r\n                  >\r\n                    {index + 1}\r\n                  </div>\r\n\r\n                  <div>\r\n                    <p className=\"font-medium text-gray-900\">\r\n                      {performer.songTitle}\r\n                    </p>\r\n                    <p className=\"text-sm text-gray-600\">\r\n                      {performer.clientName} • {performer.tableName}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"text-right\">\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Star className=\"w-4 h-4 text-yellow-500\" />\r\n                    <span className=\"font-semibold\">\r\n                      {performer.averageRating.toFixed(1)}\r\n                    </span>\r\n                  </div>\r\n                  <p className=\"text-xs text-gray-500\">\r\n                    {performer.totalVotes} votos\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <Trophy className=\"w-12 h-12 mx-auto mb-3 opacity-50\" />\r\n            <p>Nenhuma performance avaliada ainda</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Top Tables */}\r\n      {analytics.trends.topTables.length > 0 && (\r\n        <div className=\"bg-white rounded-lg shadow-sm p-6\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2\">\r\n            <Users className=\"w-5 h-5 text-blue-600\" />\r\n            <span>Mesas Mais Ativas</span>\r\n          </h3>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n            {analytics.trends.topTables.map((table, index) => (\r\n              <div key={index} className=\"p-4 bg-gray-50 rounded-lg\">\r\n                <div className=\"flex items-center justify-between mb-2\">\r\n                  <h4 className=\"font-medium text-gray-900\">\r\n                    {table.tableName}\r\n                  </h4>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Star className=\"w-4 h-4 text-yellow-500\" />\r\n                    <span className=\"text-sm font-semibold\">\r\n                      {table.averageRating.toFixed(1)}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-1 text-sm text-gray-600\">\r\n                  <div className=\"flex justify-between\">\r\n                    <span>Performances:</span>\r\n                    <span>{table.performances}</span>\r\n                  </div>\r\n                  <div className=\"flex justify-between\">\r\n                    <span>Total de votos:</span>\r\n                    <span>{table.totalVotes}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CompetitiveAnalytics;\r\n", "import React, { useState, useEffect, useCallback, memo } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport { useRestaurantContext } from \"./RestaurantDashboard\";\r\nimport { buildApiUrl, API_CONFIG } from \"../../config/api\";\r\nimport CompetitiveAnalytics from \"./CompetitiveAnalytics\";\r\nimport {\r\n  BarChart3,\r\n  Heart,\r\n  Music,\r\n  Users,\r\n  RefreshCw,\r\n  Award,\r\n  Activity,\r\n  CheckCircle,\r\n  XCircle,\r\n  Trophy,\r\n} from \"lucide-react\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\n\r\n// Enums for Advanced Analytics\r\nenum Performance {\r\n  Excellent = \"excellent\",\r\n  Good = \"good\",\r\n  Average = \"average\",\r\n  Poor = \"poor\",\r\n  Terrible = \"terrible\",\r\n}\r\n\r\nenum Recommendation {\r\n  Keep = \"keep\",\r\n  Monitor = \"monitor\",\r\n  Remove = \"remove\",\r\n  Blacklist = \"blacklist\",\r\n}\r\n\r\nenum HealthRating {\r\n  Excellent = \"excellent\",\r\n  Good = \"good\",\r\n  NeedsAttention = \"needs_attention\",\r\n  Critical = \"critical\",\r\n}\r\n\r\nenum Period {\r\n  Today = \"1d\",\r\n  Week = \"7d\",\r\n  Month = \"30d\",\r\n  Quarter = \"90d\",\r\n}\r\n\r\ninterface AnalyticsSummary {\r\n  totalPlays: number;\r\n  totalSuggestions: number;\r\n  totalVotes: number;\r\n  activeUsers: number;\r\n  averageRating: number;\r\n  growthRate: number;\r\n  peakHour: string;\r\n  topGenre: string;\r\n}\r\n\r\ninterface AnalyticsMetrics {\r\n  totalSuggestions: number;\r\n  approvedSuggestions: number;\r\n  rejectedSuggestions: number;\r\n  totalVotes: number;\r\n  upvotes: number;\r\n  downvotes: number;\r\n  uniqueSessions: number;\r\n  averageSessionDuration: number;\r\n  engagementRate: number;\r\n  approvalRate: number;\r\n  topGenres: Array<{ genre: string; count: number; percentage: number }>;\r\n  topArtists: Array<{ artist: string; count: number; percentage: number }>;\r\n  hourlyActivity: Array<{\r\n    hour: number;\r\n    suggestions: number;\r\n    votes: number;\r\n    sessions: number;\r\n  }>;\r\n  dailyActivity: Array<{\r\n    date: string;\r\n    suggestions: number;\r\n    votes: number;\r\n    sessions: number;\r\n  }>;\r\n}\r\n\r\ninterface PopularSong {\r\n  id: string;\r\n  title: string;\r\n  artist: string;\r\n  votes: number;\r\n  plays: number;\r\n  score: number;\r\n}\r\n\r\ninterface TrackAnalytics {\r\n  id: string;\r\n  title: string;\r\n  artist: string;\r\n  youtubeVideoId: string;\r\n  totalVotes: number;\r\n  upvotes: number;\r\n  downvotes: number;\r\n  score: number;\r\n  negativeVoteRatio: number;\r\n  positiveVoteRatio: number;\r\n  playCount: number;\r\n  skipCount: number;\r\n  completionRate: number;\r\n  averagePlayDuration: number;\r\n  lastPlayed?: string;\r\n  suggestedCount: number;\r\n  performance: Performance;\r\n  recommendation: Recommendation;\r\n}\r\n\r\ninterface PlaylistHealth {\r\n  totalTracks: number;\r\n  excellentTracks: number;\r\n  goodTracks: number;\r\n  averageTracks: number;\r\n  poorTracks: number;\r\n  terribleTracks: number;\r\n  overallScore: number;\r\n  healthRating: HealthRating;\r\n  recommendations: string[];\r\n}\r\n\r\ninterface UnifiedAnalyticsProps {\r\n  restaurantId?: string;\r\n}\r\n\r\nconst UnifiedAnalytics: React.FC<UnifiedAnalyticsProps> = memo(\r\n  ({ restaurantId: propRestaurantId }) => {\r\n    const { restaurantId: contextRestaurantId } = useRestaurantContext();\r\n    const { restaurantId: urlRestaurantId } = useParams<{\r\n      restaurantId: string;\r\n    }>();\r\n    const finalRestaurantId =\r\n      propRestaurantId || contextRestaurantId || urlRestaurantId;\r\n\r\n    // Validar que temos um restaurantId\r\n    if (!finalRestaurantId) {\r\n      return (\r\n        <div className=\"min-h-screen flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-2xl font-bold text-red-600\">Erro</h1>\r\n            <p className=\"text-gray-600 mt-2\">\r\n              ID do restaurante não fornecido\r\n            </p>\r\n          </div>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    const [activeTab, setActiveTab] = useState<\r\n      \"dashboard\" | \"advanced\" | \"competitive\"\r\n    >(\"dashboard\");\r\n    const [summary, setSummary] = useState<AnalyticsSummary | null>(null);\r\n    const [metrics, setMetrics] = useState<AnalyticsMetrics | null>(null);\r\n    const [popularSongs, setPopularSongs] = useState<PopularSong[]>([]);\r\n    const [selectedPeriod, setSelectedPeriod] = useState<Period>(Period.Week);\r\n    const [dashboardTab, setDashboardTab] = useState<\r\n      \"overview\" | \"engagement\" | \"music\" | \"users\"\r\n    >(\"overview\");\r\n    const [trackAnalytics, setTrackAnalytics] = useState<TrackAnalytics[]>([]);\r\n    const [playlistHealth, setPlaylistHealth] = useState<PlaylistHealth | null>(\r\n      null\r\n    );\r\n    const [filter, setFilter] = useState<Performance | \"all\">(\"all\");\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState<string | null>(null);\r\n\r\n    const periods = [\r\n      { value: Period.Today, label: \"Hoje\" },\r\n      { value: Period.Week, label: \"7 dias\" },\r\n      { value: Period.Month, label: \"30 dias\" },\r\n      { value: Period.Quarter, label: \"90 dias\" },\r\n    ];\r\n\r\n    const mainTabs = [\r\n      { id: \"dashboard\", label: \"Dashboard Geral\", icon: BarChart3 },\r\n      { id: \"advanced\", label: \"Analytics Avançado\", icon: Activity },\r\n      { id: \"competitive\", label: \"Analytics Competitivos\", icon: Trophy },\r\n    ] as const;\r\n\r\n    const dashboardTabs: { id: \"overview\" | \"engagement\" | \"music\" | \"users\"; label: string; icon: any }[] = [\r\n      { id: \"overview\", label: \"Visão Geral\", icon: BarChart3 },\r\n      { id: \"engagement\", label: \"Engajamento\", icon: Heart },\r\n      { id: \"music\", label: \"Músicas\", icon: Music },\r\n      { id: \"users\", label: \"Usuários\", icon: Users },\r\n    ];\r\n\r\n    const loadDashboardAnalytics = useCallback(async () => {\r\n      if (!finalRestaurantId) {\r\n        toast.error(\"Restaurant ID is required\");\r\n        return;\r\n      }\r\n\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const [summaryRes, metricsRes, songsRes] = await Promise.all([\r\n          fetch(\r\n            buildApiUrl(\r\n              `/analytics/dashboard/${finalRestaurantId}?period=${selectedPeriod}`\r\n            )\r\n          ),\r\n          fetch(\r\n            buildApiUrl(\r\n              `/analytics/metrics/${finalRestaurantId}?period=${selectedPeriod}`\r\n            )\r\n          ),\r\n          fetch(buildApiUrl(`/analytics/stats/${finalRestaurantId}`)),\r\n        ]);\r\n\r\n        if (!summaryRes.ok || !metricsRes.ok || !songsRes.ok) {\r\n          throw new Error(\"Erro ao carregar dados do dashboard\");\r\n        }\r\n\r\n        const [summaryData, metricsData, songsData] = await Promise.all([\r\n          summaryRes.json(),\r\n          metricsRes.json(),\r\n          songsRes.json(),\r\n        ]);\r\n\r\n        setSummary(summaryData.success ? summaryData.summary : null);\r\n        setMetrics(metricsData.success ? metricsData.metrics : null);\r\n        setPopularSongs(\r\n          songsData.success ? songsData.stats?.topSongs || [] : []\r\n        );\r\n      } catch (error) {\r\n        console.error(\"Erro ao carregar analytics dashboard:\", error);\r\n        setError(\"Erro ao carregar dados do dashboard\");\r\n        toast.error(\"Erro ao carregar dados do dashboard\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }, [finalRestaurantId, selectedPeriod]);\r\n\r\n    const loadAdvancedAnalytics = useCallback(async () => {\r\n      if (!finalRestaurantId) {\r\n        toast.error(\"Restaurant ID is required\");\r\n        return;\r\n      }\r\n\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const [tracksRes, healthRes] = await Promise.all([\r\n          fetch(buildApiUrl(`/analytics/tracks/${finalRestaurantId}`)),\r\n          fetch(buildApiUrl(`/analytics/playlist-health/${finalRestaurantId}`)),\r\n        ]);\r\n\r\n        if (!tracksRes.ok || !healthRes.ok) {\r\n          throw new Error(\"Erro ao carregar dados do servidor\");\r\n        }\r\n\r\n        const [tracksData, healthData] = await Promise.all([\r\n          tracksRes.json(),\r\n          healthRes.json(),\r\n        ]);\r\n\r\n        setTrackAnalytics(\r\n          tracksData.success && tracksData.trackAnalytics\r\n            ? Object.values(tracksData.trackAnalytics)\r\n            : []\r\n        );\r\n        setPlaylistHealth(\r\n          healthData.success && healthData.playlistHealth\r\n            ? healthData.playlistHealth\r\n            : null\r\n        );\r\n      } catch (error) {\r\n        console.error(\"Erro ao carregar advanced analytics:\", error);\r\n        setError(\"Erro ao carregar dados de analytics avançado\");\r\n        toast.error(\"Erro ao carregar analytics avançado\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    }, [finalRestaurantId]);\r\n\r\n    useEffect(() => {\r\n      if (activeTab === \"dashboard\") {\r\n        loadDashboardAnalytics();\r\n      } else {\r\n        loadAdvancedAnalytics();\r\n      }\r\n    }, [activeTab, loadDashboardAnalytics, loadAdvancedAnalytics]);\r\n\r\n    const formatDuration = (seconds: number): string => {\r\n      const mins = Math.floor(seconds / 60);\r\n      const secs = seconds % 60;\r\n      return `${mins}:${secs.toString().padStart(2, \"0\")}`;\r\n    };\r\n\r\n    const getPerformanceColor = (performance: Performance): string => {\r\n      switch (performance) {\r\n        case Performance.Excellent:\r\n          return \"text-green-600 bg-green-100 dark:bg-green-900/30\";\r\n        case Performance.Good:\r\n          return \"text-blue-600 bg-blue-100 dark:bg-blue-900/30\";\r\n        case Performance.Average:\r\n          return \"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30\";\r\n        case Performance.Poor:\r\n          return \"text-orange-600 bg-orange-100 dark:bg-orange-900/30\";\r\n        case Performance.Terrible:\r\n          return \"text-red-600 bg-red-100 dark:bg-red-900/30\";\r\n        default:\r\n          return \"text-gray-600 bg-gray-100 dark:bg-gray-900/30\";\r\n      }\r\n    };\r\n\r\n    const getRecommendationColor = (recommendation: Recommendation): string => {\r\n      switch (recommendation) {\r\n        case Recommendation.Keep:\r\n          return \"text-green-600 bg-green-100 dark:bg-green-900/30\";\r\n        case Recommendation.Monitor:\r\n          return \"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30\";\r\n        case Recommendation.Remove:\r\n          return \"text-orange-600 bg-orange-100 dark:bg-orange-900/30\";\r\n        case Recommendation.Blacklist:\r\n          return \"text-red-600 bg-red-100 dark:bg-red-900/30\";\r\n        default:\r\n          return \"text-gray-600 bg-gray-100 dark:bg-gray-900/30\";\r\n      }\r\n    };\r\n\r\n    const filteredTracks = trackAnalytics.filter(\r\n      (track) => filter === \"all\" || track.performance === filter\r\n    );\r\n\r\n    if (loading) {\r\n      return (\r\n        <div className=\"flex items-center justify-center min-h-screen\">\r\n          <RefreshCw className=\"w-6 h-6 animate-spin text-blue-600\" />\r\n          <span className=\"ml-2 text-gray-600 dark:text-gray-300\">\r\n            Carregando analytics...\r\n          </span>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className=\"max-w-7xl mx-auto p-4 sm:p-6 space-y-6\">\r\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n          <div>\r\n            <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white\">\r\n              Analytics Completo\r\n            </h1>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-300 mt-1\">\r\n              Análise detalhada do desempenho do restaurante\r\n            </p>\r\n          </div>\r\n          <button\r\n            onClick={() =>\r\n              activeTab === \"dashboard\"\r\n                ? loadDashboardAnalytics()\r\n                : loadAdvancedAnalytics()\r\n            }\r\n            className=\"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n            aria-label=\"Atualizar dados\"\r\n          >\r\n            <RefreshCw className=\"w-4 h-4\" />\r\n            <span>Atualizar</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n          <nav className=\"flex space-x-4 sm:space-x-8\">\r\n            {mainTabs.map((tab) => (\r\n              <button\r\n                key={tab.id}\r\n                onClick={() => setActiveTab(tab.id)}\r\n                className={`py-3 px-2 border-b-2 font-medium text-sm flex items-center space-x-2 ${\r\n                  activeTab === tab.id\r\n                    ? \"border-blue-500 text-blue-600 dark:text-blue-400\"\r\n                    : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300\"\r\n                }`}\r\n                aria-current={activeTab === tab.id ? \"page\" : undefined}\r\n              >\r\n                <tab.icon className=\"w-4 h-4\" />\r\n                <span>{tab.label}</span>\r\n              </button>\r\n            ))}\r\n          </nav>\r\n        </div>\r\n\r\n        <AnimatePresence mode=\"wait\">\r\n          {activeTab === \"dashboard\" && (\r\n            <motion.div\r\n              key=\"dashboard\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              transition={{ duration: 0.3 }}\r\n              className=\"space-y-6\"\r\n            >\r\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n                <div className=\"flex flex-wrap gap-2 sm:gap-4\">\r\n                  {dashboardTabs.map((tab) => (\r\n                    <button\r\n                      key={tab.id}\r\n                      onClick={() => setDashboardTab(tab.id)}\r\n                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${\r\n                        dashboardTab === tab.id\r\n                          ? \"bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400\"\r\n                          : \"text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800\"\r\n                      }`}\r\n                    >\r\n                      <tab.icon className=\"w-4 h-4\" />\r\n                      <span>{tab.label}</span>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                <select\r\n                  value={selectedPeriod}\r\n                  onChange={(e) => setSelectedPeriod(e.target.value as Period)}\r\n                  className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white\"\r\n                >\r\n                  {periods.map((period) => (\r\n                    <option key={period.value} value={period.value}>\r\n                      {period.label}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n              </div>\r\n\r\n              {summary && (\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6\">\r\n                  {[\r\n                    {\r\n                      title: \"Total de Plays\",\r\n                      value: summary.totalPlays,\r\n                      icon: Music,\r\n                      color: \"blue\",\r\n                    },\r\n                    {\r\n                      title: \"Sugestões\",\r\n                      value: summary.totalSuggestions,\r\n                      icon: Heart,\r\n                      color: \"green\",\r\n                    },\r\n                    {\r\n                      title: \"Usuários Ativos\",\r\n                      value: summary.activeUsers,\r\n                      icon: Users,\r\n                      color: \"purple\",\r\n                    },\r\n                    {\r\n                      title: \"Nota Média\",\r\n                      value: summary.averageRating.toFixed(1),\r\n                      icon: Award,\r\n                      color: \"yellow\",\r\n                    },\r\n                  ].map((stat, index) => (\r\n                    <motion.div\r\n                      key={stat.title}\r\n                      initial={{ opacity: 0, y: 20 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ delay: index * 0.1 }}\r\n                      className=\"bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\"\r\n                    >\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <div>\r\n                          <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\r\n                            {stat.title}\r\n                          </p>\r\n                          <p className=\"text-xl sm:text-2xl font-bold text-gray-900 dark:text-white\">\r\n                            {stat.value}\r\n                          </p>\r\n                        </div>\r\n                        <div\r\n                          className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/30`}\r\n                        >\r\n                          <stat.icon\r\n                            className={`w-5 h-5 sm:w-6 sm:h-6 text-${stat.color}-600 dark:text-${stat.color}-400`}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              {dashboardTab === \"overview\" && metrics && (\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\r\n                  <div className=\"bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n                      Top Gêneros\r\n                    </h3>\r\n                    <div className=\"space-y-3\">\r\n                      {metrics.topGenres.slice(0, 5).map((genre) => (\r\n                        <div\r\n                          key={genre.genre}\r\n                          className=\"flex items-center justify-between\"\r\n                        >\r\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            {genre.genre}\r\n                          </span>\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <div className=\"w-24 sm:w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n                              <div\r\n                                className=\"bg-blue-600 h-2 rounded-full\"\r\n                                style={{ width: `${genre.percentage}%` }}\r\n                              />\r\n                            </div>\r\n                            <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n                              {genre.percentage}%\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n                      Top Artistas\r\n                    </h3>\r\n                    <div className=\"space-y-3\">\r\n                      {metrics.topArtists.slice(0, 5).map((artist) => (\r\n                        <div\r\n                          key={artist.artist}\r\n                          className=\"flex items-center justify-between\"\r\n                        >\r\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                            {artist.artist}\r\n                          </span>\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <div className=\"w-24 sm:w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\r\n                              <div\r\n                                className=\"bg-green-600 h-2 rounded-full\"\r\n                                style={{ width: `${artist.percentage}%` }}\r\n                              />\r\n                            </div>\r\n                            <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n                              {artist.percentage}%\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {dashboardTab === \"music\" && popularSongs.length > 0 && (\r\n                <div className=\"bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n                    Músicas Populares\r\n                  </h3>\r\n                  <div className=\"space-y-3\">\r\n                    {popularSongs.slice(0, 10).map((song, index) => (\r\n                      <div\r\n                        key={song.id}\r\n                        className=\"flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\"\r\n                      >\r\n                        <div className=\"flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center\">\r\n                          <span className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">\r\n                            {index + 1}\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\r\n                            {song.title}\r\n                          </p>\r\n                          <p className=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\r\n                            {song.artist}\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\r\n                          <span>{song.plays} plays</span>\r\n                          <span>{song.votes} votos</span>\r\n                          <span className=\"font-medium\">{song.score}</span>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </motion.div>\r\n          )}\r\n\r\n          {activeTab === \"advanced\" && (\r\n            <motion.div\r\n              key=\"advanced\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              transition={{ duration: 0.3 }}\r\n              className=\"space-y-6\"\r\n            >\r\n              {playlistHealth && (\r\n                <div className=\"bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\r\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\r\n                    Saúde da Playlist\r\n                  </h3>\r\n                  <div className=\"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4 mb-4\">\r\n                    {[\r\n                      {\r\n                        label: \"Excelentes\",\r\n                        value: playlistHealth.excellentTracks,\r\n                        color: \"green\",\r\n                      },\r\n                      {\r\n                        label: \"Boas\",\r\n                        value: playlistHealth.goodTracks,\r\n                        color: \"blue\",\r\n                      },\r\n                      {\r\n                        label: \"Médias\",\r\n                        value: playlistHealth.averageTracks,\r\n                        color: \"yellow\",\r\n                      },\r\n                      {\r\n                        label: \"Ruins\",\r\n                        value: playlistHealth.poorTracks,\r\n                        color: \"orange\",\r\n                      },\r\n                      {\r\n                        label: \"Péssimas\",\r\n                        value: playlistHealth.terribleTracks,\r\n                        color: \"red\",\r\n                      },\r\n                      {\r\n                        label: \"Total\",\r\n                        value: playlistHealth.totalTracks,\r\n                        color: \"gray\",\r\n                      },\r\n                    ].map((item) => (\r\n                      <div key={item.label} className=\"text-center\">\r\n                        <div\r\n                          className={`text-xl sm:text-2xl font-bold text-${item.color}-600 dark:text-${item.color}-400`}\r\n                        >\r\n                          {item.value}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          {item.label}\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <span className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\r\n                      Score Geral:\r\n                    </span>\r\n                    <span className=\"text-lg font-bold text-gray-900 dark:text-white\">\r\n                      {playlistHealth.overallScore}/100\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n                <select\r\n                  value={filter}\r\n                  onChange={(e) =>\r\n                    setFilter(e.target.value as Performance | \"all\")\r\n                  }\r\n                  className=\"px-3 py-2 border border-gray-300 rounded-lg text-sm bg-white dark:bg-gray-800 dark:border-gray-600 dark:text-white\"\r\n                >\r\n                  <option value=\"all\">Todas as performances</option>\r\n                  {Object.values(Performance).map((perf) => (\r\n                    <option key={perf} value={perf}>\r\n                      {perf.charAt(0).toUpperCase() + perf.slice(1)}\r\n                    </option>\r\n                  ))}\r\n                </select>\r\n                <span className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                  {filteredTracks.length} músicas encontradas\r\n                </span>\r\n              </div>\r\n\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden\">\r\n                <div className=\"overflow-x-auto\">\r\n                  <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                    <thead className=\"bg-gray-50 dark:bg-gray-900\">\r\n                      <tr>\r\n                        <th className=\"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Música\r\n                        </th>\r\n                        <th className=\"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Performance\r\n                        </th>\r\n                        <th className=\"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Votos\r\n                        </th>\r\n                        <th className=\"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Plays\r\n                        </th>\r\n                        <th className=\"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Taxa Conclusão\r\n                        </th>\r\n                        <th className=\"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                          Recomendação\r\n                        </th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n                      {filteredTracks.map((track) => (\r\n                        <tr\r\n                          key={track.id}\r\n                          className=\"hover:bg-gray-50 dark:hover:bg-gray-700\"\r\n                        >\r\n                          <td className=\"px-4 sm:px-6 py-4\">\r\n                            <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\r\n                              {track.title}\r\n                            </div>\r\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                              {track.artist}\r\n                            </div>\r\n                          </td>\r\n                          <td className=\"px-4 sm:px-6 py-4\">\r\n                            <span\r\n                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPerformanceColor(\r\n                                track.performance\r\n                              )}`}\r\n                            >\r\n                              {track.performance.charAt(0).toUpperCase() +\r\n                                track.performance.slice(1)}\r\n                            </span>\r\n                          </td>\r\n                          <td className=\"px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-white\">\r\n                            <div className=\"flex items-center space-x-2\">\r\n                              <span className=\"text-green-600\">\r\n                                ↑{track.upvotes}\r\n                              </span>\r\n                              <span className=\"text-red-600\">\r\n                                ↓{track.downvotes}\r\n                              </span>\r\n                            </div>\r\n                          </td>\r\n                          <td className=\"px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-white\">\r\n                            {track.playCount}\r\n                          </td>\r\n                          <td className=\"px-4 sm:px-6 py-4 text-sm text-gray-900 dark:text-white\">\r\n                            {(track.completionRate * 100).toFixed(1)}%\r\n                          </td>\r\n                          <td className=\"px-4 sm:px-6 py-4\">\r\n                            <span\r\n                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRecommendationColor(\r\n                                track.recommendation\r\n                              )}`}\r\n                            >\r\n                              {track.recommendation.charAt(0).toUpperCase() +\r\n                                track.recommendation.slice(1)}\r\n                            </span>\r\n                          </td>\r\n                        </tr>\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n                </div>\r\n                {filteredTracks.length === 0 && (\r\n                  <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\r\n                    Nenhuma música encontrada para este filtro\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n\r\n          {activeTab === \"competitive\" && (\r\n            <motion.div\r\n              key=\"competitive\"\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <CompetitiveAnalytics />\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n\r\n        {error && (\r\n          <div className=\"bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-4\">\r\n            <div className=\"flex items-center\">\r\n              <XCircle className=\"w-5 h-5 text-red-600 dark:text-red-400 mr-2\" />\r\n              <span className=\"text-red-700 dark:text-red-300\">{error}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nUnifiedAnalytics.displayName = \"UnifiedAnalytics\";\r\n\r\nexport default UnifiedAnalytics;\r\n"], "names": ["CompetitiveAnalytics", "propRestaurantId", "contextRestaurantId", "useRestaurantContext", "restaurantId", "analytics", "setAnalytics", "useState", "loading", "setLoading", "period", "<PERSON><PERSON><PERSON><PERSON>", "useEffect", "loadAnalytics", "response", "buildApiUrl", "data", "error", "formatCurrency", "value", "formatPercentage", "getPeriodLabel", "jsxs", "jsx", "Trophy", "p", "motion", "DollarSign", "Vote", "Star", "Target", "<PERSON><PERSON><PERSON>", "CreditCard", "TrendingUp", "performer", "index", "Users", "table", "BarChart3", "Performance", "UnifiedAnalytics", "memo", "urlRestaurantId", "useParams", "finalRestaurantId", "activeTab", "setActiveTab", "summary", "set<PERSON>ummary", "metrics", "setMetrics", "popularSongs", "setPopularSongs", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "dashboardTab", "setDashboardTab", "trackAnalytics", "setTrackAnalytics", "playlistHealth", "setPlaylistHealth", "filter", "setFilter", "setError", "periods", "mainTabs", "Activity", "dashboardTabs", "Heart", "Music", "loadDashboardAnalytics", "useCallback", "toast", "summaryRes", "metricsRes", "songsRes", "summaryData", "metricsData", "songsData", "_a", "loadAdvancedAnalytics", "tracksRes", "healthRes", "tracksData", "healthData", "getPerformanceColor", "performance", "getRecommendationColor", "recommendation", "filteredTracks", "track", "RefreshCw", "tab", "AnimatePresence", "e", "Award", "stat", "genre", "artist", "song", "item", "perf", "XCircle"], "mappings": "gVAkEA,MAAMA,GAA4D,CAAC,CACjE,aAAcC,CAChB,IAAM,CACJ,KAAM,CAAE,aAAcC,CAAoB,EAAIC,EAAqB,EAC7DC,EAAeH,GAAoBC,EAEnC,CAACG,EAAWC,CAAY,EAAIC,WAAsC,IAAI,EACtE,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAK,EACtC,CAACG,EAAQC,CAAS,EAAIJ,WAA8B,IAAI,EAE9DK,EAAAA,UAAU,IAAM,CACAC,GAAA,EACb,CAACT,EAAcM,CAAM,CAAC,EAEzB,MAAMG,EAAgB,SAAY,CAChC,GAAI,CAACT,EAAc,CACjB,QAAQ,MAAM,8BAA8B,EAC5C,MACF,CAEI,GAAA,CACFK,EAAW,EAAI,EAEf,MAAMK,EAAW,MAAM,MACrBC,EAAY,0BAA0BX,CAAY,WAAWM,CAAM,EAAE,CAAA,EAGvE,GAAII,EAAS,GAAI,CACT,MAAAE,EAAO,MAAMF,EAAS,OAC5BR,EAAaU,EAAK,SAAS,CAC7B,QACOC,EAAO,CACN,QAAA,MAAM,2CAA4CA,CAAK,CAAA,QAC/D,CACAR,EAAW,EAAK,CAClB,CAAA,EAGIS,EAAkBC,GACf,IAAI,KAAK,aAAa,QAAS,CACpC,MAAO,WACP,SAAU,KAAA,CACX,EAAE,OAAOA,CAAK,EAGXC,EAAoBD,GACjB,GAAGA,EAAM,QAAQ,CAAC,CAAC,IAGtBE,EAAkBX,IACP,CACb,KAAM,cACN,KAAM,iBACN,MAAO,iBAAA,GAEKA,CAA6B,GAAKA,EAGlD,OAAIF,QAEC,MAAI,CAAA,UAAU,oCACb,SAACc,EAAA,KAAA,MAAA,CAAI,UAAU,6CACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,iFAAkF,CAAA,EAChGA,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAEhC,uCAAA,CAAA,CACF,CAAA,CACF,CAAA,EAIClB,EAYHiB,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,oCACb,SAACD,EAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,+DACZ,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAO,UAAU,yBAA0B,CAAA,EAC5CD,EAAAA,IAAC,QAAK,SAAsB,wBAAA,CAAA,CAAA,EAC9B,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,qBAAqB,SAAA,CAAA,+CACa,IAC5CD,EAAeX,CAAM,CAAA,EACxB,CAAA,EACF,EAEAa,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACX,SAAA,CAAC,KAAM,KAAM,KAAK,EAAY,IAAKE,GACnCF,EAAA,IAAC,SAAA,CAEC,QAAS,IAAMZ,EAAUc,CAAC,EAC1B,UAAW,8DACTf,IAAWe,EACP,yBACA,6CACN,GAEC,WAAeA,CAAC,CAAA,EARZA,CAUR,CAAA,EACH,CAAA,CAAA,CACF,CACF,CAAA,EAGAH,EAAAA,KAAC,MAAI,CAAA,UAAU,uDAEb,SAAA,CAAAC,EAAA,IAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,UAAU,oCAEV,SAAAJ,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,oCAAoC,SAAa,gBAAA,EAC9DA,EAAAA,IAAC,KAAE,UAAU,oCACV,WAAelB,EAAU,QAAQ,KAAK,EACzC,EACAiB,EAAAA,KAAC,IAAE,CAAA,UAAU,6BACV,SAAA,CAAAjB,EAAU,QAAQ,cAAc,aAAA,EACnC,CAAA,EACF,EACAkB,EAAAA,IAAC,OAAI,UAAU,gCACb,eAACI,GAAW,CAAA,UAAU,yBAAyB,CACjD,CAAA,CAAA,EACF,CAAA,CACF,EAGAJ,EAAA,IAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAO,EAAI,EACzB,UAAU,oCAEV,SAAAJ,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,oCAAoC,SAEjD,iBAAA,QACC,IAAE,CAAA,UAAU,mCACV,SAAAlB,EAAU,OAAO,WACpB,EACAiB,EAAAA,KAAC,IAAE,CAAA,UAAU,6BACV,SAAA,CAAAjB,EAAU,OAAO,kBAAkB,eAAA,EACtC,CAAA,EACF,EACAkB,EAAAA,IAAC,OAAI,UAAU,+BACb,eAACK,GAAK,CAAA,UAAU,wBAAwB,CAC1C,CAAA,CAAA,EACF,CAAA,CACF,EAGAL,EAAA,IAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAO,EAAI,EACzB,UAAU,oCAEV,SAAAJ,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,oCAAoC,SAEjD,kBAAA,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,qCACV,SAAA,CAAUjB,EAAA,OAAO,cAAc,QAAQ,CAAC,EAAE,GAAA,EAC7C,EACCkB,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAAe,kBAAA,CAAA,EAC3D,EACAA,EAAAA,IAAC,OAAI,UAAU,iCACb,eAACM,EAAK,CAAA,UAAU,0BAA0B,CAC5C,CAAA,CAAA,EACF,CAAA,CACF,EAGAN,EAAA,IAACG,EAAO,IAAP,CACC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAO,EAAI,EACzB,UAAU,oCAEV,SAAAJ,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,IAAA,CAAE,UAAU,oCAAoC,SAEjD,oBAAA,EACAA,EAAAA,IAAC,KAAE,UAAU,qCACV,WAAiBlB,EAAU,WAAW,qBAAqB,EAC9D,EACCkB,EAAA,IAAA,IAAA,CAAE,UAAU,6BAA6B,SAAoB,uBAAA,CAAA,EAChE,EACAA,EAAAA,IAAC,OAAI,UAAU,iCACb,eAACO,GAAO,CAAA,UAAU,0BAA0B,CAC9C,CAAA,CAAA,EACF,CAAA,CACF,CAAA,EACF,EAGAR,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uEACZ,SAAA,CAACC,EAAAA,IAAAQ,GAAA,CAAS,UAAU,uBAAwB,CAAA,EAC5CR,EAAAA,IAAC,QAAK,SAAkB,oBAAA,CAAA,CAAA,EAC1B,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAgB,mBAAA,QACvD,OAAK,CAAA,UAAU,gBACb,SAAAlB,EAAU,MAAM,WACnB,CAAA,EACF,EAEAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,oDACd,SAAA,CAACC,EAAAA,IAAAS,GAAA,CAAW,UAAU,yBAA0B,CAAA,EAChDT,EAAAA,IAAC,QAAK,SAAa,eAAA,CAAA,CAAA,EACrB,EACAD,EAAAA,KAAC,OAAK,CAAA,UAAU,gCACb,SAAA,CAAAjB,EAAU,MAAM,UAAU,KAC1Be,EAAiBf,EAAU,MAAM,cAAc,EAAE,GAAA,EACpD,CAAA,EACF,EAEAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAiB,oBAAA,QACxD,OAAK,CAAA,UAAU,8BACb,SAAAlB,EAAU,MAAM,UACnB,CAAA,EACF,EAGAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,OACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,sCACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,6DACV,MAAO,CAAE,MAAO,GAAGlB,EAAU,MAAM,cAAc,GAAI,CAAA,CAAA,EAEzD,EACAiB,EAAAA,KAAC,IAAE,CAAA,UAAU,yCACV,SAAA,CAAiBF,EAAAf,EAAU,MAAM,cAAc,EAAE,gBAAA,EACpD,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uEACZ,SAAA,CAACC,EAAAA,IAAAU,GAAA,CAAW,UAAU,wBAAyB,CAAA,EAC/CV,EAAAA,IAAC,QAAK,SAAuB,yBAAA,CAAA,CAAA,EAC/B,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAiB,oBAAA,EACzDA,EAAAA,IAAC,QAAK,UAAU,+BACb,WAAiBlB,EAAU,WAAW,qBAAqB,EAC9D,CAAA,EACF,EAEAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAExC,0BAAA,EACAA,EAAAA,IAAC,QAAK,UAAU,8BACb,WAAiBlB,EAAU,WAAW,uBAAuB,EAChE,CAAA,EACF,EAEAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAgB,mBAAA,QACvD,OAAK,CAAA,UAAU,gCACb,SAAAlB,EAAU,WAAW,oBACxB,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGAiB,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uEACZ,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAO,UAAU,yBAA0B,CAAA,EAC5CD,EAAAA,IAAC,QAAK,SAAgB,kBAAA,CAAA,CAAA,EACxB,EAEClB,EAAU,OAAO,cAAc,OAAS,EACtCkB,EAAA,IAAA,MAAA,CAAI,UAAU,YACZ,WAAU,OAAO,cAAc,IAAI,CAACW,EAAWC,IAC9Cb,EAAA,KAAC,MAAA,CAEC,UAAU,8DAEV,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,UAAW,2EACTY,IAAU,EACN,2BACAA,IAAU,EACV,yBACAA,IAAU,EACV,2BACA,2BACN,GAEC,SAAQA,EAAA,CAAA,CACX,SAEC,MACC,CAAA,SAAA,CAAAZ,EAAA,IAAC,IAAE,CAAA,UAAU,4BACV,SAAAW,EAAU,UACb,EACAZ,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAUY,EAAA,WAAW,MAAIA,EAAU,SAAA,EACtC,CAAA,EACF,CAAA,EACF,EAEAZ,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAAM,EAAA,CAAK,UAAU,yBAA0B,CAAA,EAC1CN,EAAAA,IAAC,QAAK,UAAU,gBACb,WAAU,cAAc,QAAQ,CAAC,EACpC,CAAA,EACF,EACAD,EAAAA,KAAC,IAAE,CAAA,UAAU,wBACV,SAAA,CAAUY,EAAA,WAAW,QAAA,EACxB,CAAA,EACF,CAAA,CAAA,EAtCKC,CAAA,CAwCR,CACH,CAAA,EAECb,OAAA,MAAA,CAAI,UAAU,iCACb,SAAA,CAACC,EAAAA,IAAAC,EAAA,CAAO,UAAU,mCAAoC,CAAA,EACtDD,EAAAA,IAAC,KAAE,SAAkC,oCAAA,CAAA,CAAA,EACvC,CAAA,EAEJ,EAGClB,EAAU,OAAO,UAAU,OAAS,GAClCiB,OAAA,MAAA,CAAI,UAAU,oCACb,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,uEACZ,SAAA,CAACC,EAAAA,IAAAa,EAAA,CAAM,UAAU,uBAAwB,CAAA,EACzCb,EAAAA,IAAC,QAAK,SAAiB,mBAAA,CAAA,CAAA,EACzB,EAECA,EAAA,IAAA,MAAA,CAAI,UAAU,uDACZ,WAAU,OAAO,UAAU,IAAI,CAACc,EAAOF,IACrCb,EAAA,KAAA,MAAA,CAAgB,UAAU,4BACzB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAC,EAAA,IAAC,KAAG,CAAA,UAAU,4BACX,SAAAc,EAAM,UACT,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAAM,EAAA,CAAK,UAAU,yBAA0B,CAAA,EAC1CN,EAAAA,IAAC,QAAK,UAAU,wBACb,WAAM,cAAc,QAAQ,CAAC,EAChC,CAAA,EACF,CAAA,EACF,EAEAD,EAAAA,KAAC,MAAI,CAAA,UAAU,kCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,uBACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAa,eAAA,CAAA,EACnBA,EAAAA,IAAC,OAAM,CAAA,SAAAc,EAAM,YAAa,CAAA,CAAA,EAC5B,EACAf,EAAAA,KAAC,MAAI,CAAA,UAAU,uBACb,SAAA,CAAAC,EAAAA,IAAC,QAAK,SAAe,iBAAA,CAAA,EACrBA,EAAAA,IAAC,OAAM,CAAA,SAAAc,EAAM,UAAW,CAAA,CAAA,EAC1B,CAAA,EACF,CAAA,GAtBQF,CAuBV,CACD,EACH,CAAA,EACF,CAEJ,CAAA,CAAA,QAlUG,MAAI,CAAA,UAAU,oCACb,SAACb,EAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAACC,EAAAA,IAAAe,EAAA,CAAU,UAAU,mCAAoC,CAAA,EACzDf,EAAAA,IAAC,KAAE,SAAsB,wBAAA,CAAA,CAAA,CAC3B,CAAA,CACF,CAAA,CA+TN,ECxbA,IAAKgB,GAAAA,IACHA,EAAA,UAAY,YACZA,EAAA,KAAO,OACPA,EAAA,QAAU,UACVA,EAAA,KAAO,OACPA,EAAA,SAAW,WALRA,IAAAA,GAAA,CAAA,CAAA,EAiHL,MAAMC,GAAoDC,EAAA,KACxD,CAAC,CAAE,aAAcxC,KAAuB,CACtC,KAAM,CAAE,aAAcC,CAAoB,EAAIC,EAAqB,EAC7D,CAAE,aAAcuC,CAAgB,EAAIC,GAEvC,EACGC,EACJ3C,GAAoBC,GAAuBwC,EAG7C,GAAI,CAACE,EACH,aACG,MAAI,CAAA,UAAU,gDACb,SAACtB,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,kCAAkC,SAAI,OAAA,EACnDA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,kCAAA,CAAA,CACF,CAAA,CACF,CAAA,EAIJ,KAAM,CAACsB,EAAWC,CAAY,EAAIvC,WAEhC,WAAW,EACP,CAACwC,EAASC,CAAU,EAAIzC,WAAkC,IAAI,EAC9D,CAAC0C,EAASC,CAAU,EAAI3C,WAAkC,IAAI,EAC9D,CAAC4C,EAAcC,CAAe,EAAI7C,EAAA,SAAwB,CAAE,CAAA,EAC5D,CAAC8C,EAAgBC,CAAiB,EAAI/C,WAAiB,IAAW,EAClE,CAACgD,EAAcC,CAAe,EAAIjD,WAEtC,UAAU,EACN,CAACkD,EAAgBC,CAAiB,EAAInD,EAAA,SAA2B,CAAE,CAAA,EACnE,CAACoD,EAAgBC,CAAiB,EAAIrD,EAAA,SAC1C,IAAA,EAEI,CAACsD,EAAQC,CAAS,EAAIvD,WAA8B,KAAK,EACzD,CAACC,EAASC,CAAU,EAAIF,WAAS,EAAI,EACrC,CAACU,EAAO8C,CAAQ,EAAIxD,WAAwB,IAAI,EAEhDyD,EAAU,CACd,CAAE,MAAO,KAAc,MAAO,MAAO,EACrC,CAAE,MAAO,KAAa,MAAO,QAAS,EACtC,CAAE,MAAO,MAAc,MAAO,SAAU,EACxC,CAAE,MAAO,MAAgB,MAAO,SAAU,CAAA,EAGtCC,EAAW,CACf,CAAE,GAAI,YAAa,MAAO,kBAAmB,KAAM3B,CAAU,EAC7D,CAAE,GAAI,WAAY,MAAO,qBAAsB,KAAM4B,EAAS,EAC9D,CAAE,GAAI,cAAe,MAAO,yBAA0B,KAAM1C,CAAO,CAAA,EAG/D2C,EAAmG,CACvG,CAAE,GAAI,WAAY,MAAO,cAAe,KAAM7B,CAAU,EACxD,CAAE,GAAI,aAAc,MAAO,cAAe,KAAM8B,CAAM,EACtD,CAAE,GAAI,QAAS,MAAO,UAAW,KAAMC,CAAM,EAC7C,CAAE,GAAI,QAAS,MAAO,WAAY,KAAMjC,CAAM,CAAA,EAG1CkC,EAAyBC,EAAAA,YAAY,SAAY,OACrD,GAAI,CAAC3B,EAAmB,CACtB4B,EAAM,MAAM,2BAA2B,EACvC,MACF,CAEA/D,EAAW,EAAI,EACfsD,EAAS,IAAI,EACT,GAAA,CACF,KAAM,CAACU,EAAYC,EAAYC,CAAQ,EAAI,MAAM,QAAQ,IAAI,CAC3D,MACE5D,EACE,wBAAwB6B,CAAiB,WAAWS,CAAc,EACpE,CACF,EACA,MACEtC,EACE,sBAAsB6B,CAAiB,WAAWS,CAAc,EAClE,CACF,EACA,MAAMtC,EAAY,oBAAoB6B,CAAiB,EAAE,CAAC,CAAA,CAC3D,EAEG,GAAA,CAAC6B,EAAW,IAAM,CAACC,EAAW,IAAM,CAACC,EAAS,GAC1C,MAAA,IAAI,MAAM,qCAAqC,EAGvD,KAAM,CAACC,EAAaC,EAAaC,CAAS,EAAI,MAAM,QAAQ,IAAI,CAC9DL,EAAW,KAAK,EAChBC,EAAW,KAAK,EAChBC,EAAS,KAAK,CAAA,CACf,EAED3B,EAAW4B,EAAY,QAAUA,EAAY,QAAU,IAAI,EAC3D1B,EAAW2B,EAAY,QAAUA,EAAY,QAAU,IAAI,EAC3DzB,EACE0B,EAAU,UAAUC,EAAAD,EAAU,QAAV,YAAAC,EAAiB,WAAY,GAAK,CAAC,CAAA,QAElD9D,EAAO,CACN,QAAA,MAAM,wCAAyCA,CAAK,EAC5D8C,EAAS,qCAAqC,EAC9CS,EAAM,MAAM,qCAAqC,CAAA,QACjD,CACA/D,EAAW,EAAK,CAClB,CAAA,EACC,CAACmC,EAAmBS,CAAc,CAAC,EAEhC2B,EAAwBT,EAAAA,YAAY,SAAY,CACpD,GAAI,CAAC3B,EAAmB,CACtB4B,EAAM,MAAM,2BAA2B,EACvC,MACF,CAEA/D,EAAW,EAAI,EACfsD,EAAS,IAAI,EACT,GAAA,CACF,KAAM,CAACkB,EAAWC,CAAS,EAAI,MAAM,QAAQ,IAAI,CAC/C,MAAMnE,EAAY,qBAAqB6B,CAAiB,EAAE,CAAC,EAC3D,MAAM7B,EAAY,8BAA8B6B,CAAiB,EAAE,CAAC,CAAA,CACrE,EAED,GAAI,CAACqC,EAAU,IAAM,CAACC,EAAU,GACxB,MAAA,IAAI,MAAM,oCAAoC,EAGtD,KAAM,CAACC,EAAYC,CAAU,EAAI,MAAM,QAAQ,IAAI,CACjDH,EAAU,KAAK,EACfC,EAAU,KAAK,CAAA,CAChB,EAEDxB,EACEyB,EAAW,SAAWA,EAAW,eAC7B,OAAO,OAAOA,EAAW,cAAc,EACvC,CAAC,CAAA,EAEPvB,EACEwB,EAAW,SAAWA,EAAW,eAC7BA,EAAW,eACX,IAAA,QAECnE,EAAO,CACN,QAAA,MAAM,uCAAwCA,CAAK,EAC3D8C,EAAS,8CAA8C,EACvDS,EAAM,MAAM,qCAAqC,CAAA,QACjD,CACA/D,EAAW,EAAK,CAClB,CAAA,EACC,CAACmC,CAAiB,CAAC,EAEtBhC,EAAAA,UAAU,IAAM,CACViC,IAAc,YACOyB,IAEDU,GAEvB,EAAA,CAACnC,EAAWyB,EAAwBU,CAAqB,CAAC,EAQvD,MAAAK,EAAuBC,GAAqC,CAChE,OAAQA,EAAa,CACnB,IAAK,YACI,MAAA,mDACT,IAAK,OACI,MAAA,gDACT,IAAK,UACI,MAAA,sDACT,IAAK,OACI,MAAA,sDACT,IAAK,WACI,MAAA,6CACT,QACS,MAAA,+CACX,CAAA,EAGIC,GAA0BC,GAA2C,CACzE,OAAQA,EAAgB,CACtB,IAAK,OACI,MAAA,mDACT,IAAK,UACI,MAAA,sDACT,IAAK,SACI,MAAA,sDACT,IAAK,YACI,MAAA,6CACT,QACS,MAAA,+CACX,CAAA,EAGIC,EAAiBhC,EAAe,OACnCiC,GAAU7B,IAAW,OAAS6B,EAAM,cAAgB7B,CAAA,EAGvD,OAAIrD,EAEAc,EAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAACC,EAAAA,IAAAoE,EAAA,CAAU,UAAU,oCAAqC,CAAA,EACzDpE,EAAA,IAAA,OAAA,CAAK,UAAU,wCAAwC,SAExD,0BAAA,CACF,CAAA,CAAA,EAKFD,EAAA,KAAC,MAAI,CAAA,UAAU,yCACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8EACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,+DAA+D,SAE7E,qBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,CAAA,EACF,EACAD,EAAA,KAAC,SAAA,CACC,QAAS,IACPuB,IAAc,YACVyB,EAAA,EACAU,EAAsB,EAE5B,UAAU,8GACV,aAAW,kBAEX,SAAA,CAACzD,EAAAA,IAAAoE,EAAA,CAAU,UAAU,SAAU,CAAA,EAC/BpE,EAAAA,IAAC,QAAK,SAAS,WAAA,CAAA,CAAA,CAAA,CACjB,CAAA,EACF,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAU,gDACb,SAAAA,EAAAA,IAAC,MAAI,CAAA,UAAU,8BACZ,SAAA0C,EAAS,IAAK2B,GACbtE,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMwB,EAAa8C,EAAI,EAAE,EAClC,UAAW,wEACT/C,IAAc+C,EAAI,GACd,mDACA,wHACN,GACA,eAAc/C,IAAc+C,EAAI,GAAK,OAAS,OAE9C,SAAA,CAAArE,EAAAA,IAACqE,EAAI,KAAJ,CAAS,UAAU,SAAU,CAAA,EAC9BrE,EAAAA,IAAC,OAAM,CAAA,SAAAqE,EAAI,KAAM,CAAA,CAAA,CAAA,EAVZA,EAAI,EAAA,CAYZ,EACH,CACF,CAAA,EAEAtE,EAAAA,KAACuE,GAAgB,CAAA,KAAK,OACnB,SAAA,CAAAhD,IAAc,aACbvB,EAAA,KAACI,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,YAEV,SAAA,CAACJ,EAAAA,KAAA,MAAA,CAAI,UAAU,8EACb,SAAA,CAAAC,MAAC,OAAI,UAAU,gCACZ,SAAc4C,EAAA,IAAKyB,GAClBtE,EAAA,KAAC,SAAA,CAEC,QAAS,IAAMkC,EAAgBoC,EAAI,EAAE,EACrC,UAAW,0FACTrC,IAAiBqC,EAAI,GACjB,mEACA,2EACN,GAEA,SAAA,CAAArE,EAAAA,IAACqE,EAAI,KAAJ,CAAS,UAAU,SAAU,CAAA,EAC9BrE,EAAAA,IAAC,OAAM,CAAA,SAAAqE,EAAI,KAAM,CAAA,CAAA,CAAA,EATZA,EAAI,EAWZ,CAAA,EACH,EACArE,EAAA,IAAC,SAAA,CACC,MAAO8B,EACP,SAAWyC,GAAMxC,EAAkBwC,EAAE,OAAO,KAAe,EAC3D,UAAU,qHAET,SAAQ9B,EAAA,IAAKtD,GACXa,EAAAA,IAAA,SAAA,CAA0B,MAAOb,EAAO,MACtC,SAAAA,EAAO,KADG,EAAAA,EAAO,KAEpB,CACD,CAAA,CACH,CAAA,EACF,EAECqC,GACCxB,EAAA,IAAC,MAAI,CAAA,UAAU,gEACZ,SAAA,CACC,CACE,MAAO,iBACP,MAAOwB,EAAQ,WACf,KAAMsB,EACN,MAAO,MACT,EACA,CACE,MAAO,YACP,MAAOtB,EAAQ,iBACf,KAAMqB,EACN,MAAO,OACT,EACA,CACE,MAAO,kBACP,MAAOrB,EAAQ,YACf,KAAMX,EACN,MAAO,QACT,EACA,CACE,MAAO,aACP,MAAOW,EAAQ,cAAc,QAAQ,CAAC,EACtC,KAAMgD,GACN,MAAO,QACT,CACA,EAAA,IAAI,CAACC,EAAM7D,IACXZ,EAAA,IAACG,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,WAAY,CAAE,MAAOS,EAAQ,EAAI,EACjC,UAAU,wGAEV,SAAAb,EAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAAAC,EAAA,IAAC,IAAE,CAAA,UAAU,uDACV,SAAAyE,EAAK,MACR,EACCzE,EAAA,IAAA,IAAA,CAAE,UAAU,8DACV,WAAK,MACR,CAAA,EACF,EACAA,EAAA,IAAC,MAAA,CACC,UAAW,uBAAuByE,EAAK,KAAK,gBAAgBA,EAAK,KAAK,UAEtE,SAAAzE,EAAA,IAACyE,EAAK,KAAL,CACC,UAAW,8BAA8BA,EAAK,KAAK,kBAAkBA,EAAK,KAAK,MAAA,CACjF,CAAA,CACF,CAAA,EACF,CAAA,EAtBKA,EAAK,KAwBb,CAAA,EACH,EAGDzC,IAAiB,YAAcN,GAC7B3B,EAAA,KAAA,MAAA,CAAI,UAAU,iDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wGACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,cAAA,EACCA,EAAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAQ0B,EAAA,UAAU,MAAM,EAAG,CAAC,EAAE,IAAKgD,GAClC3E,EAAA,KAAC,MAAA,CAEC,UAAU,oCAEV,SAAA,CAAAC,EAAA,IAAC,OAAK,CAAA,UAAU,2CACb,SAAA0E,EAAM,MACT,EACA3E,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,6DACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,+BACV,MAAO,CAAE,MAAO,GAAG0E,EAAM,UAAU,GAAI,CAAA,CAAA,EAE3C,EACA3E,EAAAA,KAAC,OAAK,CAAA,UAAU,oDACb,SAAA,CAAM2E,EAAA,WAAW,GAAA,EACpB,CAAA,EACF,CAAA,CAAA,EAhBKA,EAAM,KAkBd,CAAA,EACH,CAAA,EACF,EACA3E,EAAAA,KAAC,MAAI,CAAA,UAAU,wGACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,eAAA,EACCA,EAAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAQ0B,EAAA,WAAW,MAAM,EAAG,CAAC,EAAE,IAAKiD,GACnC5E,EAAA,KAAC,MAAA,CAEC,UAAU,oCAEV,SAAA,CAAAC,EAAA,IAAC,OAAK,CAAA,UAAU,2CACb,SAAA2E,EAAO,OACV,EACA5E,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACb,SAAA,CAACC,EAAAA,IAAA,MAAA,CAAI,UAAU,6DACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,gCACV,MAAO,CAAE,MAAO,GAAG2E,EAAO,UAAU,GAAI,CAAA,CAAA,EAE5C,EACA5E,EAAAA,KAAC,OAAK,CAAA,UAAU,oDACb,SAAA,CAAO4E,EAAA,WAAW,GAAA,EACrB,CAAA,EACF,CAAA,CAAA,EAhBKA,EAAO,MAkBf,CAAA,EACH,CAAA,EACF,CAAA,EACF,EAGD3C,IAAiB,SAAWJ,EAAa,OAAS,GAChD7B,OAAA,MAAA,CAAI,UAAU,wGACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,oBAAA,EACCA,EAAAA,IAAA,MAAA,CAAI,UAAU,YACZ,SAAa4B,EAAA,MAAM,EAAG,EAAE,EAAE,IAAI,CAACgD,EAAMhE,IACpCb,EAAA,KAAC,MAAA,CAEC,UAAU,yEAEV,SAAA,CAACC,EAAA,IAAA,MAAA,CAAI,UAAU,sGACb,SAAAA,EAAAA,IAAC,QAAK,UAAU,uDACb,SAAQY,EAAA,CAAA,CACX,CACF,CAAA,EACAb,EAAAA,KAAC,MAAI,CAAA,UAAU,iBACb,SAAA,CAAAC,EAAA,IAAC,IAAE,CAAA,UAAU,6DACV,SAAA4E,EAAK,MACR,EACC5E,EAAA,IAAA,IAAA,CAAE,UAAU,oDACV,WAAK,OACR,CAAA,EACF,EACAD,EAAAA,KAAC,MAAI,CAAA,UAAU,uEACb,SAAA,CAAAA,OAAC,OAAM,CAAA,SAAA,CAAK6E,EAAA,MAAM,QAAA,EAAM,SACvB,OAAM,CAAA,SAAA,CAAKA,EAAA,MAAM,QAAA,EAAM,EACvB5E,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,WAAK,MAAM,CAAA,EAC5C,CAAA,CAAA,EApBK4E,EAAK,EAsBb,CAAA,EACH,CAAA,EACF,CAAA,CAAA,EA3LE,WA6LN,EAGDtD,IAAc,YACbvB,EAAA,KAACI,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAC5B,UAAU,YAET,SAAA,CACCiC,GAAArC,EAAA,KAAC,MAAI,CAAA,UAAU,wGACb,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,2DAA2D,SAEzE,oBAAA,EACAA,EAAAA,IAAC,MAAI,CAAA,UAAU,4DACZ,SAAA,CACC,CACE,MAAO,aACP,MAAOoC,EAAe,gBACtB,MAAO,OACT,EACA,CACE,MAAO,OACP,MAAOA,EAAe,WACtB,MAAO,MACT,EACA,CACE,MAAO,SACP,MAAOA,EAAe,cACtB,MAAO,QACT,EACA,CACE,MAAO,QACP,MAAOA,EAAe,WACtB,MAAO,QACT,EACA,CACE,MAAO,WACP,MAAOA,EAAe,eACtB,MAAO,KACT,EACA,CACE,MAAO,QACP,MAAOA,EAAe,YACtB,MAAO,MACT,CAAA,EACA,IAAKyC,GACJ9E,EAAA,KAAA,MAAA,CAAqB,UAAU,cAC9B,SAAA,CAAAC,EAAA,IAAC,MAAA,CACC,UAAW,sCAAsC6E,EAAK,KAAK,kBAAkBA,EAAK,KAAK,OAEtF,SAAKA,EAAA,KAAA,CACR,EACC7E,EAAA,IAAA,MAAA,CAAI,UAAU,2CACZ,WAAK,MACR,CARQ,CAAA,EAAA6E,EAAK,KASf,CACD,EACH,EACA9E,EAAAA,KAAC,MAAI,CAAA,UAAU,oCACb,SAAA,CAACC,EAAA,IAAA,OAAA,CAAK,UAAU,uDAAuD,SAEvE,eAAA,EACAD,EAAAA,KAAC,OAAK,CAAA,UAAU,kDACb,SAAA,CAAeqC,EAAA,aAAa,MAAA,EAC/B,CAAA,EACF,CAAA,EACF,EAGFrC,EAAAA,KAAC,MAAI,CAAA,UAAU,8EACb,SAAA,CAAAA,EAAA,KAAC,SAAA,CACC,MAAOuC,EACP,SAAWiC,GACThC,EAAUgC,EAAE,OAAO,KAA4B,EAEjD,UAAU,qHAEV,SAAA,CAACvE,EAAA,IAAA,SAAA,CAAO,MAAM,MAAM,SAAqB,wBAAA,EACxC,OAAO,OAAOgB,CAAW,EAAE,IAAK8D,GAC9B9E,MAAA,SAAA,CAAkB,MAAO8E,EACvB,WAAK,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAK,MAAM,CAAC,GADjCA,CAEb,CACD,CAAA,CAAA,CACH,EACA/E,EAAAA,KAAC,OAAK,CAAA,UAAU,2CACb,SAAA,CAAemE,EAAA,OAAO,sBAAA,EACzB,CAAA,EACF,EAEAnE,EAAAA,KAAC,MAAI,CAAA,UAAU,6GACb,SAAA,CAAAC,EAAAA,IAAC,OAAI,UAAU,kBACb,SAACD,EAAA,KAAA,QAAA,CAAM,UAAU,2DACf,SAAA,CAAAC,MAAC,QAAM,CAAA,UAAU,8BACf,SAAAD,EAAA,KAAC,KACC,CAAA,SAAA,CAACC,EAAA,IAAA,KAAA,CAAG,UAAU,4GAA4G,SAE1H,SAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,4GAA4G,SAE1H,cAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,4GAA4G,SAE1H,QAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,4GAA4G,SAE1H,QAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,4GAA4G,SAE1H,iBAAA,EACCA,EAAA,IAAA,KAAA,CAAG,UAAU,4GAA4G,SAE1H,eAAA,CAAA,CAAA,CACF,CACF,CAAA,QACC,QAAM,CAAA,UAAU,gDACd,SAAekE,EAAA,IAAKC,GACnBpE,EAAA,KAAC,KAAA,CAEC,UAAU,0CAEV,SAAA,CAACA,EAAAA,KAAA,KAAA,CAAG,UAAU,oBACZ,SAAA,CAAAC,EAAA,IAAC,MAAI,CAAA,UAAU,oDACZ,SAAAmE,EAAM,MACT,EACCnE,EAAA,IAAA,MAAA,CAAI,UAAU,2CACZ,WAAM,OACT,CAAA,EACF,EACAA,EAAAA,IAAC,KAAG,CAAA,UAAU,oBACZ,SAAAA,EAAA,IAAC,OAAA,CACC,UAAW,4DAA4D8D,EACrEK,EAAM,WAAA,CACP,GAEA,SAAAA,EAAM,YAAY,OAAO,CAAC,EAAE,cAC3BA,EAAM,YAAY,MAAM,CAAC,CAAA,CAAA,EAE/B,QACC,KAAG,CAAA,UAAU,0DACZ,SAACpE,EAAA,KAAA,MAAA,CAAI,UAAU,8BACb,SAAA,CAACA,EAAAA,KAAA,OAAA,CAAK,UAAU,iBAAiB,SAAA,CAAA,IAC7BoE,EAAM,OAAA,EACV,EACApE,EAAAA,KAAC,OAAK,CAAA,UAAU,eAAe,SAAA,CAAA,IAC3BoE,EAAM,SAAA,EACV,CAAA,CAAA,CACF,CACF,CAAA,EACCnE,EAAA,IAAA,KAAA,CAAG,UAAU,0DACX,WAAM,UACT,EACAD,EAAAA,KAAC,KAAG,CAAA,UAAU,0DACV,SAAA,EAAMoE,EAAA,eAAiB,KAAK,QAAQ,CAAC,EAAE,GAAA,EAC3C,EACAnE,EAAAA,IAAC,KAAG,CAAA,UAAU,oBACZ,SAAAA,EAAA,IAAC,OAAA,CACC,UAAW,4DAA4DgE,GACrEG,EAAM,cAAA,CACP,GAEA,SAAAA,EAAM,eAAe,OAAO,CAAC,EAAE,cAC9BA,EAAM,eAAe,MAAM,CAAC,CAAA,CAAA,EAElC,CAAA,CAAA,EA9CKA,EAAM,EAgDd,CAAA,EACH,CAAA,CAAA,CACF,CACF,CAAA,EACCD,EAAe,SAAW,SACxB,MAAI,CAAA,UAAU,oDAAoD,SAEnE,6CAAA,CAAA,EAEJ,CAAA,CAAA,EA7KI,UA8KN,EAGD5C,IAAc,eACbtB,EAAA,IAACG,EAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAE5B,eAAC1B,GAAqB,EAAA,CAAA,EANlB,aAON,CAAA,EAEJ,EAECiB,SACE,MAAI,CAAA,UAAU,wFACb,SAACK,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAACC,EAAAA,IAAA+E,GAAA,CAAQ,UAAU,6CAA8C,CAAA,EAChE/E,EAAA,IAAA,OAAA,CAAK,UAAU,iCAAkC,SAAMN,EAAA,CAAA,CAAA,CAC1D,CACF,CAAA,CAEJ,CAAA,CAAA,CAEJ,CACF,EAEAuB,GAAiB,YAAc"}