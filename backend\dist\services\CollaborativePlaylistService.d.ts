import { Playlist } from "../models/Playlist";
/**
 * 🎵 SERVIÇO DE PLAYLIST COLABORATIVA CENTRAL
 *
 * Usa uma única conta YouTube Premium para gerenciar todas as playlists
 * dos restaurantes, eliminando a necessidade de OAuth individual.
 */
export declare class CollaborativePlaylistService {
    private oauth2Client;
    private youtube;
    private _restaurantRepository;
    private _playlistRepository;
    private _suggestionRepository;
    private youtubeService;
    private get ws();
    private get restaurantRepository();
    private get playlistRepository();
    private get suggestionRepository();
    constructor();
    /**
     * 🏪 CRIAR PLAYLIST COLABORATIVA PARA NOVO RESTAURANTE
     */
    createRestaurantPlaylist(restaurantId: string, playlistName: string, description?: string): Promise<{
        success: boolean;
        playlistId?: string;
        youtubePlaylistId?: string;
        message: string;
    }>;
    /**
     * 🎵 ADICIONAR MÚSICAS INICIAIS À PLAYLIST
     */
    addInitialTracks(playlistId: string, videoIds: string[]): Promise<{
        success: boolean;
        message: string;
        addedCount?: number;
    }>;
    /**
     * 🚀 PROCESSAR SUGESTÃO PAGA (FILA PRIORITÁRIA)
     */
    processPaidSuggestion(restaurantId: string, suggestionId: string): Promise<{
        success: boolean;
        message: string;
        newPosition?: number;
    }>;
    /**
     * 🔒 Verifica se um vídeo pertence à playlist ativa do restaurante e retorna o track
     */
    private getActivePlaylistTrack;
    /**
     * 🗳️ PROCESSAR VOTO NORMAL (GRATUITO)
     */
    processNormalVote(restaurantId: string, youtubeVideoId: string, tableNumber?: number, clientSessionId?: string): Promise<{
        success: boolean;
        message: string;
        voteWeight: number;
    }>;
    /**
     * 💰 PROCESSAR SUPERVOTO (PAGO)
     */
    processSuperVote(restaurantId: string, youtubeVideoId: string, paymentAmount: number, paymentId: string, tableNumber?: number, clientSessionId?: string, clientMessage?: string, clientName?: string): Promise<{
        success: boolean;
        message: string;
        voteWeight: number;
    }>;
    /**
     * 🧮 CALCULAR PESO DO SUPERVOTO
     */
    private calculateSuperVoteWeight;
    /**
     * 🔄 ATUALIZAR METADADOS DE SUGESTÕES COM FALLBACK
     * Tenta recuperar metadados de vídeos que foram criados com dados mínimos
     */
    updateFallbackMetadata(restaurantId: string): Promise<{
        success: boolean;
        updated: number;
        message: string;
    }>;
    /**
     * 🔎 OBTER PLAYLIST ATIVA DO RESTAURANTE
     */
    getActivePlaylistForRestaurant(restaurantId: string): Promise<Playlist | null>;
    /**
     * 🔔 NOTIFICAR SUPERVOTO EM TEMPO REAL
     */
    private notifySuperVote;
    /**
     * 🔔 NOTIFICAR FALHA DA YOUTUBE API
     */
    private notifyYouTubeAPIFailure;
    /**
     * 🔔 NOTIFICAR ATUALIZAÇÃO DE METADADOS
     */
    private notifyMetadataUpdate;
    /**
     * 🔄 MARCAR MÚSICA EM COOLDOWN (10 MINUTOS)
     */
    markSongInCooldown(restaurantId: string, youtubeVideoId: string, cooldownMinutes?: number): Promise<void>;
    /**
     * 🔍 VERIFICAR SE MÚSICA ESTÁ EM COOLDOWN
     */
    isSongInCooldown(restaurantId: string, youtubeVideoId: string): Promise<boolean>;
    /**
     * 📊 OBTER RANKING DE VOTAÇÃO (COM FILTRO DE COOLDOWN)
     */
    getVotingRanking(restaurantId: string, limit?: number): Promise<{
        success: boolean;
        data?: Array<{
            youtubeVideoId: string;
            title?: string;
            artist?: string;
            voteCount: number;
            superVoteCount: number;
            normalVoteCount: number;
            totalRevenue: number;
            isPaid: boolean;
            paymentAmount: number;
            tableNumber?: number;
        }>;
        message: string;
    }>;
    /**
     * 🔄 REORDENAR PLAYLIST BASEADA EM VOTOS
     */
    reorderPlaylistByVotes(restaurantId: string): Promise<{
        success: boolean;
        message: string;
        reorderedCount?: number;
    }>;
    /**
     * Gera uma assinatura estável do ranking atual para detectar reaplicações.
     * Usa ordem + videoId + votos e pagamento para construir um hash simples.
     */
    private computeRankingSignature;
    /**
     * Marca o ranking atual (snapshot) como consumido por um período (TTL),
     * evitando que seja reaplicado até que mudanças ocorram (nova assinatura).
     */
    markRankingAsConsumed(restaurantId: string, items: Array<{
        youtubeVideoId: string;
        voteCount?: number;
        paymentAmount?: number;
        isPaid?: boolean;
    }>, ttlSeconds?: number): Promise<void>;
    /**
     * 📊 OBTER ESTATÍSTICAS DA PLAYLIST COLABORATIVA
     */
    getPlaylistStats(restaurantId: string): Promise<{
        success: boolean;
        data?: {
            playlistName: string;
            youtubePlaylistId: string;
            totalTracks: number;
            paidSuggestions: number;
            freeSuggestions: number;
            totalRevenue: number;
            totalVotes: number;
            activeTables: number;
        };
        message: string;
    }>;
}
export declare const collaborativePlaylistService: CollaborativePlaylistService;
//# sourceMappingURL=CollaborativePlaylistService.d.ts.map