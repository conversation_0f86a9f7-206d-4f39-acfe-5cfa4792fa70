{"version": 3, "file": "RestaurantProfile-8ad714fb.js", "sources": ["../../src/components/restaurant/RestaurantProfile.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useParams } from \"react-router-dom\";\r\nimport {\r\n  User,\r\n  Mail,\r\n  Phone,\r\n  MapPin,\r\n  Building2,\r\n  Clock,\r\n  Save,\r\n  Edit,\r\n  X,\r\n  Check,\r\n  AlertCircle,\r\n  RefreshCw,\r\n  Settings,\r\n  FileImage,\r\n  ExternalLink,\r\n  Eye,\r\n  Shield,\r\n  Bell,\r\n} from \"lucide-react\";\r\nimport { YouTubeAuthManager } from \"@/components/restaurant/YouTubeAuthManager\";\r\nimport { toast } from \"react-hot-toast\";\r\nimport { buildApiUrl, API_CONFIG } from \"../../config/api\";\r\n\r\n// Tipos e Interfaces\r\ntype TabType = \"basic\" | \"hours\" | \"settings\" | \"appearance\" | \"integrations\";\r\n\r\ninterface BusinessHours {\r\n  open: string;\r\n  close: string;\r\n  isOpen: boolean;\r\n}\r\n\r\ninterface RestaurantSettings {\r\n  // Já existentes\r\n  allowSuggestions: boolean;\r\n  maxSuggestionsPerUser: number;\r\n  autoPlayEnabled: boolean;\r\n  autoSkipDisliked?: boolean;\r\n  // Da tela Settings antiga (fundidas)\r\n  allowVoting?: boolean;\r\n  showQueue?: boolean;\r\n  showVoteCounts?: boolean;\r\n  // Interface\r\n  darkMode?: boolean;\r\n  primaryColor?: string;\r\n  // Moderação\r\n  moderation?: {\r\n    autoApprove: boolean;\r\n    requireModeration: boolean;\r\n    bannedWords: string[];\r\n    maxVotesForAutoApproval: number;\r\n    minVotesForAutoRejection: number;\r\n  };\r\n  // Agenda (horário de abertura/fechamento geral)\r\n  schedule?: {\r\n    enabled: boolean;\r\n    openTime: string;\r\n    closeTime: string;\r\n    timezone: string;\r\n    closedMessage: string;\r\n  };\r\n  // Notificações\r\n  notifications?: {\r\n    emailNotifications: boolean;\r\n    newSuggestionAlert: boolean;\r\n    highVoteAlert: boolean;\r\n    moderationAlert: boolean;\r\n  };\r\n  // Áudio\r\n  audio?: {\r\n    volume: number; // 0-100\r\n    fadeInDuration: number; // s\r\n    fadeOutDuration: number; // s\r\n    crossfade: boolean;\r\n  };\r\n  // Integração YouTube (UI no Profile)\r\n  youtube?: {\r\n    enabled?: boolean;\r\n  };\r\n}\r\n\r\ntype AddressObj = {\r\n  street?: string;\r\n  city?: string;\r\n  state?: string;\r\n  zipCode?: string;\r\n  country?: string;\r\n};\r\n\r\ninterface RestaurantProfile {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  address: string | AddressObj;\r\n  description: string;\r\n  businessHours: {\r\n    [key: string]: BusinessHours;\r\n  };\r\n  settings: RestaurantSettings;\r\n  logoUrl?: string;\r\n  bannerUrl?: string;\r\n  appearance?: {\r\n    darkMode: boolean;\r\n    primaryColor: string;\r\n    accentColor: string;\r\n    fontFamily: string;\r\n  };\r\n  integrations?: {\r\n    youtubeApiEnabled: boolean;\r\n    spotifyConnected: boolean;\r\n    googleAnalytics: boolean;\r\n  };\r\n}\r\n\r\ninterface TabProps {\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  id: TabType;\r\n  active: boolean;\r\n  onClick: (id: TabType) => void;\r\n}\r\n\r\n// Componente de Tab\r\nconst Tab: React.FC<TabProps> = ({ icon, label, id, active, onClick }) => (\r\n  <button\r\n    onClick={() => onClick(id)}\r\n    className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${\r\n      active\r\n        ? \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 font-medium\"\r\n        : \"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800\"\r\n    }`}\r\n    aria-selected={active}\r\n    role=\"tab\"\r\n    id={`tab-${id}`}\r\n    aria-controls={`panel-${id}`}\r\n  >\r\n    {React.cloneElement(icon as React.ReactElement, {\r\n      className: `w-5 h-5 ${\r\n        active\r\n          ? \"text-blue-600 dark:text-blue-400\"\r\n          : \"text-gray-500 dark:text-gray-400\"\r\n      }`,\r\n    })}\r\n    <span>{label}</span>\r\n  </button>\r\n);\r\n\r\n// Componente Principal\r\nconst RestaurantProfile: React.FC = () => {\r\n  const { restaurantId } = useParams<{ restaurantId: string }>();\r\n\r\n  // Mapeamento entre frontend e backend schemas\r\n  const mapBackendToFrontend = (backendSettings: any): Partial<RestaurantSettings> => {\r\n    return {\r\n      // Configurações básicas\r\n      allowSuggestions: backendSettings?.interface?.allowAnonymousSuggestions ?? true,\r\n      maxSuggestionsPerUser: backendSettings?.moderation?.maxSuggestionsPerUser ?? 3,\r\n      autoPlayEnabled: true, // Não existe no backend, sempre true\r\n      autoSkipDisliked: false, // Não existe no backend, sempre false\r\n\r\n      // Interface do cliente (mapeamento do backend.interface)\r\n      allowVoting: backendSettings?.interface?.showVoteCount ?? true,\r\n      showQueue: backendSettings?.interface?.showQueuePosition ?? true,\r\n      showVoteCounts: backendSettings?.interface?.showVoteCount ?? true,\r\n\r\n      // Aparência (mapeamento do backend.interface)\r\n      darkMode: backendSettings?.interface?.theme === \"dark\",\r\n      primaryColor: backendSettings?.interface?.primaryColor ?? \"#3B82F6\",\r\n\r\n      // Moderação (mapeamento do backend.moderation)\r\n      moderation: {\r\n        autoApprove: backendSettings?.moderation?.autoApprove ?? false,\r\n        requireModeration: backendSettings?.moderation?.requireApproval ?? true,\r\n        bannedWords: [], // Não implementado no backend ainda\r\n        maxVotesForAutoApproval: 10, // Não existe no backend\r\n        minVotesForAutoRejection: -5, // Não existe no backend\r\n      },\r\n\r\n      // Notificações (mapeamento do backend.notifications.email)\r\n      notifications: {\r\n        emailNotifications: backendSettings?.notifications?.email?.enabled ?? true,\r\n        newSuggestionAlert: backendSettings?.notifications?.email?.newSuggestions ?? true,\r\n        highVoteAlert: true, // Não existe no backend\r\n        moderationAlert: backendSettings?.notifications?.email?.moderationRequired ?? true,\r\n      },\r\n\r\n      // Áudio (mapeamento do backend.playlist)\r\n      audio: {\r\n        volume: backendSettings?.playlist?.defaultVolume ?? 75,\r\n        fadeInDuration: backendSettings?.playlist?.crossfadeDuration ?? 3,\r\n        fadeOutDuration: backendSettings?.playlist?.crossfadeDuration ?? 3,\r\n        crossfade: backendSettings?.playlist?.crossfadeDuration > 0,\r\n      },\r\n\r\n      // Schedule (mapeamento do backend.schedule)\r\n      schedule: {\r\n        enabled: true,\r\n        openTime: \"11:00\",\r\n        closeTime: \"23:00\",\r\n        timezone: backendSettings?.schedule?.timezone ?? \"America/Sao_Paulo\",\r\n        closedMessage: \"Estamos fechados. Volte durante nosso horário de funcionamento!\",\r\n      },\r\n\r\n      // YouTube\r\n      youtube: { enabled: true },\r\n    };\r\n  };\r\n\r\n  const mapFrontendToBackend = (frontendSettings: RestaurantSettings): any => {\r\n    return {\r\n      moderation: {\r\n        autoApprove: frontendSettings.moderation?.autoApprove ?? false,\r\n        requireApproval: frontendSettings.moderation?.requireModeration ?? true,\r\n        maxSuggestionsPerUser: frontendSettings.maxSuggestionsPerUser ?? 3,\r\n        maxSuggestionsPerHour: 10, // Valor fixo\r\n        allowExplicitContent: false, // Valor fixo\r\n        allowLiveVideos: true, // Valor fixo\r\n        minVideoDuration: 30, // Valor fixo\r\n        maxVideoDuration: 600, // Valor fixo\r\n      },\r\n      playlist: {\r\n        maxQueueSize: 50, // Valor fixo\r\n        allowDuplicates: false, // Valor fixo\r\n        shuffleMode: false, // Valor fixo\r\n        repeatMode: \"none\", // Valor fixo\r\n        crossfadeDuration: frontendSettings.audio?.crossfade ? (frontendSettings.audio?.fadeInDuration ?? 3) : 0,\r\n        defaultVolume: frontendSettings.audio?.volume ?? 75,\r\n      },\r\n      interface: {\r\n        theme: frontendSettings.darkMode ? \"dark\" : \"light\",\r\n        primaryColor: frontendSettings.primaryColor ?? \"#3B82F6\",\r\n        secondaryColor: \"#10B981\", // Valor fixo\r\n        showVoteCount: frontendSettings.allowVoting ?? true,\r\n        showQueuePosition: frontendSettings.showQueue ?? true,\r\n        allowAnonymousSuggestions: frontendSettings.allowSuggestions ?? true,\r\n        requireSessionId: true, // Valor fixo\r\n      },\r\n      notifications: {\r\n        email: {\r\n          enabled: frontendSettings.notifications?.emailNotifications ?? true,\r\n          newSuggestions: frontendSettings.notifications?.newSuggestionAlert ?? true,\r\n          moderationRequired: frontendSettings.notifications?.moderationAlert ?? true,\r\n          dailyReport: false, // Valor fixo\r\n        },\r\n        webhook: {\r\n          enabled: false, // Valor fixo\r\n          url: \"\", // Valor fixo\r\n          events: [], // Valor fixo\r\n        },\r\n      },\r\n      schedule: {\r\n        timezone: frontendSettings.schedule?.timezone ?? \"America/Sao_Paulo\",\r\n        // operatingHours será gerenciado pela aba de horários\r\n      },\r\n    };\r\n  };\r\n\r\n  // Valores padrão usando o mapeamento\r\n  const defaultMergedSettings = mapBackendToFrontend({});\r\n\r\n  // Validar que restaurantId existe\r\n  if (!restaurantId) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-2xl font-bold text-red-600\">Erro de Rota</h1>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            ID do restaurante não fornecido na URL\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const [profile, setProfile] = useState<RestaurantProfile | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const mergeSettings = (incoming: Partial<RestaurantSettings>): RestaurantSettings => {\r\n    return {\r\n      ...defaultMergedSettings,\r\n      ...incoming,\r\n      moderation: { ...defaultMergedSettings.moderation!, ...incoming.moderation },\r\n      schedule: { ...defaultMergedSettings.schedule!, ...incoming.schedule },\r\n      notifications: { ...defaultMergedSettings.notifications!, ...incoming.notifications },\r\n      audio: { ...defaultMergedSettings.audio!, ...incoming.audio },\r\n      youtube: { ...defaultMergedSettings.youtube!, ...incoming.youtube },\r\n    } as RestaurantSettings;\r\n  };\r\n\r\n  const [editing, setEditing] = useState(false);\r\n  const [saving, setSaving] = useState(false);\r\n  const [editedProfile, setEditedProfile] = useState<RestaurantProfile | null>(\r\n    null\r\n  );\r\n  const [activeTab, setActiveTab] = useState<TabType>(\"basic\");\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Função para carregar o perfil\r\n  const loadProfile = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      console.log(\"🏪 Carregando perfil do restaurante:\", restaurantId);\r\n\r\n      const url = buildApiUrl(\r\n        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`\r\n      );\r\n      console.log(\"🏪 URL:\", url);\r\n\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 10000);\r\n\r\n      const response = await fetch(url, {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        signal: controller.signal,\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🏪 Profile loaded:\", data);\r\n\r\n        if (data.success && data.profile) {\r\n          // Mapear configurações do backend para o frontend\r\n          const backendSettings = data.profile.settings || {};\r\n          const mappedSettings = mapBackendToFrontend(backendSettings);\r\n          const mergedSettings = mergeSettings(mappedSettings);\r\n\r\n          const profileData: RestaurantProfile = {\r\n            id: data.profile.id,\r\n            name: data.profile.name || \"\",\r\n            email: data.profile.email || \"\",\r\n            phone: data.profile.phone || \"\",\r\n            address: {\r\n              street: data.profile.address?.street || \"\",\r\n              city: data.profile.address?.city || \"\",\r\n              state: data.profile.address?.state || \"\",\r\n              zipCode: data.profile.address?.zipCode || \"\",\r\n              country: data.profile.address?.country || \"Brasil\",\r\n            },\r\n            description: data.profile.description || \"\",\r\n            logoUrl: data.profile.logo || \"\",\r\n            bannerUrl: data.profile.banner || \"\",\r\n            businessHours: data.profile.businessHours || {},\r\n            settings: mergedSettings,\r\n            appearance: data.profile.appearance || {\r\n              darkMode: false,\r\n              primaryColor: \"#3b82f6\",\r\n              accentColor: \"#10b981\",\r\n              fontFamily: \"Inter\",\r\n            },\r\n            integrations: data.profile.integrations || {\r\n              youtubeApiEnabled: false,\r\n              spotifyConnected: false,\r\n              googleAnalytics: false,\r\n            },\r\n          };\r\n\r\n          setProfile(profileData);\r\n          setEditedProfile(profileData);\r\n        } else {\r\n          throw new Error(data.message || \"Dados do perfil inválidos\");\r\n        }\r\n      } else {\r\n        console.error(\r\n          \"🏪 Erro ao carregar perfil:\",\r\n          response.status,\r\n          response.statusText\r\n        );\r\n        const errorText = await response.text().catch(() => \"\");\r\n        console.error(\"🏪 Error details:\", errorText);\r\n        throw new Error(\r\n          `Erro ${response.status}: ${response.statusText || \"Erro desconhecido\"}`\r\n        );\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"🏪 Erro ao carregar perfil:\", error);\r\n      if (error.name === \"AbortError\") {\r\n        setError(\"Timeout: A requisição demorou muito para responder\");\r\n      } else if (error.message?.includes(\"Failed to fetch\")) {\r\n        setError(\"Erro de conexão: Verifique sua internet\");\r\n      } else {\r\n        setError(error.message || \"Erro desconhecido ao carregar perfil\");\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [restaurantId]);\r\n\r\n  // Garante que o carregamento do perfil ocorra sempre, mesmo quando loading === true no primeiro render\r\n  useEffect(() => {\r\n    loadProfile();\r\n  }, [loadProfile]);\r\n\r\n  // Estados de loading e error - retorno antecipado\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <RefreshCw\r\n          className=\"w-8 h-8 animate-spin text-blue-600\"\r\n          aria-hidden=\"true\"\r\n        />\r\n        <span className=\"sr-only\">Carregando perfil do restaurante</span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!profile) {\r\n    return (\r\n      <div className=\"text-center py-12 bg-red-50 dark:bg-red-900/20 rounded-lg\">\r\n        <AlertCircle\r\n          className=\"w-12 h-12 text-red-400 mx-auto mb-4\"\r\n          aria-hidden=\"true\"\r\n        />\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n          Erro ao carregar perfil\r\n        </h3>\r\n        <p className=\"text-red-500 mb-4\">\r\n          {error || \"Não foi possível carregar os dados do perfil\"}\r\n        </p>\r\n        <button\r\n          onClick={loadProfile}\r\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800\"\r\n        >\r\n          Tentar novamente\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const daysOfWeek = [\r\n    { key: \"monday\", label: \"Segunda-feira\" },\r\n    { key: \"tuesday\", label: \"Terça-feira\" },\r\n    { key: \"wednesday\", label: \"Quarta-feira\" },\r\n    { key: \"thursday\", label: \"Quinta-feira\" },\r\n    { key: \"friday\", label: \"Sexta-feira\" },\r\n    { key: \"saturday\", label: \"Sábado\" },\r\n    { key: \"sunday\", label: \"Domingo\" },\r\n  ];\r\n\r\n  const tabs = [\r\n    {\r\n      id: \"basic\" as TabType,\r\n      label: \"Informações Básicas\",\r\n      icon: <Building2 />,\r\n    },\r\n    {\r\n      id: \"hours\" as TabType,\r\n      label: \"Horário de Funcionamento\",\r\n      icon: <Clock />,\r\n    },\r\n    { id: \"settings\" as TabType, label: \"Configurações\", icon: <Settings /> },\r\n    { id: \"appearance\" as TabType, label: \"Aparência\", icon: <FileImage /> },\r\n    {\r\n      id: \"integrations\" as TabType,\r\n      label: \"Integrações\",\r\n      icon: <ExternalLink />,\r\n    },\r\n  ];\r\n\r\n  const handleSave = async () => {\r\n    if (!editedProfile) return;\r\n\r\n    try {\r\n      setSaving(true);\r\n      console.log(\"🏪 Salvando perfil do restaurante:\", restaurantId);\r\n\r\n      const url = buildApiUrl(\r\n        `${API_CONFIG.ENDPOINTS.RESTAURANTS}/${restaurantId}/profile`\r\n      );\r\n      console.log(\"🏪 Salvando para:\", url);\r\n\r\n      // Mapear configurações do frontend para o formato do backend\r\n      const backendSettings = mapFrontendToBackend(editedProfile.settings);\r\n      const profileToSave = {\r\n        ...editedProfile,\r\n        settings: backendSettings,\r\n      };\r\n\r\n      console.log(\"🔄 Frontend settings:\", editedProfile.settings);\r\n      console.log(\"🔄 Backend settings:\", backendSettings);\r\n      console.log(\"🔄 Profile to save:\", profileToSave);\r\n\r\n      const response = await fetch(url, {\r\n        method: \"PUT\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify(profileToSave),\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        console.log(\"🏪 Profile saved:\", data);\r\n        setProfile(data.profile || editedProfile);\r\n        setEditing(false);\r\n        toast.success(\"Perfil atualizado com sucesso!\");\r\n      } else {\r\n        console.error(\r\n          \"🏪 Erro ao salvar:\",\r\n          response.status,\r\n          response.statusText\r\n        );\r\n        const errorText = await response.text().catch(() => \"\");\r\n        console.error(\"🏪 Error details:\", errorText);\r\n        throw new Error(`API returned status ${response.status}`);\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"🏪 Erro ao salvar perfil:\", error);\r\n\r\n      // Mesmo em caso de erro de conectividade, salvar localmente\r\n      setProfile(editedProfile);\r\n      setEditing(false);\r\n\r\n      if (error?.response?.status) {\r\n        toast.error(\r\n          `Erro ${error.response.status}: ${\r\n            error.response.data?.message || \"Erro ao salvar perfil\"\r\n          }`\r\n        );\r\n      } else {\r\n        toast(\"Perfil salvo localmente (sem conexão com servidor)\", { icon: \"⚠️\" });\r\n      }\r\n    } finally {\r\n      setSaving(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setEditedProfile(profile);\r\n    setEditing(false);\r\n  };\r\n\r\n  const updateField = (field: string, value: any) => {\r\n    if (!editedProfile) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      [field]: value,\r\n    });\r\n  };\r\n\r\n  const updateBusinessHours = (day: string, field: string, value: any) => {\r\n    if (!editedProfile) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      businessHours: {\r\n        ...editedProfile.businessHours,\r\n        [day]: {\r\n          ...editedProfile.businessHours[day],\r\n          [field]: value,\r\n        },\r\n      },\r\n    });\r\n  };\r\n\r\n  // Atualiza settings garantindo objeto completo para tipos aninhados\r\n  const updateSettingsSafe = <K extends keyof RestaurantSettings>(field: K, value: any) => {\r\n    if (!editedProfile) return;\r\n    const current = editedProfile.settings as RestaurantSettings;\r\n    let next: RestaurantSettings = current;\r\n    if (field === 'moderation') {\r\n      next = mergeSettings({ ...current, moderation: { ...current.moderation!, ...(value as any) } } as RestaurantSettings);\r\n    } else if (field === 'notifications') {\r\n      next = mergeSettings({ ...current, notifications: { ...current.notifications!, ...(value as any) } } as RestaurantSettings);\r\n    } else if (field === 'audio') {\r\n      next = mergeSettings({ ...current, audio: { ...current.audio!, ...(value as any) } } as RestaurantSettings);\r\n    } else {\r\n      next = mergeSettings({ ...current, [field]: value } as RestaurantSettings);\r\n    }\r\n    setEditedProfile({ ...editedProfile, settings: next });\r\n  };\r\n\r\n  const updateSettings = <K extends keyof RestaurantSettings>(field: K, value: any) => {\r\n    updateSettingsSafe(field, value);\r\n  };\r\n\r\n  const updateAppearance = (field: string, value: any) => {\r\n    if (!editedProfile || !editedProfile.appearance) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      appearance: {\r\n        ...editedProfile.appearance,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  const updateIntegrations = (field: string, value: any) => {\r\n    if (!editedProfile || !editedProfile.integrations) return;\r\n    setEditedProfile({\r\n      ...editedProfile,\r\n      integrations: {\r\n        ...editedProfile.integrations,\r\n        [field]: value,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Renderizadores de abas\r\n  const renderBasicInfo = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-basic\"\r\n      aria-labelledby=\"tab-basic\"\r\n    >\r\n      {/* Grid de Cards */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n\r\n        {/* Card 1: Informações de Contato */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Building2 className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Informações de Contato</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Dados básicos de identificação e contato do restaurante\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Nome do Restaurante */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"restaurant-name\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n              >\r\n                Nome do Restaurante\r\n              </label>\r\n              {editing ? (\r\n                <input\r\n                  id=\"restaurant-name\"\r\n                  type=\"text\"\r\n                  value={editedProfile?.name || \"\"}\r\n                  onChange={(e) => updateField(\"name\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              ) : (\r\n                <div className=\"flex items-center space-x-2 text-gray-900 dark:text-white\">\r\n                  <Building2 className=\"w-4 h-4 text-gray-500\" aria-hidden=\"true\" />\r\n                  <span className=\"font-medium\">{profile?.name}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Email */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"email\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n              >\r\n                Email\r\n              </label>\r\n              {editing ? (\r\n                <input\r\n                  id=\"email\"\r\n                  type=\"email\"\r\n                  value={editedProfile?.email || \"\"}\r\n                  onChange={(e) => updateField(\"email\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              ) : (\r\n                <div className=\"flex items-center space-x-2 text-gray-900 dark:text-white\">\r\n                  <Mail className=\"w-4 h-4 text-gray-500\" aria-hidden=\"true\" />\r\n                  <span className=\"font-medium\">{profile?.email}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Telefone */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"phone\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n              >\r\n                Telefone\r\n              </label>\r\n              {editing ? (\r\n                <input\r\n                  id=\"phone\"\r\n                  type=\"tel\"\r\n                  value={editedProfile?.phone || \"\"}\r\n                  onChange={(e) => updateField(\"phone\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              ) : (\r\n                <div className=\"flex items-center space-x-2 text-gray-900 dark:text-white\">\r\n                  <Phone className=\"w-4 h-4 text-gray-500\" aria-hidden=\"true\" />\r\n                  <span className=\"font-medium\">{profile?.phone}</span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card 2: Localização */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <MapPin className=\"w-5 h-5 text-green-600 dark:text-green-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Localização</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Endereço e informações de localização do restaurante\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Endereço */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"address\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n              >\r\n                Endereço Completo\r\n              </label>\r\n              {editing ? (\r\n                <textarea\r\n                  id=\"address\"\r\n                  rows={3}\r\n                  value={\r\n                    typeof editedProfile?.address === \"string\"\r\n                      ? editedProfile?.address\r\n                      : [\r\n                          (editedProfile?.address as AddressObj)?.street,\r\n                          (editedProfile?.address as AddressObj)?.city,\r\n                          (editedProfile?.address as AddressObj)?.state,\r\n                          (editedProfile?.address as AddressObj)?.zipCode,\r\n                          (editedProfile?.address as AddressObj)?.country,\r\n                        ]\r\n                          .filter(Boolean)\r\n                          .join(\", \")\r\n                  }\r\n                  onChange={(e) => updateField(\"address\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\r\n                />\r\n              ) : (\r\n                <div className=\"flex items-start space-x-2 text-gray-900 dark:text-white\">\r\n                  <MapPin className=\"w-4 h-4 text-gray-500 mt-0.5\" aria-hidden=\"true\" />\r\n                  <span className=\"font-medium\">\r\n                    {typeof profile?.address === \"string\"\r\n                      ? profile?.address\r\n                      : [\r\n                          (profile?.address as AddressObj)?.street,\r\n                          (profile?.address as AddressObj)?.city,\r\n                          (profile?.address as AddressObj)?.state,\r\n                          (profile?.address as AddressObj)?.zipCode,\r\n                          (profile?.address as AddressObj)?.country,\r\n                        ]\r\n                          .filter(Boolean)\r\n                          .join(\", \")}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Segunda linha - Card de Descrição */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n        <div className=\"flex items-center space-x-2 mb-4\">\r\n          <FileImage className=\"w-5 h-5 text-purple-600 dark:text-purple-400\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Descrição</h3>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n          Descrição detalhada do restaurante para os clientes\r\n        </p>\r\n\r\n        <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n          <label\r\n            htmlFor=\"description\"\r\n            className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\"\r\n          >\r\n            Descrição do Restaurante\r\n          </label>\r\n          {editing ? (\r\n            <textarea\r\n              id=\"description\"\r\n              value={editedProfile?.description || \"\"}\r\n              onChange={(e) => updateField(\"description\", e.target.value)}\r\n              rows={4}\r\n              placeholder=\"Descreva seu restaurante, especialidades, ambiente...\"\r\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none\"\r\n            />\r\n          ) : (\r\n            <div className=\"text-gray-900 dark:text-white\">\r\n              <span className=\"font-medium\">{profile?.description || \"Nenhuma descrição fornecida\"}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Terceira linha - Card de Imagens */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n        <div className=\"flex items-center space-x-2 mb-4\">\r\n          <FileImage className=\"w-5 h-5 text-orange-600 dark:text-orange-400\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Imagens do Restaurante</h3>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n          Logo e imagens que representam seu restaurante\r\n        </p>\r\n\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n          {/* Logo */}\r\n          <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n              Logo do Restaurante\r\n            </label>\r\n            {editing ? (\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-center justify-center h-40 w-40 mx-auto border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 overflow-hidden\">\r\n                  {editedProfile?.logoUrl ? (\r\n                    <img\r\n                      src={editedProfile.logoUrl}\r\n                      alt=\"Logo do Restaurante\"\r\n                      className=\"max-h-full max-w-full object-contain\"\r\n                    />\r\n                  ) : (\r\n                    <div className=\"text-center p-4\">\r\n                      <FileImage\r\n                        className=\"w-8 h-8 text-gray-400 mx-auto mb-2\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Logo do restaurante\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"URL da imagem do logo\"\r\n                  value={editedProfile?.logoUrl || \"\"}\r\n                  onChange={(e) => updateField(\"logoUrl\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center justify-center h-40 w-40 mx-auto bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600\">\r\n                {profile?.logoUrl ? (\r\n                  <img\r\n                    src={profile.logoUrl}\r\n                    alt=\"Logo do Restaurante\"\r\n                    className=\"max-h-full max-w-full object-contain\"\r\n                  />\r\n                ) : (\r\n                  <Building2\r\n                    className=\"w-12 h-12 text-gray-400\"\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Banner */}\r\n          <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\r\n              Banner do Restaurante\r\n            </label>\r\n            {editing ? (\r\n              <div className=\"space-y-3\">\r\n                <div className=\"flex items-center justify-center h-32 w-full border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 overflow-hidden\">\r\n                  {editedProfile?.bannerUrl ? (\r\n                    <img\r\n                      src={editedProfile.bannerUrl}\r\n                      alt=\"Banner do Restaurante\"\r\n                      className=\"max-h-full max-w-full object-cover w-full h-full\"\r\n                    />\r\n                  ) : (\r\n                    <div className=\"text-center p-4\">\r\n                      <FileImage\r\n                        className=\"w-8 h-8 text-gray-400 mx-auto mb-2\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500\">\r\n                        Banner do restaurante\r\n                      </p>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"URL da imagem do banner\"\r\n                  value={editedProfile?.bannerUrl || \"\"}\r\n                  onChange={(e) => updateField(\"bannerUrl\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center justify-center h-32 w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600\">\r\n                {profile?.bannerUrl ? (\r\n                  <img\r\n                    src={profile.bannerUrl}\r\n                    alt=\"Banner do Restaurante\"\r\n                    className=\"max-h-full max-w-full object-cover w-full h-full\"\r\n                  />\r\n                ) : (\r\n                  <Building2\r\n                    className=\"w-12 h-12 text-gray-400\"\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderBusinessHours = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-hours\"\r\n      aria-labelledby=\"tab-hours\"\r\n    >\r\n      {/* Card de Horários */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n        <div className=\"flex items-center space-x-2 mb-4\">\r\n          <Clock className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Horário de Funcionamento</h3>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n          Configure os horários de funcionamento para cada dia da semana\r\n        </p>\r\n\r\n        <div className=\"space-y-3\">\r\n          {daysOfWeek.map((day) => (\r\n            <div\r\n              key={day.key}\r\n              className=\"flex flex-wrap md:flex-nowrap items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-600\"\r\n            >\r\n              <div className=\"w-full md:w-32\">\r\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  {day.label}\r\n                </span>\r\n              </div>\r\n\r\n              {editing ? (\r\n                <>\r\n                  <label className=\"flex items-center space-x-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={\r\n                        editedProfile?.businessHours[day.key]?.isOpen || false\r\n                      }\r\n                      onChange={(e) =>\r\n                        updateBusinessHours(day.key, \"isOpen\", e.target.checked)\r\n                      }\r\n                      className=\"rounded text-blue-600 focus:ring-blue-500\"\r\n                    />\r\n                    <span className=\"text-sm\">Aberto</span>\r\n                  </label>\r\n\r\n                  {editedProfile?.businessHours[day.key]?.isOpen && (\r\n                    <div className=\"flex flex-wrap md:flex-nowrap items-center gap-2\">\r\n                      <label\r\n                        className=\"text-sm sr-only\"\r\n                        htmlFor={`open-${day.key}`}\r\n                      >\r\n                        Horário de abertura\r\n                      </label>\r\n                      <input\r\n                        id={`open-${day.key}`}\r\n                        type=\"time\"\r\n                        value={editedProfile.businessHours[day.key]?.open || \"\"}\r\n                        onChange={(e) =>\r\n                          updateBusinessHours(day.key, \"open\", e.target.value)\r\n                        }\r\n                        className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                      <span className=\"text-gray-500\">às</span>\r\n                      <label\r\n                        className=\"text-sm sr-only\"\r\n                        htmlFor={`close-${day.key}`}\r\n                      >\r\n                        Horário de fechamento\r\n                      </label>\r\n                      <input\r\n                        id={`close-${day.key}`}\r\n                        type=\"time\"\r\n                        value={\r\n                          editedProfile.businessHours[day.key]?.close || \"\"\r\n                        }\r\n                        onChange={(e) =>\r\n                          updateBusinessHours(day.key, \"close\", e.target.value)\r\n                        }\r\n                        className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </>\r\n              ) : (\r\n                <div className=\"flex items-center space-x-2\">\r\n                  {profile?.businessHours[day.key]?.isOpen ? (\r\n                    <>\r\n                      <Check\r\n                        className=\"w-5 h-5 text-green-600\"\r\n                        aria-hidden=\"true\"\r\n                      />\r\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\r\n                        {profile.businessHours[day.key]?.open} às{\" \"}\r\n                        {profile.businessHours[day.key]?.close}\r\n                      </span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <X className=\"w-5 h-5 text-red-600\" aria-hidden=\"true\" />\r\n                      <span className=\"text-sm text-gray-500\">Fechado</span>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderSettings = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-settings\"\r\n      aria-labelledby=\"tab-settings\"\r\n    >\r\n      {/* Grid de Cards */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n\r\n        {/* Card 1: Configurações Básicas */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Settings className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Configurações Básicas</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Controle fundamental da playlist e sugestões dos clientes\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Permitir Sugestões */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Permitir Sugestões dos Clientes\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Clientes podem sugerir músicas via QR code\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={editedProfile?.settings.allowSuggestions || false}\r\n                    onChange={(e) => updateSettings(\"allowSuggestions\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.allowSuggestions\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.allowSuggestions ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Reprodução Automática */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Reprodução Automática\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Próxima música inicia automaticamente\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={editedProfile?.settings.autoPlayEnabled || false}\r\n                    onChange={(e) => updateSettings(\"autoPlayEnabled\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.autoPlayEnabled\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.autoPlayEnabled ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Auto Skip */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Pular Músicas com Muitos Votos Negativos\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Músicas com muitos dislikes serão puladas automaticamente\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={editedProfile?.settings.autoSkipDisliked || false}\r\n                    onChange={(e) => updateSettings(\"autoSkipDisliked\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.autoSkipDisliked\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.autoSkipDisliked ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Máximo de Sugestões */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Máximo de Sugestões por Cliente\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Limite de sugestões por cliente por sessão\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"1\"\r\n                  max=\"10\"\r\n                  value={editedProfile?.settings.maxSuggestionsPerUser || 3}\r\n                  onChange={(e) => updateSettings(\"maxSuggestionsPerUser\", parseInt(e.target.value))}\r\n                  className=\"w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-center focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                />\r\n              ) : (\r\n                <div className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\r\n                  {profile?.settings.maxSuggestionsPerUser}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card 2: Interface do Cliente */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Eye className=\"w-5 h-5 text-green-600 dark:text-green-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Interface do Cliente</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Controles visuais que aparecem para os clientes\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Votação */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Permitir Votação\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Clientes podem votar nas músicas\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={!!editedProfile?.settings.allowVoting}\r\n                    onChange={(e) => updateSettings(\"allowVoting\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.allowVoting\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.allowVoting ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Mostrar Fila */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Mostrar Fila\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Exibir fila de reprodução para clientes\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={!!editedProfile?.settings.showQueue}\r\n                    onChange={(e) => updateSettings(\"showQueue\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.showQueue\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.showQueue ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Contagem de Votos */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Mostrar Contagem de Votos\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Exibir número de votos para clientes\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={!!editedProfile?.settings.showVoteCounts}\r\n                    onChange={(e) => updateSettings(\"showVoteCounts\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.showVoteCounts\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.showVoteCounts ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Segunda linha de cards */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n\r\n        {/* Card 3: Moderação */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Shield className=\"w-5 h-5 text-orange-600 dark:text-orange-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Moderação</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Controles de aprovação e moderação de conteúdo\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Auto Aprovar */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Aprovação Automática\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Aprovar automaticamente com base em votos\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={!!editedProfile?.settings.moderation?.autoApprove}\r\n                    onChange={(e) => updateSettings(\"moderation\", { ...editedProfile?.settings.moderation, autoApprove: e.target.checked })}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.moderation?.autoApprove\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.moderation?.autoApprove ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Moderação Manual */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Moderação Manual\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Exigir aprovação manual para sugestões\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={!!editedProfile?.settings.moderation?.requireModeration}\r\n                    onChange={(e) => updateSettings(\"moderation\", { ...editedProfile?.settings.moderation, requireModeration: e.target.checked })}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.settings.moderation?.requireModeration\r\n                    ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                    : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                }`}>\r\n                  {profile?.settings.moderation?.requireModeration ? \"Ativo\" : \"Inativo\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Limites de Votos */}\r\n            {editing && (\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                    Máx. votos para auto-aprovar\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={editedProfile?.settings.moderation?.maxVotesForAutoApproval ?? 10}\r\n                    onChange={(e) => updateSettings(\"moderation\", { ...editedProfile?.settings.moderation, maxVotesForAutoApproval: parseInt(e.target.value || '0', 10) })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n                    Mín. votos para auto-rejeitar\r\n                  </label>\r\n                  <input\r\n                    type=\"number\"\r\n                    value={editedProfile?.settings.moderation?.minVotesForAutoRejection ?? -5}\r\n                    onChange={(e) => updateSettings(\"moderation\", { ...editedProfile?.settings.moderation, minVotesForAutoRejection: parseInt(e.target.value || '0', 10) })}\r\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Palavras Banidas */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div className=\"flex items-center justify-between mb-3\">\r\n                <div>\r\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                    Palavras Banidas\r\n                  </label>\r\n                  <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                    Lista de palavras que serão automaticamente rejeitadas\r\n                  </p>\r\n                </div>\r\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                  {editedProfile?.settings.moderation?.bannedWords?.length || 0} palavras\r\n                </div>\r\n              </div>\r\n\r\n              {editing ? (\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex gap-2\">\r\n                    <input\r\n                      type=\"text\"\r\n                      placeholder=\"Adicionar palavra banida...\"\r\n                      className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                      onKeyPress={(e) => {\r\n                        if (e.key === 'Enter') {\r\n                          const input = e.target as HTMLInputElement;\r\n                          const word = input.value.trim().toLowerCase();\r\n                          if (word && !editedProfile?.settings.moderation?.bannedWords?.includes(word)) {\r\n                            updateSettings(\"moderation\", {\r\n                              ...editedProfile?.settings.moderation,\r\n                              bannedWords: [...(editedProfile?.settings.moderation?.bannedWords || []), word]\r\n                            });\r\n                            input.value = '';\r\n                          }\r\n                        }\r\n                      }}\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex flex-wrap gap-1\">\r\n                    {editedProfile?.settings.moderation?.bannedWords?.map((word, index) => (\r\n                      <span\r\n                        key={index}\r\n                        className=\"inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded text-xs\"\r\n                      >\r\n                        {word}\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => {\r\n                            const newWords = editedProfile?.settings.moderation?.bannedWords?.filter((_, i) => i !== index) || [];\r\n                            updateSettings(\"moderation\", {\r\n                              ...editedProfile?.settings.moderation,\r\n                              bannedWords: newWords\r\n                            });\r\n                          }}\r\n                          className=\"ml-1 hover:text-red-600 dark:hover:text-red-300\"\r\n                        >\r\n                          ×\r\n                        </button>\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"flex flex-wrap gap-1\">\r\n                  {profile?.settings.moderation?.bannedWords?.length ? (\r\n                    profile.settings.moderation.bannedWords.map((word, index) => (\r\n                      <span\r\n                        key={index}\r\n                        className=\"inline-flex items-center px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded text-xs\"\r\n                      >\r\n                        {word}\r\n                      </span>\r\n                    ))\r\n                  ) : (\r\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">Nenhuma palavra banida</span>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card 4: Notificações e Áudio */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Bell className=\"w-5 h-5 text-purple-600 dark:text-purple-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Notificações & Áudio</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Configurações de alertas e controles de áudio\r\n          </p>\r\n\r\n          <div className=\"space-y-6\">\r\n            {/* Notificações */}\r\n            <div>\r\n              <h4 className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3\">Notificações</h4>\r\n              <div className=\"grid grid-cols-1 gap-3\">\r\n                {[\r\n                  { key: \"emailNotifications\", label: \"Email\", desc: \"Receber notificações por email\" },\r\n                  { key: \"newSuggestionAlert\", label: \"Novas Sugestões\", desc: \"Alertas de novas sugestões\" },\r\n                  { key: \"highVoteAlert\", label: \"Votos Altos\", desc: \"Alertas de músicas com muitos votos\" },\r\n                  { key: \"moderationAlert\", label: \"Moderação\", desc: \"Alertas de moderação necessária\" }\r\n                ].map(({ key, label, desc }) => (\r\n                  <div key={key} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n                    <div>\r\n                      <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{label}</label>\r\n                      <p className=\"text-xs text-gray-500 dark:text-gray-400\">{desc}</p>\r\n                    </div>\r\n                    {editing ? (\r\n                      <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={!!editedProfile?.settings.notifications?.[key as keyof typeof editedProfile.settings.notifications]}\r\n                          onChange={(e) => updateSettings(\"notifications\", { ...editedProfile?.settings.notifications, [key]: e.target.checked })}\r\n                          className=\"sr-only peer\"\r\n                        />\r\n                        <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                      </label>\r\n                    ) : (\r\n                      <div className={`px-2 py-1 rounded text-xs font-medium ${\r\n                        profile?.settings.notifications?.[key as keyof typeof profile.settings.notifications]\r\n                          ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                          : \"bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300\"\r\n                      }`}>\r\n                        {profile?.settings.notifications?.[key as keyof typeof profile.settings.notifications] ? \"On\" : \"Off\"}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Áudio */}\r\n            <div>\r\n              <h4 className=\"text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3\">Configurações de Áudio</h4>\r\n              <div className=\"space-y-4\">\r\n                {/* Volume */}\r\n                <div className=\"p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Volume</label>\r\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{editedProfile?.settings.audio?.volume ?? 75}%</span>\r\n                  </div>\r\n                  {editing && (\r\n                    <input\r\n                      type=\"range\"\r\n                      min={0}\r\n                      max={100}\r\n                      value={editedProfile?.settings.audio?.volume ?? 75}\r\n                      onChange={(e) => updateSettings(\"audio\", { ...editedProfile?.settings.audio, volume: parseInt(e.target.value, 10) })}\r\n                      className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\r\n                    />\r\n                  )}\r\n                </div>\r\n\r\n                {/* Fade In/Out e Crossfade */}\r\n                {editing && (\r\n                  <div className=\"grid grid-cols-2 gap-3\">\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">Fade-in (s)</label>\r\n                      <input\r\n                        type=\"number\"\r\n                        value={editedProfile?.settings.audio?.fadeInDuration ?? 3}\r\n                        onChange={(e) => updateSettings(\"audio\", { ...editedProfile?.settings.audio, fadeInDuration: parseInt(e.target.value || '0', 10) })}\r\n                        className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">Fade-out (s)</label>\r\n                      <input\r\n                        type=\"number\"\r\n                        value={editedProfile?.settings.audio?.fadeOutDuration ?? 3}\r\n                        onChange={(e) => updateSettings(\"audio\", { ...editedProfile?.settings.audio, fadeOutDuration: parseInt(e.target.value || '0', 10) })}\r\n                        className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm\"\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Crossfade */}\r\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n                  <div>\r\n                    <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Crossfade</label>\r\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400\">Transição suave entre músicas</p>\r\n                  </div>\r\n                  {editing ? (\r\n                    <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={!!editedProfile?.settings.audio?.crossfade}\r\n                        onChange={(e) => updateSettings(\"audio\", { ...editedProfile?.settings.audio, crossfade: e.target.checked })}\r\n                        className=\"sr-only peer\"\r\n                      />\r\n                      <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                    </label>\r\n                  ) : (\r\n                    <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                      profile?.settings.audio?.crossfade\r\n                        ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                        : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n                    }`}>\r\n                      {profile?.settings.audio?.crossfade ? \"Ativo\" : \"Inativo\"}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Terceira linha de cards - Configurações Avançadas */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n\r\n        {/* Card 5: Configurações Avançadas de Playlist */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Settings className=\"w-5 h-5 text-indigo-600 dark:text-indigo-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Playlist Avançada</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Configurações avançadas de comportamento da playlist\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Tamanho Máximo da Fila */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Tamanho Máximo da Fila\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Número máximo de músicas na fila\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <input\r\n                  type=\"number\"\r\n                  min=\"10\"\r\n                  max=\"200\"\r\n                  value={50} // Valor fixo do backend\r\n                  disabled\r\n                  className=\"w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 text-center text-sm\"\r\n                />\r\n              ) : (\r\n                <div className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\r\n                  50\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Permitir Duplicatas */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Permitir Duplicatas\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Permitir a mesma música múltiplas vezes na fila\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded-full text-xs font-medium\">\r\n                Desabilitado\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modo Shuffle */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Modo Aleatório\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Reproduzir músicas em ordem aleatória\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 rounded-full text-xs font-medium\">\r\n                Desabilitado\r\n              </div>\r\n            </div>\r\n\r\n            {/* Modo Repetição */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Modo Repetição\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Como repetir a playlist\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 rounded-full text-xs font-medium\">\r\n                Nenhum\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card 6: Interface Avançada */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Eye className=\"w-5 h-5 text-teal-600 dark:text-teal-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Interface Avançada</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Configurações avançadas da interface do cliente\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Mostrar Posição na Fila */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Mostrar Posição na Fila\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Exibir posição da música na fila para clientes\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full text-xs font-medium\">\r\n                Ativo\r\n              </div>\r\n            </div>\r\n\r\n            {/* Sugestões Anônimas */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Sugestões Anônimas\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Permitir sugestões sem identificação do cliente\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full text-xs font-medium\">\r\n                Ativo\r\n              </div>\r\n            </div>\r\n\r\n            {/* Exigir ID de Sessão */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Exigir ID de Sessão\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Rastrear clientes por sessão única\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full text-xs font-medium\">\r\n                Ativo\r\n              </div>\r\n            </div>\r\n\r\n            {/* Tema da Interface */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Tema da Interface\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Tema padrão para clientes\r\n                </p>\r\n              </div>\r\n              <div className=\"px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded-full text-xs font-medium\">\r\n                Claro\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderAppearance = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-appearance\"\r\n      aria-labelledby=\"tab-appearance\"\r\n    >\r\n      {/* Grid de Cards */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n\r\n        {/* Card 1: Tema */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <FileImage className=\"w-5 h-5 text-purple-600 dark:text-purple-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Tema da Interface</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Configurações de aparência e tema do player de música\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Modo Escuro */}\r\n            <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <div>\r\n                <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                  Modo Escuro\r\n                </label>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                  Ativar interface com tema escuro para o player\r\n                </p>\r\n              </div>\r\n              {editing ? (\r\n                <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={editedProfile?.appearance?.darkMode || false}\r\n                    onChange={(e) => updateAppearance(\"darkMode\", e.target.checked)}\r\n                    className=\"sr-only peer\"\r\n                  />\r\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n                </label>\r\n              ) : (\r\n                <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                  profile?.appearance?.darkMode\r\n                    ? \"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400\"\r\n                    : \"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400\"\r\n                }`}>\r\n                  {profile?.appearance?.darkMode ? \"Escuro\" : \"Claro\"}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Card 2: Cores */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n          <div className=\"flex items-center space-x-2 mb-4\">\r\n            <Settings className=\"w-5 h-5 text-orange-600 dark:text-orange-400\" />\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Cores do Tema</h3>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n            Personalize as cores da interface do player\r\n          </p>\r\n\r\n          <div className=\"space-y-4\">\r\n            {/* Cor Primária */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"primary-color\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\"\r\n              >\r\n                Cor Primária\r\n              </label>\r\n              <div className=\"flex items-center space-x-3\">\r\n                {editing ? (\r\n                  <input\r\n                    id=\"primary-color\"\r\n                    type=\"color\"\r\n                    value={editedProfile?.appearance?.primaryColor || \"#3b82f6\"}\r\n                    onChange={(e) => updateAppearance(\"primaryColor\", e.target.value)}\r\n                    className=\"h-10 w-20 border-0 p-0 rounded cursor-pointer\"\r\n                  />\r\n                ) : (\r\n                  <div\r\n                    className=\"h-8 w-8 rounded-full border border-gray-300 dark:border-gray-600 shadow-sm\"\r\n                    style={{\r\n                      backgroundColor: profile?.appearance?.primaryColor || \"#3b82f6\",\r\n                    }}\r\n                  ></div>\r\n                )}\r\n                <span className=\"text-sm font-mono text-gray-700 dark:text-gray-300\">\r\n                  {editing ? editedProfile?.appearance?.primaryColor || \"#3b82f6\" : profile?.appearance?.primaryColor || \"#3b82f6\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Cor de Destaque */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"accent-color\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\"\r\n              >\r\n                Cor de Destaque\r\n              </label>\r\n              <div className=\"flex items-center space-x-3\">\r\n                {editing ? (\r\n                  <input\r\n                    id=\"accent-color\"\r\n                    type=\"color\"\r\n                    value={editedProfile?.appearance?.accentColor || \"#10b981\"}\r\n                    onChange={(e) => updateAppearance(\"accentColor\", e.target.value)}\r\n                    className=\"h-10 w-20 border-0 p-0 rounded cursor-pointer\"\r\n                  />\r\n                ) : (\r\n                  <div\r\n                    className=\"h-8 w-8 rounded-full border border-gray-300 dark:border-gray-600 shadow-sm\"\r\n                    style={{\r\n                      backgroundColor: profile?.appearance?.accentColor || \"#10b981\",\r\n                    }}\r\n                  ></div>\r\n                )}\r\n                <span className=\"text-sm font-mono text-gray-700 dark:text-gray-300\">\r\n                {editing ? editedProfile?.appearance?.accentColor || \"#10b981\" : profile?.appearance?.accentColor || \"#10b981\"}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Fonte */}\r\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n              <label\r\n                htmlFor=\"font-family\"\r\n                className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\"\r\n              >\r\n                Família da Fonte\r\n              </label>\r\n              {editing ? (\r\n                <select\r\n                  id=\"font-family\"\r\n                  value={editedProfile?.appearance?.fontFamily || \"Inter\"}\r\n                  onChange={(e) => updateAppearance(\"fontFamily\", e.target.value)}\r\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\r\n                >\r\n                  <option value=\"Inter\">Inter</option>\r\n                  <option value=\"Roboto\">Roboto</option>\r\n                  <option value=\"Poppins\">Poppins</option>\r\n                  <option value=\"Montserrat\">Montserrat</option>\r\n                  <option value=\"Open Sans\">Open Sans</option>\r\n                </select>\r\n              ) : (\r\n                <div className=\"text-gray-900 dark:text-white\">\r\n                  <span\r\n                    className=\"font-medium\"\r\n                    style={{\r\n                      fontFamily: profile?.appearance?.fontFamily || \"Inter\",\r\n                    }}\r\n                  >\r\n                    {profile?.appearance?.fontFamily || \"Inter\"}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Card de Visualização */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n        <div className=\"flex items-center space-x-2 mb-4\">\r\n          <Eye className=\"w-5 h-5 text-teal-600 dark:text-teal-400\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Visualização</h3>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n          Preview de como o player aparecerá para os clientes\r\n        </p>\r\n\r\n        <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n          <div\r\n            className=\"h-36 rounded-lg border border-gray-200 dark:border-gray-600 p-4 flex flex-col\"\r\n            style={{\r\n              backgroundColor: editedProfile?.appearance?.darkMode ? \"#1f2937\" : \"#ffffff\",\r\n              color: editedProfile?.appearance?.darkMode ? \"#f3f4f6\" : \"#111827\",\r\n              fontFamily: editedProfile?.appearance?.fontFamily || \"Inter\",\r\n            }}\r\n          >\r\n            <div className=\"text-center mb-3\">\r\n              <div\r\n                className=\"font-bold text-lg\"\r\n                style={{ color: editedProfile?.appearance?.primaryColor || \"#3b82f6\" }}\r\n              >\r\n                Agora Tocando\r\n              </div>\r\n              <div className=\"text-sm opacity-75\">Nome da Música - Artista</div>\r\n            </div>\r\n            <div className=\"flex-1 flex items-center justify-center\">\r\n              <div className=\"h-2 w-full rounded-full bg-gray-200 dark:bg-gray-600\">\r\n                <div\r\n                  className=\"h-2 rounded-full transition-all duration-300\"\r\n                  style={{\r\n                    width: \"40%\",\r\n                    backgroundColor: editedProfile?.appearance?.accentColor || \"#10b981\",\r\n                  }}\r\n                ></div>\r\n              </div>\r\n            </div>\r\n            <div className=\"flex justify-between text-xs mt-2 opacity-75\">\r\n              <span>1:30</span>\r\n              <span>3:45</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderIntegrations = () => (\r\n    <div\r\n      className=\"space-y-6\"\r\n      role=\"tabpanel\"\r\n      id=\"panel-integrations\"\r\n      aria-labelledby=\"tab-integrations\"\r\n    >\r\n      {/* Card de Integrações */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\r\n        <div className=\"flex items-center space-x-2 mb-4\">\r\n          <ExternalLink className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">Integrações</h3>\r\n        </div>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-6\">\r\n          Configure as integrações com serviços externos\r\n        </p>\r\n\r\n        <div className=\"space-y-4\">\r\n          {/* YouTube API */}\r\n          <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                API do YouTube\r\n              </label>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Usar a API oficial do YouTube para buscar músicas\r\n              </p>\r\n            </div>\r\n            {editing ? (\r\n              <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={editedProfile?.integrations?.youtubeApiEnabled || false}\r\n                  onChange={(e) => updateIntegrations(\"youtubeApiEnabled\", e.target.checked)}\r\n                  className=\"sr-only peer\"\r\n                />\r\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n              </label>\r\n            ) : (\r\n              <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                profile?.integrations?.youtubeApiEnabled\r\n                  ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                  : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n              }`}>\r\n                {profile?.integrations?.youtubeApiEnabled ? \"Ativo\" : \"Inativo\"}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* YouTube Auth Manager */}\r\n          <div className=\"p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n            <div className=\"mb-3\">\r\n              <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Autenticação YouTube\r\n              </label>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Configure a autenticação com sua conta YouTube\r\n              </p>\r\n            </div>\r\n            {restaurantId && (\r\n              <YouTubeAuthManager\r\n                restaurantId={restaurantId}\r\n                onAuthStatusChange={(authed) => {\r\n                  console.log(\"YouTube auth:\", authed);\r\n                }}\r\n              />\r\n            )}\r\n          </div>\r\n\r\n          {/* Spotify */}\r\n          <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Spotify\r\n              </label>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Integrar com sua conta Spotify para playlists\r\n              </p>\r\n            </div>\r\n            {editing ? (\r\n              <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={editedProfile?.integrations?.spotifyConnected || false}\r\n                  onChange={(e) => updateIntegrations(\"spotifyConnected\", e.target.checked)}\r\n                  className=\"sr-only peer\"\r\n                />\r\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n              </label>\r\n            ) : (\r\n              <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                profile?.integrations?.spotifyConnected\r\n                  ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                  : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n              }`}>\r\n                {profile?.integrations?.spotifyConnected ? \"Conectado\" : \"Desconectado\"}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Google Analytics */}\r\n          <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg\">\r\n            <div>\r\n              <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\r\n                Google Analytics\r\n              </label>\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\r\n                Monitorar estatísticas de uso do player\r\n              </p>\r\n            </div>\r\n            {editing ? (\r\n              <label className=\"relative inline-flex items-center cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={editedProfile?.integrations?.googleAnalytics || false}\r\n                  onChange={(e) => updateIntegrations(\"googleAnalytics\", e.target.checked)}\r\n                  className=\"sr-only peer\"\r\n                />\r\n                <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\r\n              </label>\r\n            ) : (\r\n              <div className={`px-3 py-1 rounded-full text-xs font-medium ${\r\n                profile?.integrations?.googleAnalytics\r\n                  ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400\"\r\n                  : \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400\"\r\n              }`}>\r\n                {profile?.integrations?.googleAnalytics ? \"Ativo\" : \"Inativo\"}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // Renderização principal do componente\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\r\n            Perfil do Restaurante\r\n          </h2>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\r\n            Gerencie as informações e configurações do seu restaurante\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"flex space-x-2\">\r\n          {editing ? (\r\n            <>\r\n              <button\r\n                onClick={handleCancel}\r\n                disabled={saving}\r\n                className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50\"\r\n              >\r\n                <X className=\"w-4 h-4 mr-2 inline\" />\r\n                Cancelar\r\n              </button>\r\n              <button\r\n                onClick={handleSave}\r\n                disabled={saving}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 flex items-center\"\r\n              >\r\n                {saving ? (\r\n                  <RefreshCw className=\"w-4 h-4 mr-2 animate-spin\" />\r\n                ) : (\r\n                  <Save className=\"w-4 h-4 mr-2\" />\r\n                )}\r\n                {saving ? \"Salvando...\" : \"Salvar\"}\r\n              </button>\r\n            </>\r\n          ) : (\r\n            <button\r\n              onClick={() => setEditing(true)}\r\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center\"\r\n            >\r\n              <Edit className=\"w-4 h-4 mr-2\" />\r\n              Editar\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Tabs */}\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700\">\r\n        <nav className=\"-mb-px flex space-x-8\" aria-label=\"Tabs\">\r\n          {tabs.map((tab) => (\r\n            <Tab\r\n              key={tab.id}\r\n              icon={tab.icon}\r\n              label={tab.label}\r\n              id={tab.id}\r\n              active={activeTab === tab.id}\r\n              onClick={setActiveTab}\r\n            />\r\n          ))}\r\n        </nav>\r\n      </div>\r\n\r\n      {/* Tab Content */}\r\n      <div className=\"mt-6\">\r\n        <AnimatePresence mode=\"wait\">\r\n          <motion.div\r\n            key={activeTab}\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -10 }}\r\n            transition={{ duration: 0.2 }}\r\n          >\r\n            {activeTab === \"basic\" && renderBasicInfo()}\r\n            {activeTab === \"hours\" && renderBusinessHours()}\r\n            {activeTab === \"settings\" && renderSettings()}\r\n            {activeTab === \"appearance\" && renderAppearance()}\r\n            {activeTab === \"integrations\" && renderIntegrations()}\r\n          </motion.div>\r\n        </AnimatePresence>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RestaurantProfile;"], "names": ["Tab", "icon", "label", "id", "active", "onClick", "jsxs", "React", "jsx", "RestaurantProfile", "restaurantId", "useParams", "mapBackendToFrontend", "backendSettings", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_k", "_j", "_m", "_l", "_o", "_n", "_p", "_q", "_r", "_s", "_t", "mapFrontendToBackend", "frontendSettings", "defaultMergedSettings", "profile", "setProfile", "useState", "loading", "setLoading", "mergeSettings", "incoming", "editing", "setEditing", "saving", "setSaving", "editedProfile", "setEditedProfile", "activeTab", "setActiveTab", "error", "setError", "loadProfile", "useCallback", "url", "buildApiUrl", "API_CONFIG", "controller", "timeoutId", "response", "data", "mappedSettings", "mergedSettings", "profileData", "errorText", "useEffect", "RefreshCw", "AlertCircle", "daysOfWeek", "tabs", "Building2", "Clock", "Settings", "FileImage", "ExternalLink", "handleSave", "profileToSave", "toast", "handleCancel", "updateField", "field", "value", "updateBusinessHours", "day", "updateSettingsSafe", "current", "next", "updateSettings", "updateAppearance", "updateIntegrations", "renderBasicInfo", "e", "Mail", "Phone", "MapPin", "renderBusinessHours", "Fragment", "Check", "X", "renderSettings", "Eye", "Shield", "input", "word", "index", "newWords", "_", "i", "Bell", "key", "desc", "_u", "renderAppearance", "renderIntegrations", "YouTubeAuthManager", "authed", "Save", "Edit", "tab", "AnimatePresence", "motion"], "mappings": "maAgIA,MAAMA,GAA0B,CAAC,CAAE,KAAAC,EAAM,MAAAC,EAAO,GAAAC,EAAI,OAAAC,EAAQ,QAAAC,KAC1DC,EAAA,KAAC,SAAA,CACC,QAAS,IAAMD,EAAQF,CAAE,EACzB,UAAW,sEACTC,EACI,4EACA,2EACN,GACA,gBAAeA,EACf,KAAK,MACL,GAAI,OAAOD,CAAE,GACb,gBAAe,SAASA,CAAE,GAEzB,SAAA,CAAAI,GAAM,aAAaN,EAA4B,CAC9C,UAAW,WACTG,EACI,mCACA,kCACN,EAAA,CACD,EACDI,EAAAA,IAAC,QAAM,SAAMN,CAAA,CAAA,CAAA,CAAA,CACf,EAIIO,GAA8B,IAAM,CAClC,KAAA,CAAE,aAAAC,GAAiBC,KAGnBC,EAAwBC,GAAsD,6CAC3E,MAAA,CAEL,mBAAkBC,EAAAD,GAAA,YAAAA,EAAiB,YAAjB,YAAAC,EAA4B,4BAA6B,GAC3E,wBAAuBC,EAAAF,GAAA,YAAAA,EAAiB,aAAjB,YAAAE,EAA6B,wBAAyB,EAC7E,gBAAiB,GACjB,iBAAkB,GAGlB,cAAaC,EAAAH,GAAA,YAAAA,EAAiB,YAAjB,YAAAG,EAA4B,gBAAiB,GAC1D,YAAWC,EAAAJ,GAAA,YAAAA,EAAiB,YAAjB,YAAAI,EAA4B,oBAAqB,GAC5D,iBAAgBC,EAAAL,GAAA,YAAAA,EAAiB,YAAjB,YAAAK,EAA4B,gBAAiB,GAG7D,WAAUC,EAAAN,GAAA,YAAAA,EAAiB,YAAjB,YAAAM,EAA4B,SAAU,OAChD,eAAcC,EAAAP,GAAA,YAAAA,EAAiB,YAAjB,YAAAO,EAA4B,eAAgB,UAG1D,WAAY,CACV,cAAaC,EAAAR,GAAA,YAAAA,EAAiB,aAAjB,YAAAQ,EAA6B,cAAe,GACzD,oBAAmBC,EAAAT,GAAA,YAAAA,EAAiB,aAAjB,YAAAS,EAA6B,kBAAmB,GACnE,YAAa,CAAC,EACd,wBAAyB,GACzB,yBAA0B,EAC5B,EAGA,cAAe,CACb,qBAAoBC,GAAAC,EAAAX,GAAA,YAAAA,EAAiB,gBAAjB,YAAAW,EAAgC,QAAhC,YAAAD,EAAuC,UAAW,GACtE,qBAAoBE,GAAAC,EAAAb,GAAA,YAAAA,EAAiB,gBAAjB,YAAAa,EAAgC,QAAhC,YAAAD,EAAuC,iBAAkB,GAC7E,cAAe,GACf,kBAAiBE,GAAAC,EAAAf,GAAA,YAAAA,EAAiB,gBAAjB,YAAAe,EAAgC,QAAhC,YAAAD,EAAuC,qBAAsB,EAChF,EAGA,MAAO,CACL,SAAQE,EAAAhB,GAAA,YAAAA,EAAiB,WAAjB,YAAAgB,EAA2B,gBAAiB,GACpD,iBAAgBC,EAAAjB,GAAA,YAAAA,EAAiB,WAAjB,YAAAiB,EAA2B,oBAAqB,EAChE,kBAAiBC,EAAAlB,GAAA,YAAAA,EAAiB,WAAjB,YAAAkB,EAA2B,oBAAqB,EACjE,YAAWC,EAAAnB,GAAA,YAAAA,EAAiB,WAAjB,YAAAmB,EAA2B,mBAAoB,CAC5D,EAGA,SAAU,CACR,QAAS,GACT,SAAU,QACV,UAAW,QACX,WAAUC,EAAApB,GAAA,YAAAA,EAAiB,WAAjB,YAAAoB,EAA2B,WAAY,oBACjD,cAAe,iEACjB,EAGA,QAAS,CAAE,QAAS,EAAK,CAAA,CAC3B,EAGIC,EAAwBC,GAA8C,uBACnE,MAAA,CACL,WAAY,CACV,cAAarB,EAAAqB,EAAiB,aAAjB,YAAArB,EAA6B,cAAe,GACzD,kBAAiBC,EAAAoB,EAAiB,aAAjB,YAAApB,EAA6B,oBAAqB,GACnE,sBAAuBoB,EAAiB,uBAAyB,EACjE,sBAAuB,GACvB,qBAAsB,GACtB,gBAAiB,GACjB,iBAAkB,GAClB,iBAAkB,GACpB,EACA,SAAU,CACR,aAAc,GACd,gBAAiB,GACjB,YAAa,GACb,WAAY,OACZ,mBAAmBnB,EAAAmB,EAAiB,QAAjB,MAAAnB,EAAwB,YAAaC,EAAAkB,EAAiB,QAAjB,YAAAlB,EAAwB,iBAAkB,EAAK,EACvG,gBAAeC,EAAAiB,EAAiB,QAAjB,YAAAjB,EAAwB,SAAU,EACnD,EACA,UAAW,CACT,MAAOiB,EAAiB,SAAW,OAAS,QAC5C,aAAcA,EAAiB,cAAgB,UAC/C,eAAgB,UAChB,cAAeA,EAAiB,aAAe,GAC/C,kBAAmBA,EAAiB,WAAa,GACjD,0BAA2BA,EAAiB,kBAAoB,GAChE,iBAAkB,EACpB,EACA,cAAe,CACb,MAAO,CACL,UAAShB,EAAAgB,EAAiB,gBAAjB,YAAAhB,EAAgC,qBAAsB,GAC/D,iBAAgBC,EAAAe,EAAiB,gBAAjB,YAAAf,EAAgC,qBAAsB,GACtE,qBAAoBC,EAAAc,EAAiB,gBAAjB,YAAAd,EAAgC,kBAAmB,GACvE,YAAa,EACf,EACA,QAAS,CACP,QAAS,GACT,IAAK,GACL,OAAQ,CAAC,CACX,CACF,EACA,SAAU,CACR,WAAUC,EAAAa,EAAiB,WAAjB,YAAAb,EAA2B,WAAY,mBAEnD,CAAA,CACF,EAIIc,EAAwBxB,EAAqB,CAAA,CAAE,EAGrD,GAAI,CAACF,EACH,aACG,MAAI,CAAA,UAAU,gDACb,SAACJ,EAAA,KAAA,MAAA,CAAI,UAAU,cACb,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,kCAAkC,SAAY,eAAA,EAC3DA,EAAA,IAAA,IAAA,CAAE,UAAU,qBAAqB,SAElC,yCAAA,CAAA,CACF,CAAA,CACF,CAAA,EAIJ,KAAM,CAAC6B,EAASC,CAAU,EAAIC,WAAmC,IAAI,EAC/D,CAACC,GAASC,EAAU,EAAIF,WAAS,EAAI,EACrCG,EAAiBC,IACd,CACL,GAAGP,EACH,GAAGO,EACH,WAAY,CAAE,GAAGP,EAAsB,WAAa,GAAGO,EAAS,UAAW,EAC3E,SAAU,CAAE,GAAGP,EAAsB,SAAW,GAAGO,EAAS,QAAS,EACrE,cAAe,CAAE,GAAGP,EAAsB,cAAgB,GAAGO,EAAS,aAAc,EACpF,MAAO,CAAE,GAAGP,EAAsB,MAAQ,GAAGO,EAAS,KAAM,EAC5D,QAAS,CAAE,GAAGP,EAAsB,QAAU,GAAGO,EAAS,OAAQ,CAAA,GAIhE,CAACC,EAASC,CAAU,EAAIN,WAAS,EAAK,EACtC,CAACO,EAAQC,EAAS,EAAIR,WAAS,EAAK,EACpC,CAACS,EAAeC,CAAgB,EAAIV,EAAA,SACxC,IAAA,EAEI,CAACW,EAAWC,EAAY,EAAIZ,WAAkB,OAAO,EACrD,CAACa,GAAOC,CAAQ,EAAId,WAAwB,IAAI,EAGhDe,EAAcC,EAAAA,YAAY,SAAY,iBACtC,GAAA,CACFd,GAAW,EAAI,EACfY,EAAS,IAAI,EACL,QAAA,IAAI,uCAAwC3C,CAAY,EAEhE,MAAM8C,EAAMC,GACV,GAAGC,GAAW,UAAU,WAAW,IAAIhD,CAAY,UAAA,EAE7C,QAAA,IAAI,UAAW8C,CAAG,EAEpB,MAAAG,EAAa,IAAI,gBACjBC,EAAY,WAAW,IAAMD,EAAW,MAAA,EAAS,GAAK,EAEtDE,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,OAAQG,EAAW,MAAA,CACpB,EAID,GAFA,aAAaC,CAAS,EAElBC,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OAGxB,GAFI,QAAA,IAAI,qBAAsBC,CAAI,EAElCA,EAAK,SAAWA,EAAK,QAAS,CAEhC,MAAMjD,EAAkBiD,EAAK,QAAQ,UAAY,CAAA,EAC3CC,EAAiBnD,EAAqBC,CAAe,EACrDmD,EAAiBtB,EAAcqB,CAAc,EAE7CE,EAAiC,CACrC,GAAIH,EAAK,QAAQ,GACjB,KAAMA,EAAK,QAAQ,MAAQ,GAC3B,MAAOA,EAAK,QAAQ,OAAS,GAC7B,MAAOA,EAAK,QAAQ,OAAS,GAC7B,QAAS,CACP,SAAQhD,EAAAgD,EAAK,QAAQ,UAAb,YAAAhD,EAAsB,SAAU,GACxC,OAAMC,EAAA+C,EAAK,QAAQ,UAAb,YAAA/C,EAAsB,OAAQ,GACpC,QAAOC,EAAA8C,EAAK,QAAQ,UAAb,YAAA9C,EAAsB,QAAS,GACtC,UAASC,EAAA6C,EAAK,QAAQ,UAAb,YAAA7C,EAAsB,UAAW,GAC1C,UAASC,EAAA4C,EAAK,QAAQ,UAAb,YAAA5C,EAAsB,UAAW,QAC5C,EACA,YAAa4C,EAAK,QAAQ,aAAe,GACzC,QAASA,EAAK,QAAQ,MAAQ,GAC9B,UAAWA,EAAK,QAAQ,QAAU,GAClC,cAAeA,EAAK,QAAQ,eAAiB,CAAC,EAC9C,SAAUE,EACV,WAAYF,EAAK,QAAQ,YAAc,CACrC,SAAU,GACV,aAAc,UACd,YAAa,UACb,WAAY,OACd,EACA,aAAcA,EAAK,QAAQ,cAAgB,CACzC,kBAAmB,GACnB,iBAAkB,GAClB,gBAAiB,EACnB,CAAA,EAGFxB,EAAW2B,CAAW,EACtBhB,EAAiBgB,CAAW,CAAA,KAE5B,OAAM,IAAI,MAAMH,EAAK,SAAW,2BAA2B,CAC7D,KACK,CACG,QAAA,MACN,8BACAD,EAAS,OACTA,EAAS,UAAA,EAEX,MAAMK,EAAY,MAAML,EAAS,KAAO,EAAA,MAAM,IAAM,EAAE,EAC9C,cAAA,MAAM,oBAAqBK,CAAS,EACtC,IAAI,MACR,QAAQL,EAAS,MAAM,KAAKA,EAAS,YAAc,mBAAmB,EAAA,CAE1E,QACOT,EAAY,CACX,QAAA,MAAM,8BAA+BA,CAAK,EAC9CA,EAAM,OAAS,aACjBC,EAAS,oDAAoD,GACpDD,EAAAA,EAAM,UAANA,MAAAA,EAAe,SAAS,mBACjCC,EAAS,yCAAyC,EAEzCD,EAAAA,EAAM,SAAW,sCAAsC,CAClE,QACA,CACAX,GAAW,EAAK,CAClB,CAAA,EACC,CAAC/B,CAAY,CAAC,EAQjB,GALAyD,EAAAA,UAAU,IAAM,CACFb,GAAA,EACX,CAACA,CAAW,CAAC,EAGZd,GAEA,OAAAlC,EAAA,KAAC,MAAI,CAAA,UAAU,wCACb,SAAA,CAAAE,EAAA,IAAC4D,GAAA,CACC,UAAU,qCACV,cAAY,MAAA,CACd,EACC5D,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAgC,mCAAA,CAC5D,CAAA,CAAA,EAIJ,GAAI,CAAC6B,EAED,OAAA/B,EAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAAAE,EAAA,IAAC6D,GAAA,CACC,UAAU,sCACV,cAAY,MAAA,CACd,EACC7D,EAAA,IAAA,KAAA,CAAG,UAAU,yDAAyD,SAEvE,0BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,oBACV,aAAS,+CACZ,EACAA,EAAA,IAAC,SAAA,CACC,QAAS8C,EACT,UAAU,wKACX,SAAA,kBAAA,CAED,CACF,CAAA,CAAA,EAIJ,MAAMgB,GAAa,CACjB,CAAE,IAAK,SAAU,MAAO,eAAgB,EACxC,CAAE,IAAK,UAAW,MAAO,aAAc,EACvC,CAAE,IAAK,YAAa,MAAO,cAAe,EAC1C,CAAE,IAAK,WAAY,MAAO,cAAe,EACzC,CAAE,IAAK,SAAU,MAAO,aAAc,EACtC,CAAE,IAAK,WAAY,MAAO,QAAS,EACnC,CAAE,IAAK,SAAU,MAAO,SAAU,CAAA,EAG9BC,GAAO,CACX,CACE,GAAI,QACJ,MAAO,sBACP,WAAOC,EAAU,EAAA,CACnB,EACA,CACE,GAAI,QACJ,MAAO,2BACP,WAAOC,GAAM,EAAA,CACf,EACA,CAAE,GAAI,WAAuB,MAAO,gBAAiB,KAAMjE,EAAA,IAACkE,IAAS,CAAG,EACxE,CAAE,GAAI,aAAyB,MAAO,YAAa,KAAMlE,EAAA,IAACmE,IAAU,CAAG,EACvE,CACE,GAAI,eACJ,MAAO,cACP,WAAOC,GAAa,EAAA,CACtB,CAAA,EAGIC,GAAa,SAAY,SAC7B,GAAK7B,EAED,GAAA,CACFD,GAAU,EAAI,EACN,QAAA,IAAI,qCAAsCrC,CAAY,EAE9D,MAAM8C,EAAMC,GACV,GAAGC,GAAW,UAAU,WAAW,IAAIhD,CAAY,UAAA,EAE7C,QAAA,IAAI,oBAAqB8C,CAAG,EAG9B,MAAA3C,EAAkBqB,EAAqBc,EAAc,QAAQ,EAC7D8B,EAAgB,CACpB,GAAG9B,EACH,SAAUnC,CAAA,EAGJ,QAAA,IAAI,wBAAyBmC,EAAc,QAAQ,EACnD,QAAA,IAAI,uBAAwBnC,CAAe,EAC3C,QAAA,IAAI,sBAAuBiE,CAAa,EAE1C,MAAAjB,EAAW,MAAM,MAAML,EAAK,CAChC,OAAQ,MACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAUsB,CAAa,CAAA,CACnC,EAED,GAAIjB,EAAS,GAAI,CACT,MAAAC,EAAO,MAAMD,EAAS,OACpB,QAAA,IAAI,oBAAqBC,CAAI,EAC1BxB,EAAAwB,EAAK,SAAWd,CAAa,EACxCH,EAAW,EAAK,EAChBkC,EAAM,QAAQ,gCAAgC,CAAA,KACzC,CACG,QAAA,MACN,qBACAlB,EAAS,OACTA,EAAS,UAAA,EAEX,MAAMK,EAAY,MAAML,EAAS,KAAO,EAAA,MAAM,IAAM,EAAE,EAC9C,cAAA,MAAM,oBAAqBK,CAAS,EACtC,IAAI,MAAM,uBAAuBL,EAAS,MAAM,EAAE,CAC1D,QACOT,EAAY,CACX,QAAA,MAAM,4BAA6BA,CAAK,EAGhDd,EAAWU,CAAa,EACxBH,EAAW,EAAK,GAEZO,EAAAA,GAAAA,YAAAA,EAAO,WAAPA,MAAAA,EAAiB,OACb2B,EAAA,MACJ,QAAQ3B,EAAM,SAAS,MAAM,OAC3BA,EAAAA,EAAM,SAAS,OAAfA,YAAAA,EAAqB,UAAW,uBAClC,EAAA,EAGF2B,EAAM,qDAAsD,CAAE,KAAM,IAAM,CAAA,CAC5E,QACA,CACAhC,GAAU,EAAK,CACjB,CAAA,EAGIiC,GAAe,IAAM,CACzB/B,EAAiBZ,CAAO,EACxBQ,EAAW,EAAK,CAAA,EAGZoC,EAAc,CAACC,EAAeC,IAAe,CAC5CnC,GACYC,EAAA,CACf,GAAGD,EACH,CAACkC,CAAK,EAAGC,CAAA,CACV,CAAA,EAGGC,EAAsB,CAACC,EAAaH,EAAeC,IAAe,CACjEnC,GACYC,EAAA,CACf,GAAGD,EACH,cAAe,CACb,GAAGA,EAAc,cACjB,CAACqC,CAAG,EAAG,CACL,GAAGrC,EAAc,cAAcqC,CAAG,EAClC,CAACH,CAAK,EAAGC,CACX,CACF,CAAA,CACD,CAAA,EAIGG,GAAqB,CAAqCJ,EAAUC,IAAe,CACvF,GAAI,CAACnC,EAAe,OACpB,MAAMuC,EAAUvC,EAAc,SAC9B,IAAIwC,EAA2BD,EAC3BL,IAAU,aACZM,EAAO9C,EAAc,CAAE,GAAG6C,EAAS,WAAY,CAAE,GAAGA,EAAQ,WAAa,GAAIJ,CAAc,CAAyB,CAAA,EAC3GD,IAAU,gBACnBM,EAAO9C,EAAc,CAAE,GAAG6C,EAAS,cAAe,CAAE,GAAGA,EAAQ,cAAgB,GAAIJ,CAAc,CAAyB,CAAA,EACjHD,IAAU,QACnBM,EAAO9C,EAAc,CAAE,GAAG6C,EAAS,MAAO,CAAE,GAAGA,EAAQ,MAAQ,GAAIJ,CAAc,CAAyB,CAAA,EAEnGK,EAAA9C,EAAc,CAAE,GAAG6C,EAAS,CAACL,CAAK,EAAGC,EAA6B,EAE3ElC,EAAiB,CAAE,GAAGD,EAAe,SAAUwC,CAAM,CAAA,CAAA,EAGjDC,EAAiB,CAAqCP,EAAUC,IAAe,CACnFG,GAAmBJ,EAAOC,CAAK,CAAA,EAG3BO,EAAmB,CAACR,EAAeC,IAAe,CAClD,CAACnC,GAAiB,CAACA,EAAc,YACpBC,EAAA,CACf,GAAGD,EACH,WAAY,CACV,GAAGA,EAAc,WACjB,CAACkC,CAAK,EAAGC,CACX,CAAA,CACD,CAAA,EAGGQ,EAAqB,CAACT,EAAeC,IAAe,CACpD,CAACnC,GAAiB,CAACA,EAAc,cACpBC,EAAA,CACf,GAAGD,EACH,aAAc,CACZ,GAAGA,EAAc,aACjB,CAACkC,CAAK,EAAGC,CACX,CAAA,CACD,CAAA,EAIGS,GAAkB,IAAA,yBACtBtF,OAAAA,EAAA,KAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,cACH,kBAAgB,YAGhB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCAGb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAgE,EAAA,CAAU,UAAU,0CAA2C,CAAA,EAC/DhE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAsB,yBAAA,CAAA,EAC5F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,0DAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,kBACR,UAAU,kEACX,SAAA,qBAAA,CAED,EACCoC,EACCpC,EAAA,IAAC,QAAA,CACC,GAAG,kBACH,KAAK,OACL,OAAOwC,GAAA,YAAAA,EAAe,OAAQ,GAC9B,SAAW6C,GAAMZ,EAAY,OAAQY,EAAE,OAAO,KAAK,EACnD,UAAU,wLAAA,CAGZ,EAAAvF,EAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAAAE,EAAA,IAACgE,EAAU,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC/DhE,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,0BAAS,KAAK,CAAA,EAC/C,CAAA,EAEJ,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,QACR,UAAU,kEACX,SAAA,OAAA,CAED,EACCoC,EACCpC,EAAA,IAAC,QAAA,CACC,GAAG,QACH,KAAK,QACL,OAAOwC,GAAA,YAAAA,EAAe,QAAS,GAC/B,SAAW6C,GAAMZ,EAAY,QAASY,EAAE,OAAO,KAAK,EACpD,UAAU,wLAAA,CAGZ,EAAAvF,EAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAAAE,EAAA,IAACsF,GAAK,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC1DtF,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,0BAAS,MAAM,CAAA,EAChD,CAAA,EAEJ,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,QACR,UAAU,kEACX,SAAA,UAAA,CAED,EACCoC,EACCpC,EAAA,IAAC,QAAA,CACC,GAAG,QACH,KAAK,MACL,OAAOwC,GAAA,YAAAA,EAAe,QAAS,GAC/B,SAAW6C,GAAMZ,EAAY,QAASY,EAAE,OAAO,KAAK,EACpD,UAAU,wLAAA,CAGZ,EAAAvF,EAAA,KAAC,MAAI,CAAA,UAAU,4DACb,SAAA,CAAAE,EAAA,IAACuF,GAAM,CAAA,UAAU,wBAAwB,cAAY,OAAO,EAC3DvF,EAAA,IAAA,OAAA,CAAK,UAAU,cAAe,0BAAS,MAAM,CAAA,EAChD,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAwF,GAAA,CAAO,UAAU,4CAA6C,CAAA,EAC9DxF,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAW,cAAA,CAAA,EACjF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,uDAAA,QAEC,MAAI,CAAA,UAAU,YAEb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,UACR,UAAU,kEACX,SAAA,mBAAA,CAED,EACCoC,EACCpC,EAAA,IAAC,WAAA,CACC,GAAG,UACH,KAAM,EACN,MACE,OAAOwC,GAAA,YAAAA,EAAe,UAAY,SAC9BA,GAAA,YAAAA,EAAe,QACf,EACGlC,EAAAkC,GAAA,YAAAA,EAAe,UAAf,YAAAlC,EAAuC,QACvCC,EAAAiC,GAAA,YAAAA,EAAe,UAAf,YAAAjC,EAAuC,MACvCC,EAAAgC,GAAA,YAAAA,EAAe,UAAf,YAAAhC,EAAuC,OACvCC,EAAA+B,GAAA,YAAAA,EAAe,UAAf,YAAA/B,EAAuC,SACvCC,EAAA8B,GAAA,YAAAA,EAAe,UAAf,YAAA9B,EAAuC,OAEvC,EAAA,OAAO,OAAO,EACd,KAAK,IAAI,EAElB,SAAW2E,GAAMZ,EAAY,UAAWY,EAAE,OAAO,KAAK,EACtD,UAAU,oMAAA,CAGZ,EAAAvF,EAAA,KAAC,MAAI,CAAA,UAAU,2DACb,SAAA,CAAAE,EAAA,IAACwF,GAAO,CAAA,UAAU,+BAA+B,cAAY,OAAO,EACpExF,EAAAA,IAAC,QAAK,UAAU,cACb,gBAAO6B,GAAA,YAAAA,EAAS,UAAY,SACzBA,GAAA,YAAAA,EAAS,QACT,EACGlB,EAAAkB,GAAA,YAAAA,EAAS,UAAT,YAAAlB,EAAiC,QACjCC,EAAAiB,GAAA,YAAAA,EAAS,UAAT,YAAAjB,EAAiC,MACjCC,EAAAgB,GAAA,YAAAA,EAAS,UAAT,YAAAhB,EAAiC,OACjCC,EAAAe,GAAA,YAAAA,EAAS,UAAT,YAAAf,EAAiC,SACjCE,EAAAa,GAAA,YAAAA,EAAS,UAAT,YAAAb,EAAiC,SAEjC,OAAO,OAAO,EACd,KAAK,IAAI,CAClB,CAAA,CAAA,EACF,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,CAAA,EACF,EAGAlB,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAmE,EAAA,CAAU,UAAU,8CAA+C,CAAA,EACnEnE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAS,YAAA,CAAA,EAC/E,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,sDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,cACR,UAAU,kEACX,SAAA,0BAAA,CAED,EACCoC,EACCpC,EAAA,IAAC,WAAA,CACC,GAAG,cACH,OAAOwC,GAAA,YAAAA,EAAe,cAAe,GACrC,SAAW6C,GAAMZ,EAAY,cAAeY,EAAE,OAAO,KAAK,EAC1D,KAAM,EACN,YAAY,wDACZ,UAAU,oMAAA,CAAA,EAGZrF,EAAAA,IAAC,MAAI,CAAA,UAAU,gCACb,SAAAA,MAAC,OAAK,CAAA,UAAU,cAAe,UAAA6B,GAAA,YAAAA,EAAS,cAAe,6BAA8B,CAAA,EACvF,CAAA,EAEJ,CAAA,EACF,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAmE,EAAA,CAAU,UAAU,8CAA+C,CAAA,EACnEnE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAsB,yBAAA,CAAA,EAC5F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,sBAAA,EACCoC,EACCtC,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAE,EAAA,IAAC,MAAI,CAAA,UAAU,sKACZ,SAAAwC,GAAA,MAAAA,EAAe,QACdxC,EAAA,IAAC,MAAA,CACC,IAAKwC,EAAc,QACnB,IAAI,sBACJ,UAAU,sCAAA,CAGZ,EAAA1C,EAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAAAE,EAAA,IAACmE,EAAA,CACC,UAAU,qCACV,cAAY,MAAA,CACd,EACCnE,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,sBAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,wBACZ,OAAOwC,GAAA,YAAAA,EAAe,UAAW,GACjC,SAAW6C,GAAMZ,EAAY,UAAWY,EAAE,OAAO,KAAK,EACtD,UAAU,gMAAA,CACZ,CAAA,EACF,EAECrF,MAAA,MAAA,CAAI,UAAU,sJACZ,oBAAS,QACRA,EAAA,IAAC,MAAA,CACC,IAAK6B,EAAQ,QACb,IAAI,sBACJ,UAAU,sCAAA,CAAA,EAGZ7B,EAAA,IAACgE,EAAA,CACC,UAAU,0BACV,cAAY,MAAA,CAAA,EAGlB,CAAA,EAEJ,EAGAlE,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,wBAAA,EACCoC,EACCtC,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAAAE,EAAA,IAAC,MAAI,CAAA,UAAU,gKACZ,SAAAwC,GAAA,MAAAA,EAAe,UACdxC,EAAA,IAAC,MAAA,CACC,IAAKwC,EAAc,UACnB,IAAI,wBACJ,UAAU,kDAAA,CAGZ,EAAA1C,EAAA,KAAC,MAAI,CAAA,UAAU,kBACb,SAAA,CAAAE,EAAA,IAACmE,EAAA,CACC,UAAU,qCACV,cAAY,MAAA,CACd,EACCnE,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAErC,wBAAA,CAAA,CAAA,CACF,CAEJ,CAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,0BACZ,OAAOwC,GAAA,YAAAA,EAAe,YAAa,GACnC,SAAW6C,GAAMZ,EAAY,YAAaY,EAAE,OAAO,KAAK,EACxD,UAAU,gMAAA,CACZ,CAAA,EACF,EAECrF,MAAA,MAAA,CAAI,UAAU,gJACZ,oBAAS,UACRA,EAAA,IAAC,MAAA,CACC,IAAK6B,EAAQ,UACb,IAAI,wBACJ,UAAU,kDAAA,CAAA,EAGZ7B,EAAA,IAACgE,EAAA,CACC,UAAU,0BACV,cAAY,MAAA,CAAA,EAGlB,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,CAAA,CAAA,CAAA,GAIEyB,GAAsB,IAC1BzF,EAAA,IAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,cACH,kBAAgB,YAGhB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAiE,GAAA,CAAM,UAAU,0CAA2C,CAAA,EAC3DjE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAwB,2BAAA,CAAA,EAC9F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iEAAA,QAEC,MAAI,CAAA,UAAU,YACZ,SAAW8D,GAAA,IAAKe,sBACf/E,OAAAA,EAAA,KAAC,MAAA,CAEC,UAAU,6IAEV,SAAA,CAACE,EAAA,IAAA,MAAA,CAAI,UAAU,iBACb,SAAAA,EAAAA,IAAC,QAAK,UAAU,uDACb,SAAI6E,EAAA,KAAA,CACP,CACF,CAAA,EAECzC,EAEGtC,EAAA,KAAA4F,WAAA,CAAA,SAAA,CAAC5F,EAAAA,KAAA,QAAA,CAAM,UAAU,8BACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UACEM,EAAAkC,GAAA,YAAAA,EAAe,cAAcqC,EAAI,OAAjC,YAAAvE,EAAuC,SAAU,GAEnD,SAAW+E,GACTT,EAAoBC,EAAI,IAAK,SAAUQ,EAAE,OAAO,OAAO,EAEzD,UAAU,2CAAA,CACZ,EACCrF,EAAA,IAAA,OAAA,CAAK,UAAU,UAAU,SAAM,SAAA,CAAA,EAClC,IAECO,EAAAiC,GAAA,YAAAA,EAAe,cAAcqC,EAAI,OAAjC,YAAAtE,EAAuC,SACtCT,EAAA,KAAC,MAAI,CAAA,UAAU,mDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,UAAU,kBACV,QAAS,QAAQ6E,EAAI,GAAG,GACzB,SAAA,qBAAA,CAED,EACA7E,EAAA,IAAC,QAAA,CACC,GAAI,QAAQ6E,EAAI,GAAG,GACnB,KAAK,OACL,QAAOrE,EAAAgC,EAAc,cAAcqC,EAAI,GAAG,IAAnC,YAAArE,EAAsC,OAAQ,GACrD,SAAW6E,GACTT,EAAoBC,EAAI,IAAK,OAAQQ,EAAE,OAAO,KAAK,EAErD,UAAU,8KAAA,CACZ,EACCrF,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAE,KAAA,EAClCA,EAAA,IAAC,QAAA,CACC,UAAU,kBACV,QAAS,SAAS6E,EAAI,GAAG,GAC1B,SAAA,uBAAA,CAED,EACA7E,EAAA,IAAC,QAAA,CACC,GAAI,SAAS6E,EAAI,GAAG,GACpB,KAAK,OACL,QACEpE,EAAA+B,EAAc,cAAcqC,EAAI,GAAG,IAAnC,YAAApE,EAAsC,QAAS,GAEjD,SAAW4E,GACTT,EAAoBC,EAAI,IAAK,QAASQ,EAAE,OAAO,KAAK,EAEtD,UAAU,8KAAA,CACZ,CAAA,EACF,CAAA,CAEJ,CAAA,EAECrF,EAAAA,IAAA,MAAA,CAAI,UAAU,8BACZ,UAASU,EAAAmB,GAAA,YAAAA,EAAA,cAAcgD,EAAI,OAAlB,MAAAnE,EAAwB,OAE9BZ,EAAAA,KAAA4F,EAAAA,SAAA,CAAA,SAAA,CAAA1F,EAAA,IAAC2F,GAAA,CACC,UAAU,yBACV,cAAY,MAAA,CACd,EACA7F,EAAAA,KAAC,OAAK,CAAA,UAAU,2CACb,SAAA,EAAQa,EAAAkB,EAAA,cAAcgD,EAAI,GAAG,IAArB,YAAAlE,EAAwB,KAAK,MAAI,KACzCC,EAAAiB,EAAQ,cAAcgD,EAAI,GAAG,IAA7B,YAAAjE,EAAgC,KAAA,EACnC,CAAA,CAAA,CACF,EAGEd,EAAAA,KAAA4F,EAAA,SAAA,CAAA,SAAA,CAAA1F,EAAA,IAAC4F,GAAE,CAAA,UAAU,uBAAuB,cAAY,OAAO,EACtD5F,EAAA,IAAA,OAAA,CAAK,UAAU,wBAAwB,SAAO,UAAA,CAAA,CAAA,CACjD,CAEJ,CAAA,CAAA,CAAA,EAlFG6E,EAAI,GAqFZ,EAAA,EACH,CAAA,EACF,CAAA,CAAA,EAIEgB,GAAiB,IAAA,+CACrB/F,OAAAA,EAAA,KAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,iBACH,kBAAgB,eAGhB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCAGb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAkE,EAAA,CAAS,UAAU,0CAA2C,CAAA,EAC9DlE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAqB,wBAAA,CAAA,EAC3F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,4DAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,kCAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,6CAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,SAASwC,GAAA,YAAAA,EAAe,SAAS,mBAAoB,GACrD,SAAW6C,GAAMJ,EAAe,mBAAoBI,EAAE,OAAO,OAAO,EACpE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,8CACd6B,GAAA,MAAAA,EAAS,SAAS,iBACd,uEACA,8DACN,GACG,SAAAA,GAAA,MAAAA,EAAS,SAAS,iBAAmB,QAAU,UAClD,CAAA,EAEJ,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,wCAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,SAASwC,GAAA,YAAAA,EAAe,SAAS,kBAAmB,GACpD,SAAW6C,GAAMJ,EAAe,kBAAmBI,EAAE,OAAO,OAAO,EACnE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,8CACd6B,GAAA,MAAAA,EAAS,SAAS,gBACd,uEACA,8DACN,GACG,SAAAA,GAAA,MAAAA,EAAS,SAAS,gBAAkB,QAAU,UACjD,CAAA,EAEJ,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,2CAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,4DAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,SAASwC,GAAA,YAAAA,EAAe,SAAS,mBAAoB,GACrD,SAAW6C,GAAMJ,EAAe,mBAAoBI,EAAE,OAAO,OAAO,EACpE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,8CACd6B,GAAA,MAAAA,EAAS,SAAS,iBACd,uEACA,8DACN,GACG,SAAAA,GAAA,MAAAA,EAAS,SAAS,iBAAmB,QAAU,UAClD,CAAA,EAEJ,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,kCAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,6CAAA,CAAA,EACF,EACCoC,EACCpC,EAAA,IAAC,QAAA,CACC,KAAK,SACL,IAAI,IACJ,IAAI,KACJ,OAAOwC,GAAA,YAAAA,EAAe,SAAS,wBAAyB,EACxD,SAAW6C,GAAMJ,EAAe,wBAAyB,SAASI,EAAE,OAAO,KAAK,CAAC,EACjF,UAAU,kMAAA,CAAA,EAGXrF,EAAA,IAAA,MAAA,CAAI,UAAU,8GACZ,SAAA6B,GAAA,YAAAA,EAAS,SAAS,sBACrB,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAA8F,GAAA,CAAI,UAAU,4CAA6C,CAAA,EAC3D9F,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAoB,uBAAA,CAAA,EAC1F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,kDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,mCAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,EAACwC,GAAA,MAAAA,EAAe,SAAS,aACnC,SAAW6C,GAAMJ,EAAe,cAAeI,EAAE,OAAO,OAAO,EAC/D,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,8CACd6B,GAAA,MAAAA,EAAS,SAAS,YACd,uEACA,8DACN,GACG,SAAAA,GAAA,MAAAA,EAAS,SAAS,YAAc,QAAU,UAC7C,CAAA,EAEJ,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,eAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,0CAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,EAACwC,GAAA,MAAAA,EAAe,SAAS,WACnC,SAAW6C,GAAMJ,EAAe,YAAaI,EAAE,OAAO,OAAO,EAC7D,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,8CACd6B,GAAA,MAAAA,EAAS,SAAS,UACd,uEACA,8DACN,GACG,SAAAA,GAAA,MAAAA,EAAS,SAAS,UAAY,QAAU,UAC3C,CAAA,EAEJ,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,4BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,uCAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,EAACwC,GAAA,MAAAA,EAAe,SAAS,gBACnC,SAAW6C,GAAMJ,EAAe,iBAAkBI,EAAE,OAAO,OAAO,EAClE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,8CACd6B,GAAA,MAAAA,EAAS,SAAS,eACd,uEACA,8DACN,GACG,SAAAA,GAAA,MAAAA,EAAS,SAAS,eAAiB,QAAU,UAChD,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGA/B,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAGb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAA+F,GAAA,CAAO,UAAU,8CAA+C,CAAA,EAChE/F,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAS,YAAA,CAAA,EAC/E,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,uBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,4CAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,GAACM,EAAAkC,GAAA,YAAAA,EAAe,SAAS,aAAxB,MAAAlC,EAAoC,aAC/C,SAAW+E,GAAMJ,EAAe,aAAc,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,WAAY,YAAa6C,EAAE,OAAO,QAAS,EACtH,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAECA,MAAA,MAAA,CAAI,UAAW,+CACdO,EAAAsB,GAAA,YAAAA,EAAS,SAAS,aAAlB,MAAAtB,EAA8B,YAC1B,uEACA,8DACN,GACG,UAAAC,EAAAqB,GAAA,YAAAA,EAAS,SAAS,aAAlB,MAAArB,EAA8B,YAAc,QAAU,UACzD,CAAA,EAEJ,EAGAV,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,yCAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,GAACS,EAAA+B,GAAA,YAAAA,EAAe,SAAS,aAAxB,MAAA/B,EAAoC,mBAC/C,SAAW4E,GAAMJ,EAAe,aAAc,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,WAAY,kBAAmB6C,EAAE,OAAO,QAAS,EAC5H,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAECA,MAAA,MAAA,CAAI,UAAW,+CACdU,EAAAmB,GAAA,YAAAA,EAAS,SAAS,aAAlB,MAAAnB,EAA8B,kBAC1B,uEACA,8DACN,GACG,UAAAC,EAAAkB,GAAA,YAAAA,EAAS,SAAS,aAAlB,MAAAlB,EAA8B,kBAAoB,QAAU,UAC/D,CAAA,EAEJ,EAGCyB,GACCtC,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,+BAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,QAAOY,EAAA4B,GAAA,YAAAA,EAAe,SAAS,aAAxB,YAAA5B,EAAoC,0BAA2B,GACtE,SAAWyE,GAAMJ,EAAe,aAAc,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,WAAY,wBAAyB,SAAS6C,EAAE,OAAO,OAAS,IAAK,EAAE,EAAG,EACrJ,UAAU,gMAAA,CACZ,CAAA,EACF,SACC,MACC,CAAA,SAAA,CAACrF,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAEnF,gCAAA,EACAA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,QAAOa,EAAA2B,GAAA,YAAAA,EAAe,SAAS,aAAxB,YAAA3B,EAAoC,2BAA4B,GACvE,SAAWwE,GAAMJ,EAAe,aAAc,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,WAAY,yBAA0B,SAAS6C,EAAE,OAAO,OAAS,IAAK,EAAE,EAAG,EACtJ,UAAU,gMAAA,CACZ,CAAA,EACF,CAAA,EACF,EAIFvF,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,yDAAA,CAAA,EACF,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,2CACZ,SAAA,GAAekB,GAAAF,EAAA0B,GAAA,YAAAA,EAAA,SAAS,aAAT,YAAA1B,EAAqB,cAArB,YAAAE,EAAkC,SAAU,EAAE,WAAA,EAChE,CAAA,EACF,EAECoB,EACCtC,EAAA,KAAC,MAAI,CAAA,UAAU,YACb,SAAA,CAACE,EAAAA,IAAA,MAAA,CAAI,UAAU,aACb,SAAAA,EAAA,IAAC,QAAA,CACC,KAAK,OACL,YAAY,8BACZ,UAAU,iMACV,WAAaqF,GAAM,WACb,GAAAA,EAAE,MAAQ,QAAS,CACrB,MAAMW,EAAQX,EAAE,OACVY,EAAOD,EAAM,MAAM,OAAO,YAAY,EACxCC,GAAQ,GAAC1F,GAAAD,EAAAkC,GAAA,YAAAA,EAAe,SAAS,aAAxB,YAAAlC,EAAoC,cAApC,MAAAC,EAAiD,SAAS0F,MACrEhB,EAAe,aAAc,CAC3B,GAAGzC,GAAA,YAAAA,EAAe,SAAS,WAC3B,YAAa,CAAC,KAAIhC,EAAAgC,GAAA,YAAAA,EAAe,SAAS,aAAxB,YAAAhC,EAAoC,cAAe,CAAC,EAAIyF,CAAI,CAAA,CAC/E,EACDD,EAAM,MAAQ,GAElB,CACF,CAAA,CAAA,EAEJ,EACAhG,EAAAA,IAAC,MAAI,CAAA,UAAU,uBACZ,UAAAkB,GAAAH,EAAAyB,GAAA,YAAAA,EAAe,SAAS,aAAxB,YAAAzB,EAAoC,cAApC,YAAAG,EAAiD,IAAI,CAAC+E,EAAMC,IAC3DpG,EAAA,KAAC,OAAA,CAEC,UAAU,wHAET,SAAA,CAAAmG,EACDjG,EAAA,IAAC,SAAA,CACC,KAAK,SACL,QAAS,IAAM,SACb,MAAMmG,IAAW5F,GAAAD,EAAAkC,GAAA,YAAAA,EAAe,SAAS,aAAxB,YAAAlC,EAAoC,cAApC,YAAAC,EAAiD,OAAO,CAAC6F,EAAGC,IAAMA,IAAMH,KAAU,CAAA,EACnGjB,EAAe,aAAc,CAC3B,GAAGzC,GAAA,YAAAA,EAAe,SAAS,WAC3B,YAAa2D,CAAA,CACd,CACH,EACA,UAAU,kDACX,SAAA,GAAA,CAED,CAAA,CAAA,EAhBKD,CAkBR,GACH,CAAA,EACF,EAEClG,EAAA,IAAA,MAAA,CAAI,UAAU,uBACZ,gCAAS,SAAS,2BAAY,sBAAa,OAC1C6B,EAAQ,SAAS,WAAW,YAAY,IAAI,CAACoE,EAAMC,IACjDlG,EAAA,IAAC,OAAA,CAEC,UAAU,kHAET,SAAAiG,CAAA,EAHIC,CAAA,CAKR,EAEDlG,EAAAA,IAAC,QAAK,UAAU,2CAA2C,iCAAsB,CAAA,EAErF,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAsG,GAAA,CAAK,UAAU,8CAA+C,CAAA,EAC9DtG,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAoB,uBAAA,CAAA,EAC1F,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,gDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,8DAA8D,SAAY,eAAA,EACxFA,EAAAA,IAAC,MAAI,CAAA,UAAU,yBACZ,SAAA,CACC,CAAE,IAAK,qBAAsB,MAAO,QAAS,KAAM,gCAAiC,EACpF,CAAE,IAAK,qBAAsB,MAAO,kBAAmB,KAAM,4BAA6B,EAC1F,CAAE,IAAK,gBAAiB,MAAO,cAAe,KAAM,qCAAsC,EAC1F,CAAE,IAAK,kBAAmB,MAAO,YAAa,KAAM,iCAAkC,CACxF,EAAE,IAAI,CAAC,CAAE,IAAAuG,EAAK,MAAA7G,EAAO,KAAA8G,CAAA,eACnB1G,OAAAA,EAAA,KAAC,MAAc,CAAA,UAAU,kFACvB,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAwD,SAAMN,EAAA,EAC9EM,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA4C,SAAKwG,EAAA,CAAA,EAChE,EACCpE,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,GAACM,EAAAkC,GAAA,YAAAA,EAAe,SAAS,gBAAxB,MAAAlC,EAAwCiG,IACnD,SAAWlB,GAAMJ,EAAe,gBAAiB,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,cAAe,CAAC+D,CAAG,EAAGlB,EAAE,OAAO,QAAS,EACtH,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAECA,MAAA,MAAA,CAAI,UAAW,0CACdO,EAAAsB,GAAA,YAAAA,EAAS,SAAS,gBAAlB,MAAAtB,EAAkCgG,GAC9B,uEACA,+DACN,GACG,UAAS/F,EAAAqB,GAAA,YAAAA,EAAA,SAAS,gBAAT,MAAArB,EAAyB+F,GAAsD,KAAO,MAClG,CAAA,GAtBMA,CAwBV,EACD,EACH,CAAA,EACF,SAGC,MACC,CAAA,SAAA,CAACvG,EAAA,IAAA,KAAA,CAAG,UAAU,8DAA8D,SAAsB,yBAAA,EAClGF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,yCACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAAM,SAAA,EAC9EF,EAAAA,KAAC,OAAK,CAAA,UAAU,2CAA4C,SAAA,GAAeqB,EAAAqB,GAAA,YAAAA,EAAA,SAAS,QAAT,YAAArB,EAAgB,SAAU,GAAG,GAAA,EAAC,CAAA,EAC3G,EACCiB,GACCpC,EAAA,IAAC,QAAA,CACC,KAAK,QACL,IAAK,EACL,IAAK,IACL,QAAOqB,EAAAmB,GAAA,YAAAA,EAAe,SAAS,QAAxB,YAAAnB,EAA+B,SAAU,GAChD,SAAWgE,GAAMJ,EAAe,QAAS,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,MAAO,OAAQ,SAAS6C,EAAE,OAAO,MAAO,EAAE,EAAG,EACnH,UAAU,mFAAA,CACZ,CAAA,EAEJ,EAGCjD,GACCtC,EAAA,KAAC,MAAI,CAAA,UAAU,yBACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAAW,cAAA,EAC9FA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,QAAOsB,EAAAkB,GAAA,YAAAA,EAAe,SAAS,QAAxB,YAAAlB,EAA+B,iBAAkB,EACxD,SAAW+D,GAAMJ,EAAe,QAAS,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,MAAO,eAAgB,SAAS6C,EAAE,OAAO,OAAS,IAAK,EAAE,EAAG,EAClI,UAAU,sIAAA,CACZ,CAAA,EACF,SACC,MACC,CAAA,SAAA,CAACrF,EAAA,IAAA,QAAA,CAAM,UAAU,kEAAkE,SAAY,eAAA,EAC/FA,EAAA,IAAC,QAAA,CACC,KAAK,SACL,QAAOuB,EAAAiB,GAAA,YAAAA,EAAe,SAAS,QAAxB,YAAAjB,EAA+B,kBAAmB,EACzD,SAAW8D,GAAMJ,EAAe,QAAS,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,MAAO,gBAAiB,SAAS6C,EAAE,OAAO,OAAS,IAAK,EAAE,EAAG,EACnI,UAAU,sIAAA,CACZ,CAAA,EACF,CAAA,EACF,EAIFvF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAAS,YAAA,EAChFA,EAAA,IAAA,IAAA,CAAE,UAAU,2CAA2C,SAA6B,gCAAA,CAAA,EACvF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,QAAS,CAAC,GAACwB,EAAAgB,GAAA,YAAAA,EAAe,SAAS,QAAxB,MAAAhB,EAA+B,WAC1C,SAAW6D,GAAMJ,EAAe,QAAS,CAAE,GAAGzC,GAAA,YAAAA,EAAe,SAAS,MAAO,UAAW6C,EAAE,OAAO,QAAS,EAC1G,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAECA,MAAA,MAAA,CAAI,UAAW,+CACdyB,EAAAI,GAAA,YAAAA,EAAS,SAAS,QAAlB,MAAAJ,EAAyB,UACrB,uEACA,8DACN,GACG,UAAAgF,EAAA5E,GAAA,YAAAA,EAAS,SAAS,QAAlB,MAAA4E,EAAyB,UAAY,QAAU,UAClD,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGA3G,EAAAA,KAAC,MAAI,CAAA,UAAU,wCAGb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAkE,EAAA,CAAS,UAAU,8CAA+C,CAAA,EAClElE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAiB,oBAAA,CAAA,EACvF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,uDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,yBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,mCAAA,CAAA,EACF,EACCoC,EACCpC,EAAA,IAAC,QAAA,CACC,KAAK,SACL,IAAI,KACJ,IAAI,MACJ,MAAO,GACP,SAAQ,GACR,UAAU,yJAAA,CAAA,EAGZA,EAAAA,IAAC,MAAI,CAAA,UAAU,8GAA8G,SAE7H,KAAA,CAAA,EAEJ,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,sBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,kDAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,0GAA0G,SAEzH,eAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,iBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,wCAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,0GAA0G,SAEzH,eAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,iBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,0BAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,2GAA2G,SAE1H,SAAA,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAA8F,GAAA,CAAI,UAAU,0CAA2C,CAAA,EACzD9F,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAkB,qBAAA,CAAA,EACxF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,kDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,0BAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,kHAAkH,SAEjI,QAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,qBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,kDAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,kHAAkH,SAEjI,QAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,sBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,qCAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,kHAAkH,SAEjI,QAAA,CAAA,EACF,EAGAF,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,oBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,4BAAA,CAAA,EACF,EACCA,EAAA,IAAA,MAAA,CAAI,UAAU,8GAA8G,SAE7H,QAAA,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,EACF,CAAA,CAAA,CAAA,GAIE0G,GAAmB,IAAA,2CACvB5G,OAAAA,EAAA,KAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,mBACH,kBAAgB,iBAGhB,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,wCAGb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAmE,EAAA,CAAU,UAAU,8CAA+C,CAAA,EACnEnE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAiB,oBAAA,CAAA,EACvF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,wDAAA,QAEC,MAAI,CAAA,UAAU,YAEb,SAACF,EAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,cAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAASM,EAAAkC,GAAA,YAAAA,EAAe,aAAf,YAAAlC,EAA2B,WAAY,GAChD,SAAW+E,GAAMH,EAAiB,WAAYG,EAAE,OAAO,OAAO,EAC9D,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,+CACdO,EAAAsB,GAAA,YAAAA,EAAS,aAAT,MAAAtB,EAAqB,SACjB,2EACA,sEACN,GACG,UAAAC,EAAAqB,GAAA,YAAAA,EAAS,aAAT,MAAArB,EAAqB,SAAW,SAAW,QAC9C,CAAA,CAAA,CAEJ,CACF,CAAA,CAAA,EACF,EAGAV,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAkE,EAAA,CAAS,UAAU,8CAA+C,CAAA,EAClElE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAa,gBAAA,CAAA,EACnF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,8CAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,gBACR,UAAU,kEACX,SAAA,cAAA,CAED,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CACCsC,EAAApC,EAAA,IAAC,QAAA,CACC,GAAG,gBACH,KAAK,QACL,QAAOS,EAAA+B,GAAA,YAAAA,EAAe,aAAf,YAAA/B,EAA2B,eAAgB,UAClD,SAAW4E,GAAMH,EAAiB,eAAgBG,EAAE,OAAO,KAAK,EAChE,UAAU,+CAAA,CAAA,EAGZrF,EAAA,IAAC,MAAA,CACC,UAAU,6EACV,MAAO,CACL,kBAAiBU,EAAAmB,GAAA,YAAAA,EAAS,aAAT,YAAAnB,EAAqB,eAAgB,SACxD,CAAA,CACD,EAEFV,EAAA,IAAA,OAAA,CAAK,UAAU,qDACb,SAAUoC,IAAAzB,EAAA6B,GAAA,YAAAA,EAAe,aAAf,YAAA7B,EAA2B,eAAgB,YAAYC,EAAAiB,GAAA,YAAAA,EAAS,aAAT,YAAAjB,EAAqB,eAAgB,UACzG,CAAA,EACF,CAAA,EACF,EAGAd,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,eACR,UAAU,kEACX,SAAA,iBAAA,CAED,EACAF,EAAAA,KAAC,MAAI,CAAA,UAAU,8BACZ,SAAA,CACCsC,EAAApC,EAAA,IAAC,QAAA,CACC,GAAG,eACH,KAAK,QACL,QAAOa,EAAA2B,GAAA,YAAAA,EAAe,aAAf,YAAA3B,EAA2B,cAAe,UACjD,SAAWwE,GAAMH,EAAiB,cAAeG,EAAE,OAAO,KAAK,EAC/D,UAAU,+CAAA,CAAA,EAGZrF,EAAA,IAAC,MAAA,CACC,UAAU,6EACV,MAAO,CACL,kBAAiBc,EAAAe,GAAA,YAAAA,EAAS,aAAT,YAAAf,EAAqB,cAAe,SACvD,CAAA,CACD,EAEFd,EAAA,IAAA,OAAA,CAAK,UAAU,qDACf,SAAUoC,IAAApB,EAAAwB,GAAA,YAAAA,EAAe,aAAf,YAAAxB,EAA2B,cAAe,YAAYD,EAAAc,GAAA,YAAAA,EAAS,aAAT,YAAAd,EAAqB,cAAe,UACrG,CAAA,EACF,CAAA,EACF,EAGAjB,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,QAAQ,cACR,UAAU,kEACX,SAAA,kBAAA,CAED,EACCoC,EACCtC,EAAA,KAAC,SAAA,CACC,GAAG,cACH,QAAOoB,EAAAsB,GAAA,YAAAA,EAAe,aAAf,YAAAtB,EAA2B,aAAc,QAChD,SAAWmE,GAAMH,EAAiB,aAAcG,EAAE,OAAO,KAAK,EAC9D,UAAU,yLAEV,SAAA,CAACrF,EAAA,IAAA,SAAA,CAAO,MAAM,QAAQ,SAAK,QAAA,EAC1BA,EAAA,IAAA,SAAA,CAAO,MAAM,SAAS,SAAM,SAAA,EAC5BA,EAAA,IAAA,SAAA,CAAO,MAAM,UAAU,SAAO,UAAA,EAC9BA,EAAA,IAAA,SAAA,CAAO,MAAM,aAAa,SAAU,aAAA,EACpCA,EAAA,IAAA,SAAA,CAAO,MAAM,YAAY,SAAS,YAAA,CAAA,CAAA,CAAA,EAGrCA,EAAAA,IAAC,MAAI,CAAA,UAAU,gCACb,SAAAA,EAAA,IAAC,OAAA,CACC,UAAU,cACV,MAAO,CACL,aAAYiB,EAAAY,GAAA,YAAAA,EAAS,aAAT,YAAAZ,EAAqB,aAAc,OACjD,EAEC,WAAAG,EAAAS,GAAA,YAAAA,EAAS,aAAT,YAAAT,EAAqB,aAAc,OAAA,CAAA,EAExC,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,CAAA,EACF,EAGAtB,EAAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAA8F,GAAA,CAAI,UAAU,0CAA2C,CAAA,EACzD9F,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAY,eAAA,CAAA,EAClF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,sDAAA,EAEAA,EAAAA,IAAC,MAAI,CAAA,UAAU,gDACb,SAAAF,EAAA,KAAC,MAAA,CACC,UAAU,gFACV,MAAO,CACL,iBAAiBqB,EAAAqB,GAAA,YAAAA,EAAe,aAAf,MAAArB,EAA2B,SAAW,UAAY,UACnE,OAAOE,EAAAmB,GAAA,YAAAA,EAAe,aAAf,MAAAnB,EAA2B,SAAW,UAAY,UACzD,aAAYC,EAAAkB,GAAA,YAAAA,EAAe,aAAf,YAAAlB,EAA2B,aAAc,OACvD,EAEA,SAAA,CAACxB,EAAAA,KAAA,MAAA,CAAI,UAAU,mBACb,SAAA,CAAAE,EAAA,IAAC,MAAA,CACC,UAAU,oBACV,MAAO,CAAE,QAAOuB,EAAAiB,GAAA,YAAAA,EAAe,aAAf,YAAAjB,EAA2B,eAAgB,SAAU,EACtE,SAAA,eAAA,CAED,EACCvB,EAAA,IAAA,MAAA,CAAI,UAAU,qBAAqB,SAAwB,2BAAA,CAAA,EAC9D,QACC,MAAI,CAAA,UAAU,0CACb,SAACA,MAAA,MAAA,CAAI,UAAU,uDACb,SAAAA,EAAA,IAAC,MAAA,CACC,UAAU,+CACV,MAAO,CACL,MAAO,MACP,kBAAiBwB,EAAAgB,GAAA,YAAAA,EAAe,aAAf,YAAAhB,EAA2B,cAAe,SAC7D,CAAA,GAEJ,CACF,CAAA,EACA1B,EAAAA,KAAC,MAAI,CAAA,UAAU,+CACb,SAAA,CAAAE,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,EACVA,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,EAEJ,CAAA,EACF,CAAA,CAAA,CAAA,GAIE2G,GAAqB,IAAA,uBACzB3G,OAAAA,EAAA,IAAC,MAAA,CACC,UAAU,YACV,KAAK,WACL,GAAG,qBACH,kBAAgB,mBAGhB,SAAAF,EAAA,KAAC,MAAI,CAAA,UAAU,iGACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,mCACb,SAAA,CAACE,EAAAA,IAAAoE,GAAA,CAAa,UAAU,0CAA2C,CAAA,EAClEpE,EAAA,IAAA,KAAA,CAAG,UAAU,sDAAsD,SAAW,cAAA,CAAA,EACjF,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,EAEAF,EAAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,iBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,oDAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAASM,EAAAkC,GAAA,YAAAA,EAAe,eAAf,YAAAlC,EAA6B,oBAAqB,GAC3D,SAAW+E,GAAMF,EAAmB,oBAAqBE,EAAE,OAAO,OAAO,EACzE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,+CACdO,EAAAsB,GAAA,YAAAA,EAAS,eAAT,MAAAtB,EAAuB,kBACnB,uEACA,8DACN,GACG,UAAAC,EAAAqB,GAAA,YAAAA,EAAS,eAAT,MAAArB,EAAuB,kBAAoB,QAAU,UACxD,CAAA,EAEJ,EAGAV,EAAAA,KAAC,MAAI,CAAA,UAAU,gDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,uBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,iDAAA,CAAA,EACF,EACCE,GACCF,EAAA,IAAC4G,GAAA,CACC,aAAA1G,EACA,mBAAqB2G,GAAW,CACtB,QAAA,IAAI,gBAAiBA,CAAM,CACrC,CAAA,CACF,CAAA,EAEJ,EAGA/G,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,UAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,gDAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAASS,EAAA+B,GAAA,YAAAA,EAAe,eAAf,YAAA/B,EAA6B,mBAAoB,GAC1D,SAAW4E,GAAMF,EAAmB,mBAAoBE,EAAE,OAAO,OAAO,EACxE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,+CACdU,EAAAmB,GAAA,YAAAA,EAAS,eAAT,MAAAnB,EAAuB,iBACnB,uEACA,8DACN,GACG,UAAAC,EAAAkB,GAAA,YAAAA,EAAS,eAAT,MAAAlB,EAAuB,iBAAmB,YAAc,eAC3D,CAAA,EAEJ,EAGAb,EAAAA,KAAC,MAAI,CAAA,UAAU,kFACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,QAAA,CAAM,UAAU,uDAAuD,SAExE,mBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,gDAAgD,SAE7D,0CAAA,CAAA,EACF,EACCoC,EACCtC,EAAA,KAAC,QAAM,CAAA,UAAU,mDACf,SAAA,CAAAE,EAAA,IAAC,QAAA,CACC,KAAK,WACL,UAASY,EAAA4B,GAAA,YAAAA,EAAe,eAAf,YAAA5B,EAA6B,kBAAmB,GACzD,SAAWyE,GAAMF,EAAmB,kBAAmBE,EAAE,OAAO,OAAO,EACvE,UAAU,cAAA,CACZ,EACArF,EAAAA,IAAC,MAAI,CAAA,UAAU,6bAA8b,CAAA,CAAA,EAC/c,EAEAA,EAAA,IAAC,MAAI,CAAA,UAAW,+CACda,EAAAgB,GAAA,YAAAA,EAAS,eAAT,MAAAhB,EAAuB,gBACnB,uEACA,8DACN,GACG,UAAAC,EAAAe,GAAA,YAAAA,EAAS,eAAT,MAAAf,EAAuB,gBAAkB,QAAU,UACtD,CAAA,EAEJ,CAAA,EACF,CAAA,EACF,CAAA,CAAA,GAMF,OAAAhB,EAAA,KAAC,MAAI,CAAA,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,8EACb,SAAA,CAAAA,OAAC,MACC,CAAA,SAAA,CAACE,EAAA,IAAA,KAAA,CAAG,UAAU,mDAAmD,SAEjE,wBAAA,EACCA,EAAA,IAAA,IAAA,CAAE,UAAU,wCAAwC,SAErD,6DAAA,CAAA,EACF,EAECA,MAAA,MAAA,CAAI,UAAU,iBACZ,WAEGF,EAAA,KAAA4F,WAAA,CAAA,SAAA,CAAA5F,EAAA,KAAC,SAAA,CACC,QAAS0E,GACT,SAAUlC,EACV,UAAU,wQAEV,SAAA,CAACtC,EAAAA,IAAA4F,GAAA,CAAE,UAAU,qBAAsB,CAAA,EAAE,UAAA,CAAA,CAEvC,EACA9F,EAAA,KAAC,SAAA,CACC,QAASuE,GACT,SAAU/B,EACV,UAAU,8MAET,SAAA,CACCA,EAAAtC,EAAA,IAAC4D,IAAU,UAAU,2BAAA,CAA4B,EAEhD5D,EAAAA,IAAA8G,GAAA,CAAK,UAAU,cAAe,CAAA,EAEhCxE,EAAS,cAAgB,QAAA,CAAA,CAC5B,CAAA,CAAA,CACF,EAEAxC,EAAA,KAAC,SAAA,CACC,QAAS,IAAMuC,EAAW,EAAI,EAC9B,UAAU,0LAEV,SAAA,CAACrC,EAAAA,IAAA+G,GAAA,CAAK,UAAU,cAAe,CAAA,EAAE,QAAA,CAAA,CAAA,EAIvC,CAAA,EACF,EAGC/G,EAAA,IAAA,MAAA,CAAI,UAAU,gDACb,SAACA,EAAA,IAAA,MAAA,CAAI,UAAU,wBAAwB,aAAW,OAC/C,SAAK+D,GAAA,IAAKiD,GACThH,EAAA,IAACR,GAAA,CAEC,KAAMwH,EAAI,KACV,MAAOA,EAAI,MACX,GAAIA,EAAI,GACR,OAAQtE,IAAcsE,EAAI,GAC1B,QAASrE,EAAA,EALJqE,EAAI,EAAA,CAOZ,EACH,CACF,CAAA,QAGC,MAAI,CAAA,UAAU,OACb,SAAChH,MAAAiH,GAAA,CAAgB,KAAK,OACpB,SAAAnH,EAAA,KAACoH,GAAO,IAAP,CAEC,QAAS,CAAE,QAAS,EAAG,EAAG,EAAG,EAC7B,QAAS,CAAE,QAAS,EAAG,EAAG,CAAE,EAC5B,KAAM,CAAE,QAAS,EAAG,EAAG,GAAI,EAC3B,WAAY,CAAE,SAAU,EAAI,EAE3B,SAAA,CAAAxE,IAAc,SAAW0C,GAAgB,EACzC1C,IAAc,SAAW+C,GAAoB,EAC7C/C,IAAc,YAAcmD,GAAe,EAC3CnD,IAAc,cAAgBgE,GAAiB,EAC/ChE,IAAc,gBAAkBiE,GAAmB,CAAA,CAAA,EAV/CjE,GAYT,CACF,CAAA,CACF,CAAA,CAAA,CAEJ"}