import "reflect-metadata";
import express from "express";
import cors from "cors";
import helmet from "helmet";
import morgan from "morgan";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { createServer } from "http";
import { Server as SocketIOServer } from "socket.io";
import { config } from "dotenv";
import { logger } from "./utils/logger";
import { AppDataSource, initializeDatabase } from "./config/database";
import { redisClient } from "./config/redis";
import { snapshotService } from "./services/SnapshotService";
import QRCode from "qrcode";
import { v4 as uuidv4 } from "uuid";

// Carregar variáveis de ambiente
config();

// Importar rotas modulares
import genresRoutes from "./routes/genres";
import restaurantsRoutes from "./routes/restaurants";
import suggestionsRoutes from "./routes/suggestions";
import playlistsRoutes from "./routes/playlists";
import analyticsRoutes from "./routes/analytics";
import authRoutes from "./routes/auth";
import clientRoutes from "./routes/client";
import notificationsRoutes from "./routes/notifications";
import paymentsRoutes from "./routes/payments";
import playbackRoutes from "./routes/playback";
import playbackQueueRoutes from "./routes/playbackQueue";
import rewardsRoutes from "./routes/rewards";
import youtubeRoutes from "./routes/youtube";
import livePlaylistRoutes from "./routes/livePlaylist";
import collaborativePlaylistRoutes from "./routes/collaborativePlaylist";
import testYoutubeRoutes from "./routes/test-youtube";
import youtubeAuthRoutes from "./routes/youtubeAuth";
import qrcodeRoutes from "./routes/qrcode";
import businessHoursRoutes from "./routes/businessHours";
import competitiveVotingRoutes from "./routes/competitiveVoting";
import lyricsRoutes from "./routes/lyrics";
import schedulesRoutes from "./routes/schedules";
import playlistSchedulesRoutes from "./routes/playlistSchedules";
import playlistReorderRoutes from "./routes/playlistReorder";

// Importar modelos
import { Restaurant } from "./models/Restaurant";
import { User } from "./models/User";
import { Suggestion, SuggestionStatus } from "./models/Suggestion";
import { Vote, VoteType } from "./models/Vote";
import { Playlist } from "./models/Playlist";
import { ClientSession } from "./models/ClientSession";
import { PlayHistory } from "./models/PlayHistory";
import { AnalyticsDaily } from "./models/AnalyticsDaily";
import { CompetitiveVote } from "./models/CompetitiveVote";
import { Payment } from "./models/Payment";
import { QRCode as QRCodeModel, QRCodeType } from "./models/QRCode";

// Importar serviços
import { YouTubeService } from "./services/YouTubeService";
import NotificationService from "./services/NotificationService";
import { PaymentService } from "./services/PaymentService";
import { PlaybackService } from "./services/PlaybackService";
import RewardService from "./services/RewardService";
import { WebSocketService } from "./services/WebSocketService";
import { errorHandler, notFoundHandler } from "./middleware/errorHandler";

// Configurar Express
const app = express();
const server = createServer(app);

// Configurar CORS
const corsOptions = {
  origin: [
    "http://localhost:3000",
    "http://localhost:8000",
    "http://localhost:5173",
    "https://restaurant-playlist.vercel.app",
    "https://restaurant-playlist-frontend.vercel.app",
  ],
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
    "X-Tenant-ID",
    "X-Session-Token",
    "X-Session-ID",
    "X-Device-Info",
    "Cache-Control",
    "Pragma",
  ],
};

app.use(cors(corsOptions));

// Middleware de segurança
app.use(
  helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false,
  })
);

// Compression
app.use(compression());

// Logging
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Rate limiting - configuração mais permissiva para desenvolvimento
const limiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: 100, // máximo 100 requests por IP por minuto
  message: {
    error: "Muitas requisições deste IP, tente novamente em 1 minuto.",
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Ignorar rate limit para desenvolvimento local
  skip: (req) => {
    const isDevelopment =
      process.env.NODE_ENV === "development" ||
      req.ip === "127.0.0.1" ||
      req.ip === "::1";
    return isDevelopment;
  },
});
app.use(limiter);

// Middleware básico
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Middleware de tenant
const tenantMiddleware = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  const tenantId = req.headers["x-tenant-id"] || req.query.restaurantId;
  if (tenantId) {
    (req as any).tenantId = tenantId;
  }
  next();
};
app.use(tenantMiddleware);

// Logging personalizado
app.use((req, res, next) => {
  const start = Date.now();
  res.on("finish", () => {
    const duration = Date.now() - start;
    logger.info(
      `${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`
    );
  });
  next();
});

// ==================== PRODUCTION DATA ONLY ====================
// Sistema configurado para usar apenas dados reais do banco de dados

// Health check
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    database: AppDataSource.isInitialized ? "connected" : "disconnected",
    redis: redisClient.isReady ? "connected" : "disconnected",
  });
});

// Rota raiz
app.get("/", (req, res) => {
  res.json({
    message: "Restaurant Playlist API está funcionando!",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    endpoints: {
      health: "/health",
      api: "/api/v1",
      docs: "/api/docs",
    },
  });
});

// Socket.IO
const io = new SocketIOServer(server, {
  cors: {
    origin: (corsOptions as any).origin,
    methods: ["GET", "POST"],
    credentials: true,
  },
  // Força WebSocket puro para evitar problemas de long-polling em alguns ambientes
  transports: ["websocket"],
  pingTimeout: 60000,
  pingInterval: 25000,
});

// Inicializar WebSocketService
const wsService = WebSocketService.getInstance(io);

// Disponibilizar Socket.IO para as rotas
app.set("io", io);

console.log("🔧 DEBUG: About to register routes...");

// Registrar rotas modulares
app.use("/api/v1/genres", genresRoutes);
app.use("/api/v1/restaurants", restaurantsRoutes);
app.use("/api/v1/suggestions", suggestionsRoutes);
app.use("/api/v1/playlists", playlistsRoutes);
app.use("/api/v1/analytics", analyticsRoutes);
app.use("/api/v1/auth", authRoutes);
app.use("/api/v1/client", clientRoutes);
app.use("/api/v1/notifications", notificationsRoutes);
app.use("/api/v1/payments", paymentsRoutes);
try {
  const anyApp: any = app as any;
  const paymentsLayer = (anyApp._router?.stack || []).find((l: any) => l && l.regexp && String(l.regexp).includes('/api\\/v1\\/payments'));
  if (paymentsLayer) {
    const stack = (paymentsLayer.handle?.stack || []).filter((s: any) => s && s.route);
    logger.info(`🔎 Payments router montado com ${stack.length} rotas`);
    for (const s of stack) {
      logger.info(`➡️  /api/v1/payments ${Object.keys(s.route.methods).map(m=>m.toUpperCase()).join(',')} ${s.route.path}`);
    }
  } else {
    logger.warn("⚠️ Payments router não encontrado na stack do app");
  }
} catch (e) {
  logger.warn("⚠️ Falha ao inspecionar rotas de payments:", e);
}
app.use("/api/v1/playback", playbackRoutes);
app.use("/api/v1/playback-queue", playbackQueueRoutes);
// Rota de compatibilidade para o frontend
app.use("/api/v1/queues", playbackQueueRoutes);
app.use("/api/v1/rewards", rewardsRoutes);
app.use("/api/v1/youtube", youtubeRoutes);
app.use("/api/v1/live-playlist", livePlaylistRoutes);
app.use("/api/v1/collaborative-playlist", collaborativePlaylistRoutes);
app.use("/api/v1/youtube-auth", youtubeAuthRoutes);
app.use("/api/test-youtube", testYoutubeRoutes);
app.use("/api/v1/qrcode", qrcodeRoutes);
app.use("/api/v1/business-hours", businessHoursRoutes);
app.use("/api/v1/competitive-voting", competitiveVotingRoutes);
app.use("/api/v1/lyrics", lyricsRoutes);
app.use("/api/v1/schedules", schedulesRoutes);
app.use("/api/v1/playlist-schedules", playlistSchedulesRoutes);
app.use("/api/v1/playlist-reorder", playlistReorderRoutes);

console.log("✅ Routes registered successfully");

// Endpoints adicionais para compatibilidade
// Endpoint para obter dados de um restaurante específico
app.get("/api/v1/restaurants/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
      relations: ["playlists", "suggestions", "users"],
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    res.json({ restaurant: restaurant.toPublicJSON() });
  } catch (error) {
    logger.error("Erro ao buscar restaurante:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para listar restaurantes
app.get("/api/v1/restaurants", async (req, res) => {
  try {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurants = await restaurantRepository.find({
      where: { isActive: true },
      order: { name: "ASC" },
    });

    res.json({
      restaurants: restaurants.map((r) => r.toPublicJSON()),
    });
  } catch (error) {
    logger.error("Erro ao listar restaurantes:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para atualizar um restaurante
app.put("/api/v1/admin/restaurants/:id", async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, phone, address, email, password } = req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const userRepository = AppDataSource.getRepository(User);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
      relations: ["users"],
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // Atualizar dados do restaurante
    if (name) restaurant.name = name;
    if (description) restaurant.description = description;
    if (phone) restaurant.phone = phone;
    if (address) restaurant.address = address;

    await restaurantRepository.save(restaurant);

    // Atualizar dados do usuário admin se fornecidos
    if (email || password) {
      const adminUser = restaurant.users?.find((u) => u.role === "admin");

      if (adminUser) {
        if (email && email !== adminUser.email) {
          // Verificar se o novo email já está em uso
          const existingUser = await userRepository.findOne({
            where: { email },
            relations: ["restaurant"],
          });

          if (existingUser && existingUser.id !== adminUser.id) {
            return res.status(400).json({ error: "Email já está em uso" });
          }

          adminUser.email = email;
        }

        if (password) {
          const bcrypt = require("bcryptjs");
          adminUser.password = await bcrypt.hash(password, 10);
        }

        await userRepository.save(adminUser);
      }
    }

    // Buscar dados atualizados
    const updatedRestaurant = await restaurantRepository.findOne({
      where: { id },
      relations: ["users"],
    });

    const adminUser = updatedRestaurant?.users?.find((u) => u.role === "admin");

    res.json({
      success: true,
      message: "Restaurante atualizado com sucesso",
      restaurant: {
        id: updatedRestaurant!.id,
        name: updatedRestaurant!.name,
        description: updatedRestaurant!.description,
        phone: updatedRestaurant!.phone,
        address: updatedRestaurant!.address,
        isActive: updatedRestaurant!.isActive,
        createdAt: updatedRestaurant!.createdAt,
        adminUser: adminUser
          ? {
              id: adminUser.id,
              email: adminUser.email,
              name: adminUser.name,
              isActive: adminUser.isActive,
            }
          : null,
      },
    });
  } catch (error) {
    logger.error("Erro ao atualizar restaurante:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para alterar status do restaurante (ativar/desativar)
app.patch("/api/v1/admin/restaurants/:id/status", async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    restaurant.isActive = isActive;
    await restaurantRepository.save(restaurant);

    const action = isActive ? "ativado" : "desativado";
    res.json({
      success: true,
      message: `Restaurante ${action} com sucesso`,
      restaurant: {
        id: restaurant.id,
        name: restaurant.name,
        isActive: restaurant.isActive,
      },
    });
  } catch (error) {
    logger.error("Erro ao alterar status do restaurante:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para deletar um restaurante
app.delete("/api/v1/admin/restaurants/:id", async (req, res) => {
  try {
    const { id } = req.params;

    logger.info("🗑️ Iniciando deleção do restaurante", { id });

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const userRepository = AppDataSource.getRepository(User);

    const restaurant = await restaurantRepository.findOne({
      where: { id },
      relations: ["users"],
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    // Remover usuários vinculados (admin/staff)
    if (restaurant.users && restaurant.users.length) {
      await userRepository.remove(restaurant.users);
    }

    // Remover o restaurante
    await restaurantRepository.remove(restaurant);

    return res.json({
      success: true,
      message: "Restaurante deletado com sucesso",
      id,
    });
  } catch (error) {
    logger.error("Erro ao deletar restaurante:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoints PIX simulados removidos: usar rotas modulares em routes/payments.ts (Mercado Pago)

// Endpoint para listar sugestões de um restaurante
app.get("/api/v1/suggestions/:restaurantId", async (req, res) => {
  try {
    const { restaurantId } = req.params;
  const { status, limit = 50, offset = 0, isPaid, isPriority } = req.query as any;

    if (!restaurantId) {
      return res.status(400).json({ error: "RestaurantId é obrigatório" });
    }

    const suggestionRepository = AppDataSource.getRepository(Suggestion);

    const whereConditions: any = { restaurant: { id: restaurantId } };
    if (status) {
      whereConditions.status = status;
    }
    if (typeof isPaid !== "undefined") {
      // aceitar "true"/"false" (string) ou boolean
      const paid = String(isPaid).toLowerCase() === "true";
      whereConditions.isPaid = paid;
    }
    if (typeof isPriority !== "undefined") {
      const priority = String(isPriority).toLowerCase() === "true";
      whereConditions.isPriority = priority;
    }

  const suggestions = await suggestionRepository.find({
      where: whereConditions,
      order: {
        isPriority: "DESC", // Prioritárias primeiro
        createdAt: "DESC",
      },
      take: Number(limit),
      skip: Number(offset),
    });

    res.json({
      success: true,
      suggestions,
      total: suggestions.length,
    });
  } catch (error) {
    logger.error("Erro ao listar sugestões:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para votar em uma sugestão (com suporte a supervotos)
app.post("/api/v1/suggestions/:suggestionId/vote", async (req, res) => {
  try {
    const { suggestionId } = req.params;
    const {
      sessionId,
      tableNumber,
      voteType = "normal",
      paymentAmount,
      paymentId,
    } = req.body;

    if (!suggestionId) {
      return res.status(400).json({ error: "SuggestionId é obrigatório" });
    }

    // Validar tipo de voto
    if (!["normal", "super"].includes(voteType)) {
      return res
        .status(400)
        .json({ error: "Tipo de voto inválido. Use 'normal' ou 'super'" });
    }

    // Se for supervoto, validar pagamento
    if (voteType === "super") {
      if (!paymentAmount || !paymentId) {
        return res.status(400).json({
          error: "Supervotos requerem paymentAmount e paymentId",
        });
      }

      if (![5, 20, 50].includes(paymentAmount)) {
        return res.status(400).json({
          error: "Valores de supervoto válidos: R$ 5, R$ 20 ou R$ 50",
        });
      }
    }

    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const voteRepository = AppDataSource.getRepository(Vote);

    // Verificar se a sugestão existe
    const suggestion = await suggestionRepository.findOne({
      where: { id: suggestionId },
    });

    if (!suggestion) {
      return res.status(404).json({ error: "Sugestão não encontrada" });
    }

    // Verificar se já votou (por sessionId ou tableNumber)
    const existingVote = await voteRepository.findOne({
      where: [
        { suggestionId, sessionId },
        { suggestionId, tableNumber },
      ],
    });

    if (existingVote) {
      return res.status(400).json({ error: "Você já votou nesta sugestão" });
    }

    // Calcular peso do voto
    let voteWeight = 1; // Voto normal
    if (voteType === "super") {
      if (paymentAmount >= 50) voteWeight = 20; // R$ 50+ = 20 votos
      else if (paymentAmount >= 20) voteWeight = 8; // R$ 20+ = 8 votos
      else if (paymentAmount >= 5) voteWeight = 3; // R$ 5+ = 3 votos
    }

    // Criar o voto
    const vote = voteRepository.create({
      suggestionId,
      sessionId,
      tableNumber,
      voteType: VoteType.UP, // Todos os votos são positivos
      clientSessionId: sessionId,
    });

    await voteRepository.save(vote);

    // Atualizar contador de votos na sugestão
    suggestion.voteCount = (suggestion.voteCount || 0) + voteWeight;
    suggestion.upvotes = (suggestion.upvotes || 0) + voteWeight;

    // Se for supervoto, atualizar informações de pagamento
    if (voteType === "super") {
      suggestion.isPaid = true;
      suggestion.paymentAmount = Math.max(
        suggestion.paymentAmount || 0,
        paymentAmount
      );
      suggestion.paymentId = paymentId;
      suggestion.paymentStatus = "paid";
    }

    await suggestionRepository.save(suggestion);

    console.log(
      `🗳️ ${
        voteType === "super" ? "Supervoto" : "Voto normal"
      } registrado: +${voteWeight} votos (${suggestion.voteCount} total)`
    );

    // Emitir evento via Socket.IO
    io.to(`restaurant-${suggestion.restaurant.id}`).emit("voteUpdate", {
      suggestionId,
      votes: suggestion.voteCount,
      voteType,
      voteWeight,
      paymentAmount: voteType === "super" ? paymentAmount : undefined,
    });

    res.json({
      success: true,
      votes: suggestion.voteCount,
      voteWeight,
      voteType,
      paymentAmount: voteType === "super" ? paymentAmount : undefined,
      message:
        voteType === "super"
          ? `Supervoto de R$ ${paymentAmount.toFixed(
              2
            )} registrado! (+${voteWeight} votos)`
          : "Voto registrado com sucesso!",
    });
  } catch (error) {
    logger.error("Erro ao votar:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para obter ranking de votação
app.get(
  "/api/v1/restaurants/:restaurantId/voting-ranking",
  async (req, res) => {
    try {
      const { restaurantId } = req.params;
      const { limit = 20 } = req.query;

      if (!restaurantId) {
        return res.status(400).json({ error: "RestaurantId é obrigatório" });
      }

      const suggestionRepository = AppDataSource.getRepository(Suggestion);

      // Buscar sugestões ordenadas por votos (supervotos primeiro)
      const suggestions = await suggestionRepository
        .createQueryBuilder("suggestion")
        .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
        .andWhere("suggestion.status = :status", { status: "approved" })
        .orderBy("suggestion.is_paid", "DESC") // Supervotos primeiro
        .addOrderBy("suggestion.payment_amount", "DESC") // Maior valor primeiro
        .addOrderBy('suggestion."voteCount"', "DESC") // Mais votos primeiro
        .limit(parseInt(limit as string))
        .getMany();

      const ranking = suggestions.map((suggestion, index) => {
        // Calcular quantos supervotos e votos normais
        const superVoteWeight = suggestion.isPaid
          ? suggestion.paymentAmount >= 50
            ? 20
            : suggestion.paymentAmount >= 20
            ? 8
            : suggestion.paymentAmount >= 5
            ? 3
            : 1
          : 0;

        const normalVoteCount = Math.max(
          0,
          (suggestion.voteCount || 0) - superVoteWeight
        );

        return {
          position: index + 1,
          youtubeVideoId: suggestion.youtubeVideoId,
          title: suggestion.title,
          artist: suggestion.artist,
          voteCount: suggestion.voteCount || 0,
          superVoteCount: suggestion.isPaid ? 1 : 0,
          normalVoteCount,
          totalRevenue: suggestion.paymentAmount || 0,
          isPaid: suggestion.isPaid || false,
          paymentAmount: suggestion.paymentAmount || 0,
          tableNumber: suggestion.tableNumber,
          thumbnailUrl: suggestion.thumbnailUrl,
        };
      });

      res.json({
        success: true,
        data: ranking,
        message: `${ranking.length} músicas no ranking`,
        totalSongs: ranking.length,
        totalVotes: ranking.reduce((sum, item) => sum + item.voteCount, 0),
        totalRevenue: ranking.reduce((sum, item) => sum + item.totalRevenue, 0),
      });
    } catch (error) {
      logger.error("Erro ao obter ranking:", error);
      res.status(500).json({ error: "Erro interno do servidor" });
    }
  }
);

// Endpoint para aprovar/rejeitar sugestão (admin)
app.patch("/api/v1/suggestions/:suggestionId/status", async (req, res) => {
  try {
    const { suggestionId } = req.params;
    const { status, reason } = req.body;

    if (!suggestionId || !status) {
      return res.status(400).json({
        error: "SuggestionId e status são obrigatórios",
      });
    }

    if (!["approved", "rejected", "pending"].includes(status)) {
      return res.status(400).json({
        error: "Status deve ser: approved, rejected ou pending",
      });
    }

    const suggestionRepository = AppDataSource.getRepository(Suggestion);
    const suggestion = await suggestionRepository.findOne({
      where: { id: suggestionId },
    });

    if (!suggestion) {
      return res.status(404).json({ error: "Sugestão não encontrada" });
    }

    // Atualizar status
    suggestion.status = status as any;
    if (reason) {
      suggestion.rejectionReason = reason;
    }
    suggestion.updatedAt = new Date();

    await suggestionRepository.save(suggestion);

    // Emitir evento via Socket.IO
    io.to(`restaurant-${suggestion.restaurant.id}`).emit(
      "suggestionStatusUpdate",
      {
        suggestionId,
        status,
        reason,
      }
    );

    res.json({
      success: true,
      suggestion,
      message: `Sugestão ${
        status === "approved" ? "aprovada" : "rejeitada"
      } com sucesso!`,
    });
  } catch (error) {
    logger.error("Erro ao atualizar status da sugestão:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint para buscar músicas no YouTube
app.get("/api/v1/youtube/search", async (req, res) => {
  try {
    const { q, maxResults = 10 } = req.query;

    if (!q) {
      return res.status(400).json({ error: "Query (q) é obrigatória" });
    }

    // Em produção, usar a API real do YouTube
    // Por enquanto, retornar dados simulados
    const mockResults = [
      {
        id: { videoId: "dQw4w9WgXcQ" },
        snippet: {
          title: "Rick Astley - Never Gonna Give You Up",
          channelTitle: "Rick Astley",
          thumbnails: {
            default: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/default.jpg" },
            medium: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg" },
            high: { url: "https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg" },
          },
          publishedAt: "2009-10-25T06:57:33Z",
        },
        contentDetails: {
          duration: "PT3M33S",
        },
      },
    ];

    res.json({
      success: true,
      items: mockResults,
      query: q,
      maxResults: Number(maxResults),
    });
  } catch (error) {
    logger.error("Erro ao buscar no YouTube:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Endpoint removido - usando rota modular em /routes/qrcode.ts

// Endpoint duplicado removido - usar routes/client.ts

// Endpoint para verificar sessão do cliente
app.get("/api/v1/client/session/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!sessionId) {
      return res.status(400).json({ error: "SessionId é obrigatório" });
    }

    // Buscar sessão no Redis
    const sessionData = await redisClient.get(`client_session:${sessionId}`);

    if (!sessionData) {
      return res
        .status(404)
        .json({ error: "Sessão não encontrada ou expirada" });
    }

    const session = JSON.parse(sessionData);

    // Buscar dados do restaurante
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurant = await restaurantRepository.findOne({
      where: { id: session.restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({ error: "Restaurante não encontrado" });
    }

    res.json({
      success: true,
      session,
      restaurant: {
        id: restaurant.id,
        name: restaurant.name,
        description: restaurant.description,
      },
    });
  } catch (error) {
    logger.error("Erro ao verificar sessão do cliente:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Socket.IO handlers
io.on("connection", (socket) => {
  console.log(`Cliente conectado: ${socket.id}`);

  // Join restaurant room
  socket.on("join-restaurant", (restaurantId: string) => {
    socket.join(`restaurant-${restaurantId}`);
    console.log(`Socket ${socket.id} joined restaurant ${restaurantId}`);
  });

  // Join table room
  socket.on(
    "join-table",
    (data: { restaurantId: string; tableNumber: string }) => {
      const roomName = `restaurant-${data.restaurantId}-table-${data.tableNumber}`;
      socket.join(roomName);
      console.log(
        `Socket ${socket.id} joined table ${data.tableNumber} in restaurant ${data.restaurantId}`
      );
    }
  );

  // Handle suggestion events
  socket.on("new-suggestion", (data) => {
    socket.to(`restaurant-${data.restaurantId}`).emit("suggestion-added", data);
  });

  // Handle vote events
  socket.on("new-vote", (data) => {
    socket.to(`restaurant-${data.restaurantId}`).emit("vote-added", data);
  });

  // Handle playback events
  socket.on("playback-update", (data) => {
    socket
      .to(`restaurant-${data.restaurantId}`)
      .emit("playback-state-changed", data);
  });

  socket.on("disconnect", () => {
    console.log(`Cliente desconectado: ${socket.id}`);
  });
});

// Middleware de tratamento de erros
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    logger.error("Erro não tratado:", err);

    if (res.headersSent) {
      return next(err);
    }

    const statusCode = err.statusCode || err.status || 500;
    const message = err.message || "Erro interno do servidor";

    res.status(statusCode).json({
      error: message,
      ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
    });
  }
);

// ==================== ENDPOINTS QR CODE ====================
// MOVIDOS PARA: /routes/qrcode.ts

/*
// Listar QR Codes de um restaurante
app.get("/api/v1/qrcode/:restaurantId", async (req, res) => {
  try {
    const { restaurantId } = req.params;
    logger.info("📱 Carregando QR Codes para restaurante:", restaurantId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    // Buscar QR codes do banco de dados
    const qrCodes = await qrCodeRepository.find({
      where: {
        restaurant: { id: restaurantId },
        isActive: true,
      },
      relations: ["restaurant"],
      order: { createdAt: "DESC" },
    });

    logger.info("📊 Total de QR Codes encontrados:", qrCodes.length);

    res.json({
      success: true,
      qrCodes: qrCodes.map((qr) => qr.toPublicJSON()),
      total: qrCodes.length,
      restaurantId,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao buscar QR Codes:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Code para mesa específica
app.post("/api/v1/qrcode/table", async (req, res) => {
  try {
    console.log("🚨 ROTA EXECUTADA: /api/v1/qrcode/table");
    console.log("🚨 BODY:", req.body);

    const { restaurantId, tableNumber, tableName } = req.body;

    if (!restaurantId || !tableNumber) {
      return res.status(400).json({
        success: false,
        error: "restaurantId e tableNumber são obrigatórios",
      });
    }

    logger.info("🏷️ Gerando QR Code para mesa:", {
      restaurantId,
      tableNumber,
      tableName,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    // Verificar se já existe QR Code para esta mesa
    const existingQR = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        tableNumber: String(tableNumber),
        type: QRCodeType.TABLE,
        isActive: true,
      },
    });

    if (existingQR) {
      return res.status(400).json({
        success: false,
        error: `QR Code para mesa ${tableNumber} já existe`,
      });
    }

    // Gerar URL do QR Code
    const qrUrl = `http://localhost:8000/client/${restaurantId}?tableNumber=${tableNumber}`;

    // Gerar QR Code visual usando a biblioteca qrcode
    const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      quality: 0.92,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    // Criar QR Code no banco de dados
    const qrCode = qrCodeRepository.create({
      restaurant: restaurant,
      type: QRCodeType.TABLE,
      name: tableName || `Mesa ${tableNumber}`,
      tableNumber: String(tableNumber),
      url: qrUrl,
      qrCodeData: qrCodeDataURL,
      isActive: true,
    });

    const savedQRCode = await qrCodeRepository.save(qrCode);

    logger.info("✅ QR Code criado no banco:", savedQRCode.id);

    res.status(201).json({
      success: true,
      message: "QR Code gerado com sucesso",
      qrCode: savedQRCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Codes em lote para múltiplas mesas
app.post("/api/v1/qrcode/bulk-tables", async (req, res) => {
  try {
    const { restaurantId, tableCount, tablePrefix = "Mesa" } = req.body;

    if (!restaurantId || !tableCount || tableCount < 1 || tableCount > 50) {
      return res.status(400).json({
        success: false,
        error:
          "restaurantId é obrigatório e tableCount deve estar entre 1 e 50",
      });
    }

    console.log("🏷️ Gerando QR Codes em lote:", {
      restaurantId,
      tableCount,
      tablePrefix,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    const newQRCodes = [];
    let generated = 0;

    for (let i = 1; i <= tableCount; i++) {
      // Verificar se já existe
      const existingQR = await qrCodeRepository.findOne({
        where: {
          restaurant: { id: restaurantId },
          tableNumber: String(i),
          type: QRCodeType.TABLE,
          isActive: true,
        },
      });

      if (!existingQR) {
        const qrUrl = `http://localhost:8000/client/${restaurantId}?tableNumber=${i}`;

        // Gerar QR Code visual usando a biblioteca qrcode
        const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
          errorCorrectionLevel: "M",
          type: "image/png",
          quality: 0.92,
          margin: 1,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
          width: 256,
        });

        const qrCode = qrCodeRepository.create({
          restaurant: restaurant,
          type: QRCodeType.TABLE,
          name: `${tablePrefix} ${i}`,
          tableNumber: String(i),
          url: qrUrl,
          qrCodeData: qrCodeDataURL,
          isActive: true,
        });

        const savedQRCode = await qrCodeRepository.save(qrCode);
        newQRCodes.push(savedQRCode.toPublicJSON());
        generated++;
      }
    }

    console.log(`✅ ${generated} QR Codes gerados em lote`);

    res.status(201).json({
      success: true,
      message: `${generated} QR Codes gerados com sucesso`,
      qrCodes: newQRCodes,
      totalGenerated: generated,
      skipped: tableCount - generated,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Codes em lote:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Gerar QR Code geral do restaurante
app.post("/api/v1/qrcode/restaurant", async (req, res) => {
  try {
    const { restaurantId, restaurantName } = req.body;

    if (!restaurantId) {
      return res.status(400).json({
        success: false,
        error: "restaurantId é obrigatório",
      });
    }

    console.log("🏪 Gerando QR Code geral do restaurante:", {
      restaurantId,
      restaurantName,
    });

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se o restaurante existe
    const restaurant = await restaurantRepository.findOne({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return res.status(404).json({
        success: false,
        error: "Restaurante não encontrado",
      });
    }

    // Verificar se já existe QR Code geral para este restaurante
    const existingQR = await qrCodeRepository.findOne({
      where: {
        restaurant: { id: restaurantId },
        type: QRCodeType.RESTAURANT,
        isActive: true,
      },
    });

    if (existingQR) {
      return res.status(400).json({
        success: false,
        error: "QR Code geral do restaurante já existe",
      });
    }

    const qrUrl = `http://localhost:8000/client/${restaurantId}`;

    // Gerar QR Code visual usando a biblioteca qrcode
    const qrCodeDataURL = await QRCode.toDataURL(qrUrl, {
      errorCorrectionLevel: "M",
      type: "image/png",
      quality: 0.92,
      margin: 1,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
      width: 256,
    });

    const qrCode = qrCodeRepository.create({
      restaurant: restaurant,
      type: QRCodeType.RESTAURANT,
      name: restaurantName || "Acesso Geral",
      url: qrUrl,
      qrCodeData: qrCodeDataURL,
      isActive: true,
    });

    const savedQRCode = await qrCodeRepository.save(qrCode);

    console.log("✅ QR Code geral criado:", savedQRCode.id);

    res.status(201).json({
      success: true,
      message: "QR Code geral gerado com sucesso",
      qrCode: savedQRCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao criar QR Code geral:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Deletar QR Code
app.delete("/api/v1/qrcode/:qrCodeId", async (req, res) => {
  try {
    const { qrCodeId } = req.params;

    if (!qrCodeId) {
      return res.status(400).json({
        success: false,
        error: "qrCodeId é obrigatório",
      });
    }

    console.log("🗑️ Deletando QR Code:", qrCodeId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    const qrCode = await qrCodeRepository.findOne({
      where: { id: qrCodeId },
      relations: ["restaurant"],
    });

    if (!qrCode) {
      return res.status(404).json({
        success: false,
        error: "QR Code não encontrado",
      });
    }

    // Soft delete - marcar como inativo ao invés de deletar
    qrCode.isActive = false;
    await qrCodeRepository.save(qrCode);

    console.log("✅ QR Code desativado:", qrCode.name);

    res.json({
      success: true,
      message: "QR Code deletado com sucesso",
      deletedQRCode: qrCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao deletar QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});

// Atualizar imagem do QR Code
app.post("/api/v1/qrcode/update-image", async (req, res) => {
  try {
    const { qrCodeId, qrCodeDataURL } = req.body;

    if (!qrCodeId || !qrCodeDataURL) {
      return res.status(400).json({
        success: false,
        error: "qrCodeId e qrCodeDataURL são obrigatórios",
      });
    }

    console.log("🔄 Atualizando imagem do QR Code:", qrCodeId);

    const qrCodeRepository = AppDataSource.getRepository(QRCodeModel);

    const qrCode = await qrCodeRepository.findOne({
      where: { id: qrCodeId },
      relations: ["restaurant"],
    });

    if (!qrCode) {
      return res.status(404).json({
        success: false,
        error: "QR Code não encontrado",
      });
    }

    // Atualizar a imagem do QR Code
    qrCode.qrCodeData = qrCodeDataURL;
    await qrCodeRepository.save(qrCode);

    console.log("✅ Imagem do QR Code atualizada:", qrCode.name);

    res.json({
      success: true,
      message: "Imagem do QR Code atualizada com sucesso",
      qrCode: qrCode.toPublicJSON(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error("Erro ao atualizar imagem do QR Code:", error);
    res.status(500).json({
      success: false,
      error: "Erro interno do servidor",
    });
  }
});
*/

// ==================== ROTAS ADMIN TEMPORÁRIAS (ANTES DO 404) ====================
// Rota de teste admin
app.get("/api/v1/admin/test", (req, res) => {
  res.json({
    message: "Admin routes funcionando!",
    timestamp: new Date().toISOString(),
  });
});

// Listar todos os restaurantes (Admin)
app.get("/api/v1/admin/restaurants", async (req, res) => {
  try {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurants = await restaurantRepository.find({
      order: { createdAt: "DESC" },
      relations: ["users"],
    });

    const restaurantsWithAdminData = restaurants.map((restaurant) => {
      const adminUser = restaurant.users?.find((u) => u.role === "admin");
      return {
        ...restaurant.toJSON(),
        adminUser: adminUser
          ? {
              id: adminUser.id,
              email: adminUser.email,
              name: adminUser.name,
              isActive: adminUser.isActive,
            }
          : null,
      };
    });

    res.json({
      success: true,
      restaurants: restaurantsWithAdminData,
    });
  } catch (error) {
    logger.error("Erro ao listar restaurantes (admin):", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Criar novo restaurante (Admin)
app.post("/api/v1/admin/restaurants", async (req, res) => {
  try {
    const { name, email, description, address, phone, password } = req.body;

    if (!name || !email) {
      return res.status(400).json({ error: "Nome e email são obrigatórios" });
    }

    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const userRepository = AppDataSource.getRepository(User);

    // Verificar se email já existe
    const existingUser = await userRepository.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ error: "Email já está em uso" });
    }

    // Criar restaurante
    const restaurant = new Restaurant();
    restaurant.id = `restaurant-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    restaurant.name = name;
    restaurant.description = description;
    restaurant.address = address;
    restaurant.phone = phone;
    restaurant.isActive = true;
    restaurant.settings = {
      moderation: {
        requireApproval: false,
        maxSuggestionsPerUser: 3,
      },
      playlist: {
        defaultVolume: 70,
        crossfadeDuration: 3,
        shuffleMode: false,
        repeatMode: "none",
        maxQueueSize: 50,
        allowDuplicates: false,
      },
      schedule: {
        timezone: "America/Sao_Paulo",
        operatingHours: {
          monday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
          tuesday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
          wednesday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
          thursday: { isOpen: true, openTime: "09:00", closeTime: "22:00" },
          friday: { isOpen: true, openTime: "09:00", closeTime: "23:00" },
          saturday: { isOpen: true, openTime: "10:00", closeTime: "23:00" },
          sunday: { isOpen: true, openTime: "10:00", closeTime: "21:00" },
        },
      },
    } as any;

    const savedRestaurant = await restaurantRepository.save(restaurant);

    // Usar senha fornecida ou gerar senha temporária
    const userPassword = password || Math.random().toString(36).slice(-8);
    const bcrypt = require("bcryptjs");
    const hashedPassword = await bcrypt.hash(userPassword, 10);

    // Criar usuário admin do restaurante
    const user = new User();
    user.email = email;
    user.password = hashedPassword;
    user.name = `Admin ${name}`;
    user.role = "admin" as any; // Usar role válido do enum
    user.restaurant = savedRestaurant;
    user.isActive = true;

    await userRepository.save(user);

    logger.info("Restaurante criado via admin", {
      restaurantId: savedRestaurant.id,
      restaurantName: name,
      adminEmail: email,
    });

    res.status(201).json({
      success: true,
      restaurant: savedRestaurant.toJSON(),
      credentials: {
        email,
        password: userPassword,
      },
      loginUrl: `${
        process.env.FRONTEND_URL || "http://localhost:3000"
      }/restaurant/${savedRestaurant.id}/login`,
    });
  } catch (error) {
    logger.error("Erro ao criar restaurante (admin):", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Analytics do Admin
app.get("/api/v1/admin/analytics", async (req, res) => {
  try {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);
    const restaurants = await restaurantRepository.find({
      relations: ["users"],
    });

    const totalRestaurants = restaurants.length;
    const activeRestaurants = restaurants.filter((r) => r.isActive).length;
    const totalUsers = restaurants.reduce(
      (sum, r) => sum + (r.users?.length || 0),
      0
    );
    const activeUsers = restaurants.reduce(
      (sum, r) => sum + (r.users?.filter((u) => u.isActive).length || 0),
      0
    );

    // Dados mock para demonstração
    const analytics = {
      overview: {
        totalRestaurants,
        activeRestaurants,
        totalUsers,
        activeUsers,
        totalRevenue: Math.floor(Math.random() * 100000),
        monthlyGrowth: Math.floor(Math.random() * 20) + 5,
      },
      recentActivity: [
        {
          id: 1,
          type: "restaurant_created",
          message: "Novo restaurante cadastrado",
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: 2,
          type: "user_login",
          message: "Usuário fez login",
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        },
        {
          id: 3,
          type: "payment_received",
          message: "Pagamento processado",
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        },
      ],
      topRestaurants: restaurants.slice(0, 5).map((r) => ({
        id: r.id,
        name: r.name,
        revenue: Math.floor(Math.random() * 10000),
        users: r.users?.length || 0,
        status: r.isActive ? "active" : "inactive",
      })),
    };

    res.json({
      success: true,
      analytics,
    });
  } catch (error) {
    logger.error("Erro ao buscar analytics admin:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Configurações do Admin
app.get("/api/v1/admin/settings", async (req, res) => {
  try {
    // Configurações mock para demonstração
    const settings = {
      system: {
        maintenanceMode: false,
        allowNewRegistrations: true,
        maxRestaurantsPerPlan: 100,
        defaultTrialDays: 30,
      },
      notifications: {
        emailNotifications: true,
        smsNotifications: false,
        webhookUrl: "",
      },
      payments: {
        stripeEnabled: true,
        pixEnabled: true,
        defaultCurrency: "BRL",
        commissionRate: 5.0,
      },
      features: {
        analyticsEnabled: true,
        competitiveVotingEnabled: true,
        playlistSchedulingEnabled: true,
        qrCodeGenerationEnabled: true,
      },
    };

    res.json({
      success: true,
      settings,
    });
  } catch (error) {
    logger.error("Erro ao buscar configurações admin:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Atualizar configurações do Admin
app.put("/api/v1/admin/settings", async (req, res) => {
  try {
    const { settings } = req.body;

    // Em produção, salvar as configurações no banco de dados
    logger.info("Configurações admin atualizadas:", settings);

    res.json({
      success: true,
      message: "Configurações atualizadas com sucesso",
      settings,
    });
  } catch (error) {
    logger.error("Erro ao atualizar configurações admin:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Revenue do Admin
app.get("/api/v1/admin/revenue", async (req, res) => {
  try {
    const { period = "30d" } = req.query as { period?: string };

    const startDate = new Date();
    const endDate = new Date();
    switch (period) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    const paymentRepo = AppDataSource.getRepository(
      require("./models/Payment").Payment
    );
    const suggestionRepo = AppDataSource.getRepository(Restaurant); // placeholder to ensure import

    // Buscar pagamentos aprovados no período, com join em Suggestion -> Restaurant via query crua (JS agregação)
    const paymentRepository = AppDataSource.getRepository(
      require("./models/Payment").Payment
    );
    const suggestionRepository = AppDataSource.getRepository(
      require("./models/Suggestion").Suggestion
    );
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    const payments = await paymentRepository.find({
      where: {
        status: "approved",
        createdAt: require("typeorm").Between(startDate, endDate),
      },
      order: { createdAt: "ASC" },
    });

    // Carregar sugestões e mapear para restaurante
    const suggestionIds = Array.from(
      new Set(payments.map((p: any) => p.suggestionId).filter(Boolean))
    );
    const suggestions = suggestionIds.length
      ? await suggestionRepository.find({
          where: { id: require("typeorm").In(suggestionIds) },
          relations: ["restaurant"],
        })
      : [];
    const suggestionToRestaurant: Record<string, { id: string; name: string }> =
      {};
    for (const s of suggestions as any[]) {
      suggestionToRestaurant[s.id] = {
        id: s.restaurant?.id,
        name: s.restaurant?.name,
      };
    }

    // Agregações
    let totalAmountCents = 0;
    let totalPlatformFee = 0;
    let totalRestaurantAmount = 0;
    const byRestaurant: Record<
      string,
      { id: string; name: string; sum: number; platform: number; count: number }
    > = {};
    const byMethod: Record<string, { count: number; revenue: number }> = {};
    const byMonth: Record<
      string,
      { revenue: number; platform: number; transactions: number }
    > = {};

    for (const p of payments as any[]) {
      totalAmountCents += p.amount || 0;
      totalPlatformFee += p.platformFee || 0;
      totalRestaurantAmount += p.restaurantAmount || 0;

      const sugInfo = suggestionToRestaurant[p.suggestionId];
      const rKey = sugInfo?.id || "unknown";
      if (!byRestaurant[rKey]) {
        byRestaurant[rKey] = {
          id: rKey,
          name: sugInfo?.name || "Desconhecido",
          sum: 0,
          platform: 0,
          count: 0,
        };
      }
      byRestaurant[rKey].sum += p.amount || 0;
      byRestaurant[rKey].platform += p.platformFee || 0;
      byRestaurant[rKey].count += 1;

      const method = p.paymentMethod || "pix";
      if (!byMethod[method]) byMethod[method] = { count: 0, revenue: 0 };
      byMethod[method].count += 1;
      byMethod[method].revenue += (p.amount || 0) / 100;

      const mKey = `${p.createdAt.getFullYear()}-${String(
        p.createdAt.getMonth() + 1
      ).padStart(2, "0")}`;
      if (!byMonth[mKey])
        byMonth[mKey] = { revenue: 0, platform: 0, transactions: 0 };
      byMonth[mKey].revenue += (p.amount || 0) / 100;
      byMonth[mKey].platform += (p.platformFee || 0) / 100;
      byMonth[mKey].transactions += 1;
    }

    const totalRevenue = totalAmountCents / 100;
    const platformRevenue = totalPlatformFee / 100;
    const restaurantRevenue = totalRestaurantAmount / 100;
    const totalTransactions = payments.length;
    const averageTransactionValue =
      totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    // Converter agregações para arrays
    const revenueByRestaurant = Object.values(byRestaurant)
      .filter((r) => r.id !== "unknown")
      .map((r) => ({
        restaurantId: r.id,
        restaurantName: r.name,
        totalRevenue: r.sum / 100,
        platformShare: r.platform / 100,
        transactions: r.count,
        averageValue: r.count > 0 ? r.sum / 100 / r.count : 0,
      }))
      .sort((a, b) => b.totalRevenue - a.totalRevenue);

    const revenueByMonth = Object.keys(byMonth)
      .sort()
      .map((key) => {
        const [year, month] = key.split("-");
        const date = new Date(Number(year), Number(month) - 1, 1);
        return {
          month: date.toLocaleDateString("pt-BR", { month: "short" }),
          totalRevenue: byMonth[key].revenue,
          platformRevenue: byMonth[key].platform,
          transactions: byMonth[key].transactions,
        };
      });

    const paymentMethods = Object.entries(byMethod).map(([method, v]) => ({
      method: method.toUpperCase(),
      count: v.count,
      revenue: v.revenue,
      percentage: totalRevenue > 0 ? (v.revenue / totalRevenue) * 100 : 0,
    }));

    const revenueData = {
      totalRevenue,
      platformRevenue,
      restaurantRevenue,
      platformFeePercentage:
        totalRevenue > 0 ? (platformRevenue / totalRevenue) * 100 : 0,
      monthlyGrowth: 0, // pode ser calculado comparando com período anterior, omitimos por ora
      totalTransactions,
      averageTransactionValue,
      revenueByRestaurant,
      revenueByMonth,
      paymentMethods,
      period,
      generatedAt: new Date().toISOString(),
    };

    res.json({ success: true, revenue: revenueData });
  } catch (error) {
    logger.error("Erro ao buscar dados de receita admin:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// Export JSON de receitas (todos os restaurantes)
app.get("/api/v1/admin/revenue/export", async (req, res) => {
  try {
    const { period = "30d" } = req.query as { period?: string };
    // Reutiliza a lógica anterior via chamada interna HTTP seria custosa; replicamos aqui simplificadamente
    req.url = `/api/v1/admin/revenue?period=${period}`;
    // Em vez de proxy, refazemos a consulta chamando a função acima seria ideal. Para simplicidade, copiamos o cálculo.
    // Para evitar duplicação de lógica, poderíamos extrair para uma função utilitária. Mantido simples aqui.
    // Chamar a própria rota é complexo no mesmo handler; vamos refazer de forma compacta:

    const startDate = new Date();
    const endDate = new Date();
    switch (period) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    const paymentRepository = AppDataSource.getRepository(
      require("./models/Payment").Payment
    );
    const suggestionRepository = AppDataSource.getRepository(
      require("./models/Suggestion").Suggestion
    );
    const payments = await paymentRepository.find({
      where: {
        status: "approved",
        createdAt: require("typeorm").Between(startDate, endDate),
      },
      order: { createdAt: "ASC" },
    });

    const suggestionIds = Array.from(
      new Set(payments.map((p: any) => p.suggestionId).filter(Boolean))
    );
    const suggestions = suggestionIds.length
      ? await suggestionRepository.find({
          where: { id: require("typeorm").In(suggestionIds) },
          relations: ["restaurant"],
        })
      : [];
    const suggestionToRestaurant: Record<string, { id: string; name: string }> =
      {};
    for (const s of suggestions as any[]) {
      suggestionToRestaurant[s.id] = {
        id: s.restaurant?.id,
        name: s.restaurant?.name,
      };
    }

    const byRestaurant: any[] = [];
    const agg: Record<
      string,
      { id: string; name: string; sum: number; platform: number; count: number }
    > = {};
    for (const p of payments as any[]) {
      const r = suggestionToRestaurant[p.suggestionId];
      const key = r?.id || "unknown";
      if (!agg[key])
        agg[key] = {
          id: key,
          name: r?.name || "Desconhecido",
          sum: 0,
          platform: 0,
          count: 0,
        };
      agg[key].sum += p.amount || 0;
      agg[key].platform += p.platformFee || 0;
      agg[key].count += 1;
    }
    for (const v of Object.values(agg)) {
      if ((v as any).id === "unknown") continue;
      byRestaurant.push({
        restaurantId: (v as any).id,
        restaurantName: (v as any).name,
        totalRevenue: (v as any).sum / 100,
        platformShare: (v as any).platform / 100,
        transactions: (v as any).count,
        averageValue:
          (v as any).count > 0 ? (v as any).sum / 100 / (v as any).count : 0,
      });
    }

    const payload = {
      period,
      exportedAt: new Date().toISOString(),
      restaurants: byRestaurant.sort((a, b) => b.totalRevenue - a.totalRevenue),
    };

    res.setHeader("Content-Type", "application/json");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="revenue_${period}_${new Date()
        .toISOString()
        .slice(0, 10)}.json"`
    );
    res.status(200).send(JSON.stringify(payload, null, 2));
  } catch (error) {
    logger.error("Erro ao exportar dados de receita:", error);
    res.status(500).json({ error: "Erro interno do servidor" });
  }
});

// 404 e Error handlers (devem vir após TODAS as rotas)
app.use("*", notFoundHandler);
app.use(errorHandler);

// Função para inicializar o servidor
async function startServer() {
  try {
    console.log("🔄 Inicializando servidor...");

    // Inicializar banco de dados primeiro
    console.log("🔄 Conectando ao banco de dados...");
    await initializeDatabase();
    console.log("✅ Database initialized");

    // Verificar se as entidades estão carregadas
    const entities = AppDataSource.entityMetadatas;
    console.log(
      `✅ ${entities.length} entidades carregadas:`,
      entities.map((e) => e.name)
    );

    // Start periodic snapshots
    console.log("🔄 Starting snapshot service...");
    await snapshotService.startPeriodicSnapshots();
    console.log("✅ Snapshot service started");

    // Inicializar Redis
    console.log("🔄 Conectando ao Redis...");
    if (!redisClient.isReady) {
      await redisClient.connect();
      console.log("✅ Redis connected");
    } else {
      console.log("✅ Redis already connected");
    }

    // Inicializar serviços
    console.log("🔄 Inicializando serviços...");
    const youtubeService = new YouTubeService();
    const notificationService = new NotificationService();
    const playbackService = PlaybackService.getInstance();
    const rewardService = new RewardService();

    // Inicializar serviço de reordenação automática de playlists
    const { playlistReorderService } = await import(
      "./services/PlaylistReorderService"
    );
    playlistReorderService.startAutoReorder();

    // Endpoint de teste para o serviço de reordenação
    app.get("/api/v1/test-reorder-status", (req, res) => {
      const status = playlistReorderService.getStatus();
      res.json({
        success: true,
        message: "Serviço de reordenação funcionando",
        status: {
          isRunning: status.isRunning,
          nextExecution: status.nextExecution?.toISOString(),
          uptime: Math.round(status.uptime),
        },
      });
    });

    // Endpoint para atualizar metadados de fallback
    app.post("/api/v1/test-update-fallback/:restaurantId", async (req, res) => {
      try {
        const { collaborativePlaylistService } = await import(
          "./services/CollaborativePlaylistService"
        );
        const result =
          await collaborativePlaylistService.updateFallbackMetadata(
            req.params.restaurantId
          );
        res.json(result);
      } catch (error) {
        res.status(500).json({
          success: false,
          message: `Erro: ${
            error instanceof Error ? error.message : "Erro desconhecido"
          }`,
        });
      }
    });

    // Endpoint para testar notificações em tempo real
    app.post("/api/v1/test-notifications/:restaurantId", async (req, res) => {
      try {
        const { WebSocketService } = await import(
          "./services/WebSocketService"
        );
        const webSocketService = WebSocketService.getInstance();

        const testNotification = {
          type: "test_notification",
          message: "Sistema de notificações funcionando!",
          timestamp: new Date().toISOString(),
          data: {
            restaurantId: req.params.restaurantId,
            testType: "notification_system",
          },
        };

        await webSocketService.emitToRestaurant(
          req.params.restaurantId,
          "testNotification",
          testNotification
        );

        res.json({
          success: true,
          message: "Notificação de teste enviada",
          notification: testNotification,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: `Erro: ${
            error instanceof Error ? error.message : "Erro desconhecido"
          }`,
        });
      }
    });

    console.log("✅ Services initialized");

    // Criar dados de exemplo se não existirem
    await createSampleData();

    // Iniciar servidor
    const PORT = process.env.PORT || 5000;
    server.listen(PORT, () => {
      console.log(`🚀 Servidor rodando na porta ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🌐 API: http://localhost:${PORT}/api/v1`);
      console.log(`🎵 Sistema de Playlist Interativa - Pronto!`);
    });
  } catch (error) {
    console.error("❌ Erro ao inicializar servidor:", error);
    console.error("Stack trace:", error.stack);
    process.exit(1);
  }
}

// Função para criar dados de exemplo
async function createSampleData() {
  try {
    const restaurantRepository = AppDataSource.getRepository(Restaurant);

    // Verificar se já existe o restaurante demo
    const existingRestaurant = await restaurantRepository.findOne({
      where: { id: "demo-restaurant" },
    });

    if (!existingRestaurant) {
      console.log("🔄 Criando restaurante demo...");

      const demoRestaurant = new Restaurant();
      // Definir um ID/slug estável para evitar erro de NOT NULL em "id"
      demoRestaurant.id = "demo-restaurant";
      demoRestaurant.name = "Restaurante Demo";
      demoRestaurant.description = "Restaurante de demonstração do sistema";
      demoRestaurant.email = "<EMAIL>";
      demoRestaurant.phone = "(11) 99999-9999";
      demoRestaurant.isActive = true;
      demoRestaurant.status = "active" as any;
      demoRestaurant.settings = {
        moderation: {
          autoApprove: false,
          requireApproval: true,
          maxSuggestionsPerUser: 5,
          maxSuggestionsPerHour: 20,
          allowExplicitContent: false,
          allowLiveVideos: true,
          minVideoDuration: 30,
          maxVideoDuration: 600,
        },
        playlist: {
          maxQueueSize: 50,
          allowDuplicates: false,
          shuffleMode: false,
          repeatMode: "none",
          crossfadeDuration: 3,
          defaultVolume: 70,
        },
        interface: {
          theme: "light" as any,
          primaryColor: "#007bff",
          secondaryColor: "#6c757d",
          showVoteCount: true,
          showQueuePosition: true,
          allowAnonymousSuggestions: true,
          requireSessionId: false,
        },
      };
      demoRestaurant.businessHours = {
        monday: { open: "18:00", close: "02:00", closed: false },
        tuesday: { open: "18:00", close: "02:00", closed: false },
        wednesday: { open: "18:00", close: "02:00", closed: false },
        thursday: { open: "18:00", close: "02:00", closed: false },
        friday: { open: "18:00", close: "03:00", closed: false },
        saturday: { open: "18:00", close: "03:00", closed: false },
        sunday: { open: "18:00", close: "01:00", closed: false },
      };

      const savedRestaurant = await restaurantRepository.save(demoRestaurant);
      // Definir o ID após salvar
      savedRestaurant.id = "demo-restaurant";
      await restaurantRepository.save(savedRestaurant);
      console.log("✅ Restaurante demo criado");
    } else {
      console.log("✅ Restaurante demo já existe");
    }
  } catch (error) {
    console.error("❌ Erro ao criar dados de exemplo:", error);
    // Não falhar o servidor por causa disso
  }
}

// Graceful shutdown
process.on("SIGTERM", async () => {
  console.log("🔄 Recebido SIGTERM, encerrando servidor...");
  server.close(async () => {
    await AppDataSource.destroy();
    await redisClient.quit();
    console.log("✅ Servidor encerrado graciosamente");
    process.exit(0);
  });
});

process.on("SIGINT", async () => {
  console.log("🔄 Recebido SIGINT, encerrando servidor...");
  server.close(async () => {
    await AppDataSource.destroy();
    await redisClient.quit();
    console.log("✅ Servidor encerrado graciosamente");
    process.exit(0);
  });
});

// Inicializar servidor
startServer();

export { app, server, io };
