"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.playbackQueueService = void 0;
const database_1 = require("../config/database");
const Suggestion_1 = require("../models/Suggestion");
const Restaurant_1 = require("../models/Restaurant");
const ClientSession_1 = require("../models/ClientSession");
const QueueItem_1 = require("../models/QueueItem");
const redis_1 = require("../config/redis");
const NotificationService_1 = require("./NotificationService");
const YouTubeService_1 = require("./YouTubeService");
const WebSocketService_1 = require("./WebSocketService");
const CollaborativePlaylistService_1 = require("./CollaborativePlaylistService");
class PlaybackQueueService {
    constructor() {
        this.suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        this.restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
        this.sessionRepository = database_1.AppDataSource.getRepository(ClientSession_1.ClientSession);
        this.queueItemRepository = database_1.AppDataSource.getRepository(QueueItem_1.QueueItem);
        this.youtubeService = new YouTubeService_1.YouTubeService();
        this.ws = WebSocketService_1.WebSocketService.getInstance();
    }
    // Emite para os clients qual é o próximo vídeo autoritativo (primeiro da fila)
    async broadcastSelectedNext(restaurantId) {
        try {
            const { queue } = await this.getPlaybackQueue(restaurantId);
            const next = queue[0];
            if (!next)
                return;
            await this.ws?.emitToRestaurant(restaurantId, "reorderSelected", {
                video: {
                    suggestionId: next.suggestionId,
                    youtubeVideoId: next.youtubeVideoId,
                    title: next.title,
                    artist: next.artist,
                    duration: next.duration,
                    thumbnailUrl: next.thumbnailUrl,
                    isPaid: next.isPaid,
                    paymentAmount: next.paymentAmount,
                    position: next.position,
                },
                timestamp: new Date().toISOString(),
            });
        }
        catch (e) {
            console.warn("Falha ao emitir reorderSelected:", e);
        }
    }
    // Obter fila de reprodução completa
    async getPlaybackQueue(restaurantId) {
        try {
            // Buscar sugestões aprovadas ordenadas por prioridade
            const suggestions = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.restaurant", "restaurant")
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.status = :status", { status: "approved" })
                .andWhere("suggestion.playedAt IS NULL") // Não tocadas ainda
                .orderBy("suggestion.isPaid", "DESC") // Pagas primeiro
                // Prioriza músicas criadas no dia atual, depois as antigas
                .addOrderBy("CASE WHEN DATE(suggestion.createdAt) = CURRENT_DATE THEN 0 ELSE 1 END", "ASC")
                // Dentro do mesmo grupo (hoje vs antigas), mantém ordem de chegada
                .addOrderBy("suggestion.createdAt", "ASC")
                .getMany();
            // Aplicar ordenação autoritativa baseada no ranking (pagas primeiro; depois posição no ranking)
            try {
                const rankRes = await CollaborativePlaylistService_1.collaborativePlaylistService.getVotingRanking(restaurantId, 500);
                if (rankRes?.success && Array.isArray(rankRes.data)) {
                    const orderMap = new Map();
                    rankRes.data.forEach((r, idx) => {
                        orderMap.set(r.youtubeVideoId, idx);
                    });
                    suggestions.sort((a, b) => {
                        if (a.isPaid !== b.isPaid)
                            return a.isPaid ? -1 : 1;
                        const av = orderMap.get(a.youtubeVideoId) ?? Number.MAX_SAFE_INTEGER;
                        const bv = orderMap.get(b.youtubeVideoId) ?? Number.MAX_SAFE_INTEGER;
                        if (av !== bv)
                            return av - bv;
                        const aCreated = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                        const bCreated = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                        return aCreated - bCreated;
                    });
                }
            }
            catch (e) {
                // Se ranking falhar, manter ordenação padrão vinda do banco
                console.warn("Falha ao aplicar ordenação por ranking na fila:", e);
            }
            // Converter para QueueItem
            const queue = suggestions.map((suggestion, index) => ({
                id: `queue_${suggestion.id}`,
                suggestionId: suggestion.id,
                title: suggestion.title,
                artist: suggestion.artist,
                duration: suggestion.duration || 180,
                thumbnailUrl: suggestion.thumbnailUrl,
                youtubeVideoId: suggestion.youtubeVideoId,
                isPaid: suggestion.isPaid,
                paymentAmount: suggestion.paymentAmount
                    ? suggestion.paymentAmount / 100
                    : undefined,
                clientName: suggestion.clientSession?.clientName || suggestion.clientName,
                tableName: `Mesa ${suggestion.clientSession?.tableNumber || suggestion.tableNumber || "?"}`,
                sessionId: suggestion.clientSessionId,
                addedAt: suggestion.createdAt,
                position: index + 1,
                priority: suggestion.isPaid ? 1 : 2,
            }));
            // Anexar métricas globais de likes/dislikes (armazenadas no Redis)
            try {
                if (queue.length > 0 && redis_1.redisClient.isReady) {
                    const likeKeys = queue.map((item) => `analytics:likes:${restaurantId}:${item.youtubeVideoId}`);
                    const dislikeKeys = queue.map((item) => `analytics:dislikes:${restaurantId}:${item.youtubeVideoId}`);
                    const client = redis_1.redisClient.getClient();
                    const [likes, dislikes] = await Promise.all([
                        client.mGet(likeKeys),
                        client.mGet(dislikeKeys),
                    ]);
                    queue.forEach((item, idx) => {
                        const likeCount = parseInt(likes?.[idx] || "0", 10) || 0;
                        const dislikeCount = parseInt(dislikes?.[idx] || "0", 10) || 0;
                        item.likeCount = likeCount;
                        item.dislikeCount = dislikeCount;
                    });
                }
            }
            catch (e) {
                console.warn("Falha ao anexar métricas de likes/dislikes:", e);
            }
            // Calcular tempos estimados
            let cumulativeDuration = 0;
            queue.forEach((item, index) => {
                if (index === 0) {
                    item.estimatedPlayTime = new Date();
                }
                else {
                    item.estimatedPlayTime = new Date(Date.now() + cumulativeDuration * 1000);
                }
                cumulativeDuration += item.duration;
            });
            // Calcular estatísticas
            const stats = {
                totalItems: queue.length,
                paidItems: queue.filter((item) => item.isPaid).length,
                freeItems: queue.filter((item) => !item.isPaid).length,
                totalDuration: queue.reduce((sum, item) => sum + item.duration, 0),
                estimatedWaitTime: queue.length > 0
                    ? queue[queue.length - 1].estimatedPlayTime.getTime() - Date.now()
                    : 0,
                currentlyPlaying: queue[0],
                nextUp: queue[1],
            };
            return { queue, stats };
        }
        catch (error) {
            console.error("Erro ao obter fila de reprodução:", error);
            return {
                queue: [],
                stats: {
                    totalItems: 0,
                    paidItems: 0,
                    freeItems: 0,
                    totalDuration: 0,
                    estimatedWaitTime: 0,
                },
            };
        }
    }
    // Adicionar música à fila (chamado após pagamento)
    async addToQueue(suggestionId) {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant", "clientSession"],
            });
            if (!suggestion) {
                return null;
            }
            // Marcar como aprovada e definir posição na fila
            const queuePosition = await this.getNextQueuePosition(suggestion.restaurant.id, suggestion.isPaid);
            // Usar update em vez de save para evitar problemas com triggers
            await this.suggestionRepository.update(suggestion.id, {
                status: Suggestion_1.SuggestionStatus.APPROVED,
                queuePosition: queuePosition,
            });
            // Atualizar o objeto local
            suggestion.status = Suggestion_1.SuggestionStatus.APPROVED;
            suggestion.queuePosition = queuePosition;
            // Criar item da fila no banco de dados
            const queueItem = this.queueItemRepository.create({
                restaurant: suggestion.restaurant,
                suggestion: suggestion,
                clientSession: suggestion.clientSession,
                youtubeVideoId: suggestion.youtubeVideoId,
                title: suggestion.title,
                artist: suggestion.artist,
                duration: suggestion.duration || 180,
                thumbnailUrl: suggestion.thumbnailUrl,
                position: suggestion.queuePosition || 0,
                priority: suggestion.isPaid ? 1 : 2,
                isPaid: suggestion.isPaid,
                paymentAmount: suggestion.paymentAmount,
                clientName: suggestion.clientSession?.clientName || suggestion.clientName,
                tableNumber: (() => {
                    const raw = suggestion.clientSession?.tableNumber ?? suggestion.tableNumber;
                    if (raw === null || raw === undefined || raw === "")
                        return undefined;
                    if (typeof raw === 'number')
                        return raw;
                    const n = parseInt(String(raw), 10);
                    return Number.isFinite(n) ? n : undefined;
                })(),
                sessionToken: suggestion.clientSessionId,
                addedAt: new Date(),
                status: "pending",
            });
            console.log("💾 Salvando item na fila:", queueItem.title);
            await this.queueItemRepository.save(queueItem);
            console.log("✅ Item salvo na fila com ID:", queueItem.id);
            // Notificar adição à fila
            await NotificationService_1.notificationService.sendToRestaurant(suggestion.restaurant.id, {
                type: NotificationService_1.NotificationType.MUSIC,
                title: "🎵 Nova música na fila",
                message: `"${suggestion.title}" ${suggestion.isPaid ? "(PAGA)" : ""} adicionada à fila`,
                priority: suggestion.isPaid
                    ? NotificationService_1.NotificationPriority.HIGH
                    : NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    queueItem,
                    isPaid: suggestion.isPaid,
                },
            });
            // Notificar cliente
            await NotificationService_1.notificationService.sendToSession(suggestion.clientSessionId, {
                type: NotificationService_1.NotificationType.SUCCESS,
                title: "🎵 Música na fila!",
                message: `"${suggestion.title}" está na posição ${queueItem.position} da fila`,
                priority: NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    queueItem,
                    estimatedWait: this.formatDuration(queueItem.estimatedPlayTime
                        ? (queueItem.estimatedPlayTime.getTime() - Date.now()) / 1000
                        : 0),
                },
            });
            return queueItem.toInterface();
        }
        catch (error) {
            console.error("Erro ao adicionar à fila:", error);
            return null;
        }
    }
    // Marcar música como tocando
    async markAsPlaying(suggestionId) {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant", "clientSession"],
            });
            if (!suggestion)
                return;
            suggestion.status = Suggestion_1.SuggestionStatus.PLAYING;
            suggestion.playedAt = new Date();
            await this.suggestionRepository.save(suggestion);
            // Iniciar sessão de votação se for música paga
            if (suggestion.isPaid) {
                // Aqui você chamaria o serviço de votação competitiva
                // await competitiveVotingService.startVotingSession(suggestionId);
            }
            // Notificar que música começou
            await NotificationService_1.notificationService.sendToRestaurant(suggestion.restaurant.id, {
                type: NotificationService_1.NotificationType.MUSIC,
                title: "🎵 Tocando agora",
                message: `"${suggestion.title}" por ${suggestion.artist}`,
                priority: NotificationService_1.NotificationPriority.HIGH,
                category: "now_playing",
                data: {
                    suggestionId,
                    title: suggestion.title,
                    artist: suggestion.artist,
                    isPaid: suggestion.isPaid,
                    duration: suggestion.duration,
                },
            });
            // Notificar cliente que sua música começou
            await NotificationService_1.notificationService.sendToSession(suggestion.clientSessionId, {
                type: NotificationService_1.NotificationType.SUCCESS,
                title: "🎵 Sua música está tocando!",
                message: `"${suggestion.title}" começou a tocar agora`,
                priority: NotificationService_1.NotificationPriority.HIGH,
                category: "now_playing",
                data: {
                    suggestionId,
                    canStartKaraoke: suggestion.isPaid,
                },
            });
        }
        catch (error) {
            console.error("Erro ao marcar como tocando:", error);
        }
    }
    // Marcar música como concluída
    async markAsCompleted(suggestionId) {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant"],
            });
            if (!suggestion)
                return;
            suggestion.status = Suggestion_1.SuggestionStatus.COMPLETED;
            suggestion.completedAt = new Date();
            await this.suggestionRepository.save(suggestion);
            // Colocar música em cooldown (10 minutos) para não voltar ao ranking imediatamente
            try {
                if (suggestion.restaurant?.id && suggestion.youtubeVideoId) {
                    const { collaborativePlaylistService } = await Promise.resolve().then(() => __importStar(require('./CollaborativePlaylistService')));
                    await collaborativePlaylistService.markSongInCooldown(suggestion.restaurant.id, suggestion.youtubeVideoId, 10);
                }
            }
            catch (e) {
                console.warn("Falha ao aplicar cooldown após conclusão:", e);
            }
            // Atualizar posições da fila
            await this.updateQueuePositions(suggestion.restaurant.id);
            // Notificar conclusão
            await NotificationService_1.notificationService.sendToRestaurant(suggestion.restaurant.id, {
                type: NotificationService_1.NotificationType.INFO,
                title: "🎵 Música concluída",
                message: `"${suggestion.title}" terminou de tocar`,
                priority: NotificationService_1.NotificationPriority.LOW,
                category: "completed",
                data: {
                    suggestionId,
                    title: suggestion.title,
                },
            });
        }
        catch (error) {
            console.error("Erro ao marcar como concluída:", error);
        }
    }
    // Marcar música como tocando por youtubeVideoId
    async markVideoAsPlaying(restaurantId, youtubeVideoId) {
        try {
            const suggestion = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.restaurant", "restaurant")
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.youtubeVideoId = :youtubeVideoId", {
                youtubeVideoId,
            })
                .andWhere("suggestion.status IN (:...st)", {
                st: [Suggestion_1.SuggestionStatus.APPROVED, Suggestion_1.SuggestionStatus.PLAYING],
            })
                .orderBy(`CASE WHEN suggestion.status = '${Suggestion_1.SuggestionStatus.APPROVED}' THEN 0 ELSE 1 END`, "ASC")
                .addOrderBy("suggestion.createdAt", "ASC")
                .getOne();
            if (!suggestion)
                return;
            suggestion.status = Suggestion_1.SuggestionStatus.PLAYING;
            suggestion.playedAt = new Date();
            await this.suggestionRepository.save(suggestion);
        }
        catch (error) {
            console.error("Erro ao marcar como tocando por vídeo:", error);
        }
    }
    // Marcar música como concluída por youtubeVideoId
    async markVideoAsCompleted(restaurantId, youtubeVideoId) {
        try {
            const suggestion = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.restaurant", "restaurant")
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.youtubeVideoId = :youtubeVideoId", {
                youtubeVideoId,
            })
                .andWhere("suggestion.status IN (:...st)", {
                st: [Suggestion_1.SuggestionStatus.PLAYING, Suggestion_1.SuggestionStatus.APPROVED],
            })
                .orderBy(`CASE WHEN suggestion.status = '${Suggestion_1.SuggestionStatus.PLAYING}' THEN 0 ELSE 1 END`, "ASC")
                .addOrderBy("suggestion.playedAt", "DESC")
                .addOrderBy("suggestion.createdAt", "ASC")
                .getOne();
            if (!suggestion)
                return;
            suggestion.status = Suggestion_1.SuggestionStatus.COMPLETED;
            suggestion.completedAt = new Date();
            await this.suggestionRepository.save(suggestion);
            // Colocar música em cooldown (10 minutos)
            try {
                const { collaborativePlaylistService } = await Promise.resolve().then(() => __importStar(require('./CollaborativePlaylistService')));
                await collaborativePlaylistService.markSongInCooldown(restaurantId, youtubeVideoId, 10);
            }
            catch (e) {
                console.warn("Falha ao aplicar cooldown após conclusão (por vídeo):", e);
            }
            // Atualizar posições remanescentes
            await this.updateQueuePositions(restaurantId);
        }
        catch (error) {
            console.error("Erro ao marcar como concluída por vídeo:", error);
        }
    }
    // Remover música da fila
    async removeFromQueue(suggestionId, reason = "removed") {
        try {
            const suggestion = await this.suggestionRepository.findOne({
                where: { id: suggestionId },
                relations: ["restaurant", "clientSession"],
            });
            if (!suggestion)
                return false;
            suggestion.status = Suggestion_1.SuggestionStatus.REJECTED;
            suggestion.rejectionReason = reason;
            await this.suggestionRepository.save(suggestion);
            // Atualizar posições da fila
            await this.updateQueuePositions(suggestion.restaurant.id);
            // Notificar remoção
            await NotificationService_1.notificationService.sendToSession(suggestion.clientSessionId, {
                type: NotificationService_1.NotificationType.WARNING,
                title: "🚫 Música removida",
                message: `"${suggestion.title}" foi removida da fila`,
                priority: NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    suggestionId,
                    reason,
                },
            });
            return true;
        }
        catch (error) {
            console.error("Erro ao remover da fila:", error);
            return false;
        }
    }
    // Obter próxima posição na fila
    async getNextQueuePosition(restaurantId, isPaid) {
        const query = this.suggestionRepository
            .createQueryBuilder("suggestion")
            .leftJoinAndSelect("suggestion.restaurant", "restaurant")
            .where("restaurant.id = :restaurantId", { restaurantId })
            .andWhere("suggestion.status = :status", { status: "approved" })
            .andWhere("suggestion.playedAt IS NULL");
        if (isPaid) {
            // Músicas pagas: inserir no final das pagas, antes das gratuitas
            query.andWhere("suggestion.isPaid = true");
        }
        const maxPosition = await query
            .select("MAX(suggestion.queuePosition)", "maxPosition")
            .getRawOne();
        return (maxPosition?.maxPosition || 0) + 1;
    }
    // Atualizar posições da fila após mudanças
    async updateQueuePositions(restaurantId) {
        try {
            const suggestions = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoinAndSelect("suggestion.restaurant", "restaurant")
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("suggestion.status = :status", { status: "approved" })
                .andWhere("suggestion.playedAt IS NULL")
                .orderBy("suggestion.isPaid", "DESC")
                .addOrderBy("CASE WHEN DATE(suggestion.createdAt) = CURRENT_DATE THEN 0 ELSE 1 END", "ASC")
                .addOrderBy("suggestion.createdAt", "ASC")
                .getMany();
            // Atualizar posições
            for (let i = 0; i < suggestions.length; i++) {
                suggestions[i].queuePosition = i + 1;
                await this.suggestionRepository.save(suggestions[i]);
            }
            // Após atualizar posições, informar o próximo autoritativo
            await this.broadcastSelectedNext(restaurantId);
        }
        catch (error) {
            console.error("Erro ao atualizar posições da fila:", error);
        }
    }
    // Obter estimativa de tempo para uma posição
    async getEstimatedWaitTime(restaurantId, position) {
        try {
            const { queue } = await this.getPlaybackQueue(restaurantId);
            if (position <= 0 || position > queue.length) {
                return 0;
            }
            // Somar duração de todas as músicas antes desta posição
            let totalDuration = 0;
            for (let i = 0; i < position - 1; i++) {
                totalDuration += queue[i].duration;
            }
            return totalDuration;
        }
        catch (error) {
            console.error("Erro ao calcular tempo de espera:", error);
            return 0;
        }
    }
    // Obter estatísticas da fila
    async getQueueStats(restaurantId) {
        try {
            const { stats } = await this.getPlaybackQueue(restaurantId);
            return stats;
        }
        catch (error) {
            console.error("Erro ao obter estatísticas da fila:", error);
            return {
                totalItems: 0,
                paidItems: 0,
                freeItems: 0,
                totalDuration: 0,
                estimatedWaitTime: 0,
            };
        }
    }
    // Reordenar fila (para admin)
    async reorderQueue(restaurantId, newOrder) {
        try {
            for (let i = 0; i < newOrder.length; i++) {
                const suggestionId = newOrder[i];
                await this.suggestionRepository.update({ id: suggestionId }, { queuePosition: i + 1 });
            }
            // Notificar mudança na fila
            await NotificationService_1.notificationService.sendToRestaurant(restaurantId, {
                type: NotificationService_1.NotificationType.INFO,
                title: "🔄 Fila reordenada",
                message: "A ordem da fila foi alterada pelo administrador",
                priority: NotificationService_1.NotificationPriority.NORMAL,
                category: "queue",
                data: {
                    newOrder,
                },
            });
            // Emite o próximo vídeo autoritativo após reordenar
            await this.broadcastSelectedNext(restaurantId);
            return true;
        }
        catch (error) {
            console.error("Erro ao reordenar fila:", error);
            return false;
        }
    }
    // Utilitário para formatar duração
    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        if (minutes > 60) {
            const hours = Math.floor(minutes / 60);
            const remainingMinutes = minutes % 60;
            return `${hours}h ${remainingMinutes}m`;
        }
        return `${minutes}m ${remainingSeconds}s`;
    }
    // Limpar fila (remover músicas antigas)
    async clearOldQueue(restaurantId, olderThanHours = 24) {
        try {
            const cutoffDate = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
            const result = await this.suggestionRepository
                .createQueryBuilder()
                .update(Suggestion_1.Suggestion)
                .set({ status: Suggestion_1.SuggestionStatus.EXPIRED })
                .where("restaurant.id = :restaurantId", { restaurantId })
                .andWhere("status = :status", { status: "approved" })
                .andWhere("createdAt < :cutoffDate", { cutoffDate })
                .andWhere("playedAt IS NULL")
                .execute();
            return result.affected || 0;
        }
        catch (error) {
            console.error("Erro ao limpar fila antiga:", error);
            return 0;
        }
    }
    // Método combinado: criar sugestão e adicionar à fila
    async createSuggestionAndAddToQueue(suggestionData) {
        console.log("🎵 Iniciando createSuggestionAndAddToQueue", { suggestionData });
        try {
            // Verificar se restaurante existe
            console.log("🏪 Verificando restaurante:", suggestionData.restaurantId);
            const restaurant = await this.restaurantRepository.findOne({
                where: { id: suggestionData.restaurantId, isActive: true },
            });
            if (!restaurant) {
                console.error("❌ Restaurante não encontrado:", suggestionData.restaurantId);
                throw new Error("Restaurante não encontrado ou inativo");
            }
            console.log("✅ Restaurante encontrado:", restaurant.name);
            // Verificar se sessão existe
            let session = await this.sessionRepository.findOne({
                where: {
                    sessionToken: suggestionData.clientSessionId,
                    restaurant: { id: suggestionData.restaurantId },
                },
            });
            if (!session) {
                // Criar sessão se não existir
                session = this.sessionRepository.create({
                    sessionToken: suggestionData.clientSessionId,
                    restaurant,
                    // Usar um IP válido para o tipo inet; "unknown" causa erro no Postgres
                    ipAddress: "0.0.0.0",
                    userAgent: "unknown",
                    lastActivity: new Date(),
                    isActive: true,
                });
                await this.sessionRepository.save(session);
            }
            // Buscar informações do vídeo no YouTube
            let videoInfo;
            try {
                videoInfo = await this.youtubeService.getVideoInfo(suggestionData.youtubeVideoId);
            }
            catch (error) {
                console.warn("Erro ao buscar info do YouTube, usando dados fornecidos:", error);
                videoInfo = {
                    title: suggestionData.title,
                    artist: suggestionData.artist,
                    duration: 180,
                    thumbnailUrl: `https://img.youtube.com/vi/${suggestionData.youtubeVideoId}/mqdefault.jpg`,
                };
            }
            // Criar sugestão
            const suggestion = new Suggestion_1.Suggestion();
            suggestion.youtubeVideoId = suggestionData.youtubeVideoId;
            suggestion.title = videoInfo.title || suggestionData.title;
            suggestion.artist = videoInfo.artist || suggestionData.artist;
            suggestion.duration = videoInfo.duration || 180;
            suggestion.thumbnailUrl = videoInfo.thumbnailUrl;
            suggestion.clientSessionId = suggestionData.clientSessionId;
            suggestion.status =
                suggestionData.status || Suggestion_1.SuggestionStatus.APPROVED;
            // Garantir booleano válido para coluna is_paid (evitar valores como {})
            const rawPaid = suggestionData.isPaid;
            suggestion.isPaid =
                rawPaid === true ||
                    rawPaid === "true" ||
                    rawPaid === 1 ||
                    rawPaid === "1";
            suggestion.source = "client";
            suggestion.createdAt = new Date();
            suggestion.updatedAt = new Date();
            // Definir as relações
            suggestion.restaurant = restaurant;
            suggestion.sessionId = session.id;
            console.log("💾 Salvando sugestão:", suggestion.title);
            await this.suggestionRepository.save(suggestion);
            console.log("✅ Sugestão salva com ID:", suggestion.id);
            // Adicionar à fila
            console.log("🎵 Adicionando à fila:", suggestion.id);
            const queueItem = await this.addToQueue(suggestion.id);
            console.log("✅ Item adicionado à fila:", queueItem ? "sucesso" : "falhou");
            return queueItem;
        }
        catch (error) {
            console.error("Erro ao criar sugestão e adicionar à fila:", error);
            console.error("Stack trace:", error instanceof Error ? error.stack : error);
            console.error("Dados da sugestão:", suggestionData);
            throw error; // Re-throw para que o erro seja tratado adequadamente
        }
    }
    // Promover música para fila prioritária
    async promoteTrack(restaurantId, trackId) {
        try {
            // Buscar a sugestão
            const suggestion = await this.suggestionRepository.findOne({
                where: {
                    id: trackId,
                    restaurant: { id: restaurantId },
                },
                relations: ["restaurant"],
            });
            if (!suggestion) {
                throw new Error("Música não encontrada");
            }
            // Marcar como paga (prioridade)
            suggestion.isPaid = true;
            suggestion.paymentAmount = 2.0; // Valor padrão para promoção
            await this.suggestionRepository.save(suggestion);
            // Atualizar cache da fila
            const queueKey = `queue:${restaurantId}`;
            const cachedQueue = await redis_1.redisClient.getClient().get(queueKey);
            if (cachedQueue) {
                const queue = JSON.parse(cachedQueue);
                const trackIndex = queue.findIndex((item) => item.suggestionId === trackId);
                if (trackIndex !== -1) {
                    // Atualizar item na fila
                    queue[trackIndex].isPaid = true;
                    queue[trackIndex].paymentAmount = 2.0;
                    queue[trackIndex].priority = 1; // Alta prioridade
                    // Reordenar fila (prioridade primeiro)
                    queue.sort((a, b) => a.priority - b.priority);
                    // Atualizar posições
                    queue.forEach((item, index) => {
                        item.position = index + 1;
                    });
                    // Salvar fila atualizada
                    await redis_1.redisClient
                        .getClient()
                        .setEx(queueKey, 3600, JSON.stringify(queue));
                }
            }
            console.log(`✅ Música ${suggestion.title} promovida para fila prioritária`);
        }
        catch (error) {
            console.error("Erro ao promover música:", error);
            throw error;
        }
    }
}
exports.playbackQueueService = new PlaybackQueueService();
exports.default = PlaybackQueueService;
//# sourceMappingURL=PlaybackQueueService.js.map