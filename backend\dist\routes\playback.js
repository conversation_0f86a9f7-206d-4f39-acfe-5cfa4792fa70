"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const validation_1 = require("../utils/validation");
const database_1 = require("../config/database");
const Restaurant_1 = require("../models/Restaurant");
const Suggestion_1 = require("../models/Suggestion");
const asyncHandler_1 = __importDefault(require("../middleware/asyncHandler"));
const errors_1 = require("../utils/errors");
const redis_1 = require("../config/redis");
const PlaybackService_1 = require("../services/PlaybackService");
const router = (0, express_1.Router)();
/**
 * @route GET /api/v1/playback/:restaurantId/state
 * @desc Obter estado atual da reprodução
 */
router.get("/:restaurantId/state", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    // Verificar se o restaurante existe
    const restaurantRepository = database_1.AppDataSource.getRepository(Restaurant_1.Restaurant);
    const restaurant = await restaurantRepository.findOne({
        where: { id: restaurantId },
    });
    if (!restaurant) {
        throw new errors_1.NotFoundError("Restaurante não encontrado");
    }
    // Obter estado do Redis ou criar estado padrão
    const stateKey = `playback:${restaurantId}`;
    let playbackState;
    try {
        // Usar wrapper seguro do Redis
        const cachedState = await redis_1.redisClient.get(stateKey);
        if (cachedState) {
            playbackState = JSON.parse(cachedState);
        }
        else {
            // Estado padrão
            playbackState = {
                isPlaying: false,
                currentSong: null,
                volume: restaurant.settings?.playlist?.defaultVolume || 70,
                position: 0,
                queue: [],
                shuffle: restaurant.settings?.playlist?.shuffleMode || false,
                repeat: restaurant.settings?.playlist?.repeatMode || "none",
            };
            // Salvar estado padrão
            await redis_1.redisClient
                .getClient()
                .setEx(stateKey, 3600, JSON.stringify(playbackState));
        }
    }
    catch (error) {
        console.error("Erro ao acessar Redis:", error);
        // Fallback para estado padrão
        playbackState = {
            isPlaying: false,
            currentSong: null,
            volume: 70,
            position: 0,
            queue: [],
            shuffle: false,
            repeat: "none",
        };
    }
    res.json({
        success: true,
        playbackState,
    });
}));
/**
 * @route POST /api/v1/playback/:restaurantId/play
 * @desc Iniciar reprodução
 */
router.post("/:restaurantId/play", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("songId")
        .optional()
        .isString()
        .withMessage("ID da música deve ser uma string"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { songId } = req.body;
    const stateKey = `playback:${restaurantId}`;
    try {
        // 1) Validar que temos um songId válido; se não, tentar reaproveitar id salvo
        let targetSongId = songId;
        // Validar UUID v4 simples
        const uuidV4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!targetSongId) {
            // Tentar recuperar do estado simplificado (retrocompatibilidade)
            const cachedState = await redis_1.redisClient.get(stateKey);
            if (cachedState) {
                try {
                    const parsed = JSON.parse(cachedState);
                    targetSongId = parsed.currentSong?.id;
                }
                catch (e) {
                    // ignora estado inválido
                }
            }
        }
        if (!targetSongId || !uuidV4Regex.test(targetSongId)) {
            return res.status(400).json({
                success: false,
                error: "songId inválido ou ausente. Forneça um UUID v4 de uma sugestão válida.",
            });
        }
        // 2) Garantir que a sugestão pertence ao restaurante
        const suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        const suggestion = await suggestionRepository.findOne({
            where: { id: targetSongId, restaurant: { id: restaurantId } },
        });
        if (!suggestion) {
            return res.status(400).json({
                success: false,
                error: "Música não encontrada para este restaurante",
            });
        }
        // 3) Usar o serviço autoritativo para iniciar a reprodução (emite WS e seta cooldown)
        await PlaybackService_1.playbackService.playTrack(restaurantId, targetSongId);
        // 4) Obter estado atual do serviço e refletir no estado simplificado (Redis) para compatibilidade
        let svcState = null;
        try {
            svcState = await PlaybackService_1.playbackService.getPlaybackState(restaurantId);
        }
        catch { }
        const simplifiedState = {
            isPlaying: true,
            currentSong: suggestion
                ? {
                    id: suggestion.id,
                    title: suggestion.title,
                    artist: suggestion.artist,
                    youtubeId: suggestion.youtubeVideoId,
                    thumbnailUrl: suggestion.thumbnailUrl,
                    duration: suggestion.duration,
                }
                : null,
            volume: svcState?.volume ?? 70,
            position: 0,
            queue: [],
            shuffle: false,
            repeat: "none",
        };
        try {
            await redis_1.redisClient.set(stateKey, JSON.stringify(simplifiedState), 3600);
        }
        catch (e) {
            console.warn("Redis indisponível ao salvar estado de reprodução (play via service)", e);
        }
        return res.json({
            success: true,
            message: "Reprodução iniciada",
            playbackState: simplifiedState,
        });
    }
    catch (error) {
        console.error("Erro ao iniciar reprodução:", {
            restaurantId,
            songId,
            message: error?.message,
            stack: error?.stack,
        });
        return res.status(500).json({
            success: false,
            error: "Erro interno ao iniciar reprodução",
        });
    }
}));
/**
 * @route POST /api/v1/playback/:restaurantId/pause
 * @desc Pausar reprodução
 */
router.post("/:restaurantId/pause", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const stateKey = `playback:${restaurantId}`;
    try {
        const cachedState = await redis_1.redisClient.get(stateKey);
        if (cachedState) {
            let playbackState;
            try {
                playbackState = JSON.parse(cachedState);
            }
            catch (e) {
                console.warn("Estado de reprodução inválido no Redis (pause); usando padrão", e);
                playbackState = {
                    isPlaying: false,
                    currentSong: null,
                    volume: 70,
                    position: 0,
                    queue: [],
                    shuffle: false,
                    repeat: "none",
                };
            }
            playbackState.isPlaying = false;
            try {
                await redis_1.redisClient.set(stateKey, JSON.stringify(playbackState), 3600);
            }
            catch (e) {
                console.warn("Redis indisponível ao salvar estado de reprodução (pause)", e);
            }
            res.json({
                success: true,
                message: "Reprodução pausada",
                playbackState,
            });
        }
        else {
            res.json({
                success: true,
                message: "Nenhuma reprodução ativa",
            });
        }
    }
    catch (error) {
        console.error("Erro ao pausar reprodução:", error);
        res.status(500).json({
            success: false,
            error: "Erro interno ao pausar reprodução",
        });
    }
}));
/**
 * @route POST /api/v1/playback/:restaurantId/stop
 * @desc Parar reprodução
 */
router.post("/:restaurantId/stop", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const stateKey = `playback:${restaurantId}`;
    try {
        const cachedState = await redis_1.redisClient.get(stateKey);
        if (cachedState) {
            let playbackState;
            try {
                playbackState = JSON.parse(cachedState);
            }
            catch (e) {
                console.warn("Estado de reprodução inválido no Redis (stop); usando padrão", e);
                playbackState = {
                    isPlaying: false,
                    currentSong: null,
                    volume: 70,
                    position: 0,
                    queue: [],
                    shuffle: false,
                    repeat: "none",
                };
            }
            playbackState.isPlaying = false;
            playbackState.position = 0;
            try {
                await redis_1.redisClient.set(stateKey, JSON.stringify(playbackState), 3600);
            }
            catch (e) {
                console.warn("Redis indisponível ao salvar estado de reprodução (stop)", e);
            }
            res.json({
                success: true,
                message: "Reprodução parada",
                playbackState,
            });
        }
        else {
            res.json({
                success: true,
                message: "Nenhuma reprodução ativa",
            });
        }
    }
    catch (error) {
        console.error("Erro ao parar reprodução:", error);
        res.status(500).json({
            success: false,
            error: "Erro interno ao parar reprodução",
        });
    }
}));
/**
 * @route POST /api/v1/playback/:restaurantId/skip
 * @desc Pular música atual
 */
router.post("/:restaurantId/skip", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const stateKey = `playback:${restaurantId}`;
    try {
        const cachedState = await redis_1.redisClient.get(stateKey);
        let playbackState;
        if (cachedState) {
            try {
                playbackState = JSON.parse(cachedState);
            }
            catch (e) {
                console.warn("Estado de reprodução inválido no Redis (skip); usando padrão", e);
                playbackState = {
                    isPlaying: false,
                    currentSong: null,
                    volume: 70,
                    position: 0,
                    queue: [],
                    shuffle: false,
                    repeat: "none",
                };
            }
        }
        else {
            playbackState = {
                isPlaying: false,
                currentSong: null,
                volume: 70,
                position: 0,
                queue: [],
                shuffle: false,
                repeat: "none",
            };
        }
        // Marcar música atual como pulada e resetar posição
        playbackState.position = 0;
        playbackState.isPlaying = false;
        try {
            await redis_1.redisClient.set(stateKey, JSON.stringify(playbackState), 3600);
        }
        catch (e) {
            console.warn("Redis indisponível ao salvar estado de reprodução (skip)", e);
        }
        res.json({
            success: true,
            message: "Música pulada com sucesso",
            playbackState,
        });
    }
    catch (error) {
        console.error("Erro ao pular música:", error);
        res.status(500).json({
            success: false,
            error: "Erro interno ao pular música",
        });
    }
}));
/**
 * @route POST /api/v1/playback/:restaurantId/volume
 * @desc Ajustar volume
 */
router.post("/:restaurantId/volume", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
    (0, validation_1.body)("volume")
        .isInt({ min: 0, max: 100 })
        .withMessage("Volume deve ser entre 0 e 100"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    const { volume } = req.body;
    const stateKey = `playback:${restaurantId}`;
    try {
        const cachedState = await redis_1.redisClient.get(stateKey);
        let playbackState;
        if (cachedState) {
            try {
                playbackState = JSON.parse(cachedState);
            }
            catch (e) {
                console.warn("Estado de reprodução inválido no Redis (volume); usando padrão", e);
                playbackState = {
                    isPlaying: false,
                    currentSong: null,
                    volume: 70,
                    position: 0,
                    queue: [],
                    shuffle: false,
                    repeat: "none",
                };
            }
        }
        else {
            playbackState = {
                isPlaying: false,
                currentSong: null,
                volume: 70,
                position: 0,
                queue: [],
                shuffle: false,
                repeat: "none",
            };
        }
        playbackState.volume = volume;
        try {
            await redis_1.redisClient.set(stateKey, JSON.stringify(playbackState), 3600);
        }
        catch (e) {
            console.warn("Redis indisponível ao salvar estado de reprodução (volume)", e);
        }
        res.json({
            success: true,
            message: `Volume ajustado para ${volume}%`,
            playbackState,
        });
    }
    catch (error) {
        console.error("Erro ao ajustar volume:", error);
        res.status(500).json({
            success: false,
            error: "Erro interno ao ajustar volume",
        });
    }
}));
/**
 * @route GET /api/v1/playback/:restaurantId/problematic-report
 * @desc Obter relatório de músicas problemáticas
 */
router.get("/:restaurantId/problematic-report", [
    (0, validation_1.param)("restaurantId")
        .notEmpty()
        .withMessage("ID do restaurante é obrigatório"),
], (0, asyncHandler_1.default)(async (req, res) => {
    const errors = (0, validation_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errors_1.ValidationError("Dados inválidos", errors.array());
    }
    const { restaurantId } = req.params;
    try {
        const report = await PlaybackService_1.playbackService.getProblematicTracksReport(restaurantId);
        res.json({
            success: true,
            report,
            generatedAt: new Date().toISOString(),
        });
    }
    catch (error) {
        console.error("Erro ao gerar relatório de músicas problemáticas:", error);
        // Retornar dados mock em caso de erro
        res.json({
            success: true,
            report: {
                problematicTracks: [],
                recommendations: ["Sistema de análise temporariamente indisponível"],
                healthScore: 85,
            },
            generatedAt: new Date().toISOString(),
        });
    }
}));
exports.default = router;
//# sourceMappingURL=playback.js.map