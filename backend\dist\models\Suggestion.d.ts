import { Restaurant } from "./Restaurant";
import { Playlist } from "./Playlist";
import { Vote } from "./Vote";
import { User } from "./User";
import { ClientSession } from "./ClientSession";
export declare enum SuggestionStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    PLAYING = "playing",
    PLAYED = "played",
    SKIPPED = "skipped",
    COMPLETED = "completed",
    EXPIRED = "expired"
}
export declare enum SuggestionSource {
    CLIENT = "client",
    ADMIN = "admin",
    AUTO = "auto",
    IMPORT = "import"
}
export declare class Suggestion {
    id: string;
    youtubeVideoId: string;
    title: string;
    artist: string;
    genre?: string;
    channelName: string;
    duration: number;
    thumbnailUrl: string;
    description: string;
    status: SuggestionStatus;
    source: SuggestionSource;
    clientSessionId?: string;
    clientIp?: string;
    clientUserAgent?: string;
    voteCount: number;
    upvotes: number;
    downvotes: number;
    moderatedAt: Date;
    moderationReason?: string;
    playedAt: Date;
    playDuration: number;
    skipReason?: string;
    metadata: {
        genre?: string[];
        mood?: string[];
        language?: string;
        explicit?: boolean;
        live?: boolean;
        publishedAt?: string;
        viewCount?: number;
        likeCount?: number;
        tags?: string[];
    };
    moderationFlags: {
        autoRejected?: boolean;
        flaggedWords?: string[];
        contentWarnings?: string[];
        similarToRecent?: boolean;
        duplicateInQueue?: boolean;
        tooLong?: boolean;
        tooShort?: boolean;
    };
    isPriority: boolean;
    isPaid: boolean;
    paidAt?: Date;
    paymentAmount?: number;
    paymentId?: string;
    paymentStatus?: string;
    pixCode?: string;
    queuePosition?: number;
    clientName?: string;
    clientMessage?: string;
    tableNumber?: number;
    sessionId?: string;
    completedAt?: Date;
    votesCount: number;
    rejectionReason?: string;
    restaurant: Restaurant;
    playlist: Playlist;
    moderatedBy?: User;
    suggestedBy: User;
    votes: Vote[];
    clientSession?: ClientSession;
    createdAt: Date;
    updatedAt: Date;
    calculateScore(): number;
    private getTimeDecayFactor;
    canBeVoted(): boolean;
    isInQueue(): boolean;
    getFormattedDuration(): string;
    isExplicit(): boolean;
    isLive(): boolean;
    isPriorityPaid(): boolean;
    isPaymentPending(): boolean;
    isPaymentExpired(): boolean;
    markAsPaid(paymentId: string): void;
    markPaymentAsExpired(): void;
    getYouTubeUrl(): string;
    getYouTubeEmbedUrl(): string;
    approve(moderator?: User, reason?: string): void;
    reject(moderator?: User, reason?: string): void;
    startPlaying(): void;
    finishPlaying(duration?: number): void;
    skip(reason?: string): void;
    updateQueuePosition(position: number): void;
    needsModeration(): boolean;
    isModerated(): boolean;
    toPublicJSON(): {
        id: string;
        youtubeVideoId: string;
        title: string;
        artist: string;
        channelName: string;
        duration: number;
        formattedDuration: string;
        thumbnailUrl: string;
        status: SuggestionStatus;
        voteCount: number;
        upvotes: number;
        downvotes: number;
        votes: Vote[];
        queuePosition: number;
        score: number;
        canBeVoted: boolean;
        isInQueue: boolean;
        isExplicit: boolean;
        isLive: boolean;
        youtubeUrl: string;
        createdAt: Date;
        isPriority: boolean;
        isPaid: boolean;
        paidAt: Date;
        paymentAmount: number;
        paymentStatus: string;
        isPriorityPaid: boolean;
        isPaymentPending: boolean;
        clientName: string;
        tableNumber: number;
        metadata: {
            genre: string[];
            mood: string[];
            language: string;
        };
    };
    toAdminJSON(): {
        clientSessionId: string;
        clientIp: string;
        source: SuggestionSource;
        moderatedAt: Date;
        moderationReason: string;
        moderatedBy: {
            id: string;
            name: string;
            email: string;
        };
        playedAt: Date;
        playDuration: number;
        skipReason: string;
        moderationFlags: {
            autoRejected?: boolean;
            flaggedWords?: string[];
            contentWarnings?: string[];
            similarToRecent?: boolean;
            duplicateInQueue?: boolean;
            tooLong?: boolean;
            tooShort?: boolean;
        };
        fullMetadata: {
            genre?: string[];
            mood?: string[];
            language?: string;
            explicit?: boolean;
            live?: boolean;
            publishedAt?: string;
            viewCount?: number;
            likeCount?: number;
            tags?: string[];
        };
        id: string;
        youtubeVideoId: string;
        title: string;
        artist: string;
        channelName: string;
        duration: number;
        formattedDuration: string;
        thumbnailUrl: string;
        status: SuggestionStatus;
        voteCount: number;
        upvotes: number;
        downvotes: number;
        votes: Vote[];
        queuePosition: number;
        score: number;
        canBeVoted: boolean;
        isInQueue: boolean;
        isExplicit: boolean;
        isLive: boolean;
        youtubeUrl: string;
        createdAt: Date;
        isPriority: boolean;
        isPaid: boolean;
        paidAt: Date;
        paymentAmount: number;
        paymentStatus: string;
        isPriorityPaid: boolean;
        isPaymentPending: boolean;
        clientName: string;
        tableNumber: number;
        metadata: {
            genre: string[];
            mood: string[];
            language: string;
        };
    };
}
//# sourceMappingURL=Suggestion.d.ts.map