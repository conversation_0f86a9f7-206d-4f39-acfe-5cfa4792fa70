import React, { useEffect, useMemo, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useWebSocket } from "@/services/websocket";
import { buildApiUrl, getAuthHeaders } from "@/config/api";
import { usePersistentTimer } from "@/hooks/usePersistentTimer";
import {
  Star,
  Clock,
  CreditCard,
  MessageSquare,
  Timer,
  Music,
  Play,
  Users,
  RefreshCw,
  Volume2,
  Zap,
  Eye
} from "lucide-react";

type QueueItem = {
  id: string;
  suggestionId: string;
  youtubeVideoId?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  isPaid: boolean;
  paymentAmount?: number;
  position: number;
  isInCooldown?: boolean;
  cooldownTimeLeft?: number; // em segundos
};

type NowPlaying = QueueItem | null;

type SuperNote = {
  id: string;
  at: string; // ISO
  amount: number;
  voteWeight: number;
  title?: string;
  artist?: string;
  youtubeVideoId?: string;
  clientName?: string;
  tableNumber?: number;
  message?: string;
};

const fmtTime = (seconds: number) => {
  const m = Math.floor(seconds / 60);
  const s = seconds % 60;
  return `${m}:${String(s).padStart(2, "0")}`;
};

type PlaylistTrack = {
  id?: string;
  title: string;
  artist: string;
  duration: number;
  thumbnailUrl?: string;
  youtubeVideoId: string;
  formattedDuration?: string;
  position?: number;
  isAvailable?: boolean;
};

const CoverPage: React.FC = () => {
  const { restaurantId } = useParams<{ restaurantId: string }>();
  const { on, off, joinRestaurant, status } = useWebSocket();
  const { countdown, isGracePeriod, updateTimer, resetTimer, lastSnapshotRef } = usePersistentTimer(restaurantId || '');
  const [nowPlaying, setNowPlaying] = useState<NowPlaying>(null);
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [notes, setNotes] = useState<SuperNote[]>([]);
  // Notas de supervoto expiram após 5 minutos
  const NOTE_TTL_MS = 5 * 60 * 1000;
  const NOTES_STORAGE_KEY = `super-notes-${restaurantId}`;
  const [fullPlaylist, setFullPlaylist] = useState<PlaylistTrack[]>([]);
  const [playlistLoading, setPlaylistLoading] = useState<boolean>(false);
  const [playlistError, setPlaylistError] = useState<string | null>(null);

  // Guardas para evitar resets/jitter do countdown
  const lastVoteTsRef = useRef<number>(0);

  // Buscar playlist completa com Auth e retry leve (transiente quando backend sobe)
  const refetchFullPlaylist = async () => {
    if (!restaurantId) return;
    setPlaylistLoading(true);
    setPlaylistError(null);
    const headers = getAuthHeaders();
    let attempts = 0;
    const maxAttempts = 3;
    while (attempts < maxAttempts) {
      try {
        const res = await fetch(
          buildApiUrl(`/restaurants/${restaurantId}/playlist`),
          { headers }
        );
        if (!res.ok) {
          // 502/500 podem ocorrer se o upstream ainda está iniciando
          if ((res.status === 502 || res.status === 500) && attempts < maxAttempts - 1) {
            attempts++;
            await new Promise((r) => setTimeout(r, 500 * attempts));
            continue;
          }
          throw new Error(`HTTP ${res.status}`);
        }
        const json = await res.json();
        const items: PlaylistTrack[] = Array.isArray(json?.results) ? json.results : [];
        // Ordenar por position quando disponível
        items.sort((a, b) => (a.position || 0) - (b.position || 0));
        setFullPlaylist(items);
        setPlaylistLoading(false);
        return;
      } catch (err: any) {
        if (attempts < maxAttempts - 1) {
          attempts++;
          await new Promise((r) => setTimeout(r, 500 * attempts));
          continue;
        }
        setPlaylistError(err?.message || "Falha ao carregar playlist");
        setPlaylistLoading(false);
        return;
      }
    }
  };



  // Função para atualizar fila/ranking (usada no init e no auto-refresh)
  const autoRefresh = async () => {
    if (!restaurantId) return;
    setPlaylistLoading(true);
    try {
      const auth = getAuthHeaders();
      const [queueRes] = await Promise.all([
        fetch(buildApiUrl(`/playback-queue/${restaurantId}`), { headers: auth }),
      ]);
      if (queueRes.ok) {
        const q = await queueRes.json();
        // Mostrar a fila real no CoverPage
        setQueue(Array.isArray(q.queue) ? q.queue : []);
        setNowPlaying(q.currentlyPlaying || null);
      }
      await refetchFullPlaylist();
    } catch {}
    setPlaylistLoading(false);
  };

  // Carregar notas persistidas do localStorage na inicialização
  useEffect(() => {
    if (!restaurantId) return;

    try {
      const savedNotes = localStorage.getItem(NOTES_STORAGE_KEY);
      if (savedNotes) {
        const parsedNotes: SuperNote[] = JSON.parse(savedNotes);
        const now = Date.now();

        // Filtrar notas que ainda não expiraram
        const validNotes = parsedNotes.filter((note) => {
          const atMs = note.at ? new Date(note.at).getTime() : now;
          return now - atMs < NOTE_TTL_MS;
        });

        if (validNotes.length > 0) {
          setNotes(validNotes);

          // Agendar remoção das notas carregadas quando expirarem
          validNotes.forEach((note) => {
            const atMs = note.at ? new Date(note.at).getTime() : now;
            const delay = Math.max(0, NOTE_TTL_MS - (now - atMs));
            setTimeout(() => {
              setNotes((prev) => prev.filter((x) => x.id !== note.id));
            }, delay);
          });
        }
      }
    } catch (error) {
      console.warn('Erro ao carregar notas do localStorage:', error);
    }
  }, [restaurantId, NOTES_STORAGE_KEY, NOTE_TTL_MS]);

  // inicialização
  useEffect(() => {
    autoRefresh();
    // Join sala WS com o restaurantId antes de eventos
    if (restaurantId) {
      try { localStorage.setItem("currentRestaurantId", restaurantId); } catch {}
      joinRestaurant(restaurantId);
    }
    // eslint-disable-next-line
  }, [restaurantId, joinRestaurant]);



  // realtime
  useEffect(() => {
    const handleQueue = (data: any) => {
      // Mostrar fila real no CoverPage
      if (Array.isArray(data?.queue)) setQueue(data.queue);
      if (data?.currentlyPlaying) setNowPlaying(data.currentlyPlaying);
    };
    const handleRankingSnapshot = (snap: any) => {
      // Atualizar timer com base no snapshot recebido
      if (snap?.deadlineAt) {
        const deadlineMs = new Date(snap.deadlineAt).getTime();
        updateTimer(deadlineMs);
      }
    };
    const handleNowPlaying = (data: any) => {
      if (data?.suggestion) {
        const s = data.suggestion;
        setNowPlaying({
          id: s.id || s.youtubeVideoId,
          suggestionId: s.youtubeVideoId,
          youtubeVideoId: s.youtubeVideoId,
          title: s.title,
          artist: s.artist,
          duration: s.duration || 0,
          thumbnailUrl: s.thumbnailUrl,
          isPaid: !!(s as any).isPaid,
          paymentAmount: (s as any).paymentAmount,
          position: 0,
        });
      }
    };
  const handleSuperVote = (raw: any) => {
      // Alguns backends envolvem o payload em { event, data, timestamp, restaurantId }
      const data = (raw && typeof raw === "object" && "data" in raw) ? (raw as any).data : raw;
      console.info("[Cover] superVoteReceived:", raw);
      // Fallbacks defensivos: alguns ambientes podem enviar clientMessage/clientName em chaves distintas
      const msg =
        data?.payment?.message ??
        data?.payment?.clientMessage ??
        data?.suggestion?.clientMessage ??
        data?.message ??
        null;
      const name =
        data?.payment?.clientName ??
        data?.clientName ??
        data?.suggestion?.clientName ??
        null;
      const table =
        data?.payment?.tableNumber ??
        data?.tableNumber ??
        data?.suggestion?.tableNumber ??
        null;

      const n: SuperNote = {
        id: `${Date.now()}_${Math.random()}`,
        at: data?.timestamp || raw?.timestamp || new Date().toISOString(),
        amount: Number(data?.payment?.amount) || 0,
        voteWeight: Number(data?.payment?.voteWeight) || 0,
        title: data?.suggestion?.title,
        artist: data?.suggestion?.artist,
        youtubeVideoId: data?.suggestion?.youtubeVideoId,
        clientName: name || undefined,
        tableNumber: typeof table === "number" ? table : undefined,
        message: typeof msg === "string" ? msg : undefined,
      };
      setNotes((prev) => {
        const now = Date.now();
        const pruned = prev.filter((x) => {
          const atMs = x.at ? new Date(x.at).getTime() : now;
          return now - atMs < NOTE_TTL_MS;
        });
        const next = [n, ...pruned].slice(0, 50);
        console.info("[Cover] notes atualizado:", next[0]);

        // Salvar no localStorage
        try {
          localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(next));
        } catch (error) {
          console.warn('Erro ao salvar notas no localStorage:', error);
        }

        return next;
      });
      // agenda a remoção desta nota após o TTL restante
      try {
        const atMs = n.at ? new Date(n.at).getTime() : Date.now();
        const delay = Math.max(0, NOTE_TTL_MS - (Date.now() - atMs));
        setTimeout(() => {
          setNotes((prev) => {
            const filtered = prev.filter((x) => x.id !== n.id);
            // Salvar no localStorage após remoção
            try {
              localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(filtered));
            } catch (error) {
              console.warn('Erro ao salvar notas no localStorage (timeout):', error);
            }
            return filtered;
          });
        }, delay);
      } catch {}
    };
  // Removido: lógica de ranking/votos no Cover

    const handlePlaylistReordered = (data: any) => {
      // Preferir horário autoritativo do admin; caso contrário, calcular próximo ciclo
      try {
        let next: number | null = null;
        const adminNext = data?.adminDetails?.nextReorderTime;
        if (adminNext) {
          next = new Date(adminNext).getTime();
          updateTimer(next);
        } else {
          // Resetar timer para novo ciclo de 5 minutos
          resetTimer();
        }
      } catch {
        // Em caso de erro, resetar timer
        resetTimer();
      }

      // Atualizar playlist completa após reordenação
      refetchFullPlaylist();
    };

    // Handler para quando uma música termina (via WebSocket)
    const handleSongEnded = (data: any) => {
      console.log('🎵 Música terminou (WebSocket - CoverPage):', data);
      // Recarregar dados para refletir cooldowns atualizados
      setTimeout(() => {
        autoRefresh();
      }, 500);
    };

  on("queue-update" as any, handleQueue as any);
    on("now-playing" as any, handleNowPlaying as any);
    on("superVoteReceived" as any, handleSuperVote as any);
    on("song-ended" as any, handleSongEnded as any);
    // GC periódico para garantir limpeza mesmo se timers forem perdidos
    const gcId = setInterval(() => {
      const now = Date.now();
      setNotes((prev) => {
        const filtered = prev.filter((x) => {
          const atMs = x.at ? new Date(x.at).getTime() : now;
          return now - atMs < NOTE_TTL_MS;
        });

        // Salvar no localStorage após limpeza
        try {
          localStorage.setItem(NOTES_STORAGE_KEY, JSON.stringify(filtered));
        } catch (error) {
          console.warn('Erro ao salvar notas no localStorage (GC):', error);
        }

        return filtered;
      });
    }, 15000);
  on("playlistReordered" as any, handlePlaylistReordered as any);
  on("ranking-snapshot" as any, handleRankingSnapshot as any);
    return () => {
      off("queue-update" as any, handleQueue as any);
      off("now-playing" as any, handleNowPlaying as any);
      off("superVoteReceived" as any, handleSuperVote as any);
      off("song-ended" as any, handleSongEnded as any);
      clearInterval(gcId);
      off("playlistReordered" as any, handlePlaylistReordered as any);
      off("ranking-snapshot" as any, handleRankingSnapshot as any);
    };
    // Desabilitar re-subscribe por mudanças de identidade de on/off
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [restaurantId]);



  const top5 = useMemo(() => {
    return [] as any[];
  }, [nowPlaying, queue]);

  // sem catálogo: Couvert foca na fila (sequência)

  return (
    <div className="min-h-screen px-4 py-6 md:px-8 md:py-10 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white">
      <div className="mx-auto max-w-7xl">
        {/* Header melhorado */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center">
                  <Music className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-400 to-violet-400 bg-clip-text text-transparent">
                    Painel do Músico
                  </h1>
                  <p className="text-purple-200 text-sm">Couvert Musical Interativo</p>
                </div>
              </div>
              <p className="text-purple-300 text-sm max-w-2xl">
                Acompanhe o que está tocando, a fila e os recados dos fãs em tempo real.
                Sistema de SuperVotos ativo.
              </p>
            </div>

            {/* Stats rápidas */}
            <div className="flex gap-4 text-sm items-center">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center">
                <div className="text-lg font-bold text-green-400">{queue.length}</div>
                <div className="text-purple-200 text-xs">Na fila</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center">
                <div className="text-lg font-bold text-yellow-400">{notes.length}</div>
                <div className="text-purple-200 text-xs">SuperVotos</div>
              </div>
            </div>
          </div>

          {/* Cronômetro sempre visível e persistente */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className={`mt-4 inline-flex items-center gap-3 text-sm backdrop-blur-sm border rounded-lg px-4 py-2 ${
              isGracePeriod
                ? 'bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-400/30'
                : 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 border-indigo-400/30'
            }`}
          >
            <Timer className={`w-5 h-5 ${isGracePeriod ? 'text-orange-300 animate-bounce' : 'text-indigo-300 animate-pulse'}`}/>
            <span className={isGracePeriod ? 'text-orange-200' : 'text-indigo-200'}>
              {isGracePeriod ? 'Janela de graça! Últimos votos' : 'Próxima reordenação automática em'}
            </span>
            <span className={`font-mono font-bold text-xl px-2 py-1 rounded ${
              isGracePeriod
                ? 'text-orange-100 bg-orange-500/30'
                : 'text-indigo-100 bg-indigo-500/30'
            }`}>
              {countdown}
            </span>
          </motion.div>
        </motion.header>

        {/* Status da conexão WS */}
        {status !== "connected" && (
          <div className="mb-4 p-3 bg-yellow-500/20 border border-yellow-400/30 rounded-lg text-yellow-200 text-sm">
            {status === "connecting" ? "Conectando ao servidor em tempo real..." : "Desconectado do servidor. Tentando reconectar..."}
          </div>
        )}

        {/* Tocando agora (se houver) */}
        <AnimatePresence>
          {nowPlaying && (
            <motion.section
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="mb-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-xl p-6"
            >
              <div className="flex items-center gap-2 mb-3">
                <Volume2 className="w-5 h-5 text-green-400 animate-pulse" />
                <h2 className="text-lg font-semibold text-green-300">Tocando Agora</h2>
              </div>
              <div className="flex items-center gap-4">
                <img
                  src={nowPlaying.thumbnailUrl || `https://img.youtube.com/vi/${nowPlaying.youtubeVideoId}/mqdefault.jpg`}
                  alt={nowPlaying.title}
                  className="w-20 h-16 rounded-lg object-cover shadow-lg"
                />
                <div className="flex-1 min-w-0">
                  <h3 className="text-xl font-bold text-white truncate">{nowPlaying.title}</h3>
                  <p className="text-green-200 truncate">{nowPlaying.artist}</p>
                  <div className="flex items-center gap-4 mt-2 text-sm">
                    <span className="flex items-center gap-1 text-green-300">
                      <Clock className="w-4 h-4" />
                      {fmtTime(nowPlaying.duration)}
                    </span>
                    {nowPlaying.isPaid && nowPlaying.paymentAmount && (
                      <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                        <CreditCard className="w-4 h-4" />
                        SuperVoto R$ {Number(nowPlaying.paymentAmount).toFixed(2)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </motion.section>
          )}
        </AnimatePresence>

        {/* Layout reorganizado: todos em largura completa */}
        <div className="space-y-6 mb-6">
          {/* SuperVotos & Recados - movido para o topo */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden"
          >
            <div className="p-6 border-b border-white/20 bg-gradient-to-r from-emerald-500/10 to-green-500/10">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <MessageSquare className="w-5 h-5 text-emerald-400" />
                  SuperVotos & Recados
                </h2>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-emerald-300">Tempo real</span>
                </div>
              </div>
            </div>

            <div className="max-h-[500px] overflow-y-auto">
              {notes.length === 0 ? (
                <div className="p-8 text-center">
                  <MessageSquare className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">Sem SuperVotos ainda</p>
                  <p className="text-gray-500 text-sm">
                    Mensagens e pagamentos aparecem aqui quando clientes fazem SuperVotos
                  </p>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  <AnimatePresence>
                    {notes.map((n, index) => (
                      <motion.div
                        key={n.id}
                        initial={{ opacity: 0, x: -20, scale: 0.95 }}
                        animate={{ opacity: 1, x: 0, scale: 1 }}
                        exit={{ opacity: 0, x: 20, scale: 0.95 }}
                        transition={{ delay: index * 0.05 }}
                        className="p-4 bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-400/20 rounded-lg"
                      >
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-500 to-green-500 flex items-center justify-center text-white font-bold shadow-lg">
                            <Zap className="w-5 h-5" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <div className="font-semibold text-white truncate">
                                {n.title || n.youtubeVideoId || "Música"}
                              </div>
                              <div className="text-xs text-emerald-300 bg-emerald-500/20 px-2 py-1 rounded">
                                {new Date(n.at).toLocaleTimeString("pt-BR", {
                                  hour: "2-digit",
                                  minute: "2-digit"
                                })}
                              </div>
                            </div>

                            {n.artist && (
                              <div className="text-sm text-emerald-200 mb-2 truncate">{n.artist}</div>
                            )}

                            {n.message && (
                              <div className="text-sm text-gray-200 mb-3 p-2 bg-white/5 rounded border-l-2 border-emerald-400">
                                <span className="text-emerald-300 font-medium">
                                  {n.clientName
                                    ? `De: ${n.clientName}`
                                    : n.tableNumber
                                    ? `Mesa ${n.tableNumber}`
                                    : "Recado"}
                                  :
                                </span>{" "}
                                "{n.message}"
                              </div>
                            )}

                            <div className="flex items-center gap-4 text-xs">
                              <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                                <CreditCard className="w-3 h-3" />
                                R$ {n.amount.toFixed(2)}
                              </span>
                              <span className="flex items-center gap-1 text-indigo-300 bg-indigo-500/20 px-2 py-1 rounded">
                                <Star className="w-3 h-3" />
                                +{n.voteWeight} votos
                              </span>
                              {(n.clientName || n.tableNumber) && (
                                <span className="flex items-center gap-1 text-purple-300">
                                  <Users className="w-3 h-3" />
                                  {n.clientName || `Mesa ${n.tableNumber}`}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </motion.section>

          {/* Sequência (Fila) - largura completa */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Play className="w-5 h-5 text-purple-400" />
                Sequência (Fila)
              </h2>
              <div className="text-sm text-purple-300">
                {queue.length} música{queue.length !== 1 ? 's' : ''}
              </div>
            </div>
            <div className="max-h-[500px] overflow-y-auto space-y-2">
              {queue.length === 0 ? (
                <div className="p-8 text-center">
                  <Music className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">Fila vazia</p>
                  <p className="text-gray-500 text-sm">Aguardando sugestões dos clientes</p>
                </div>
              ) : (
                queue.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <span className="text-purple-300 font-bold">#{item.position}</span>
                      </div>
                      <img
                        src={item.thumbnailUrl || `https://img.youtube.com/vi/${item.youtubeVideoId}/mqdefault.jpg`}
                        alt={item.title}
                        className="w-16 h-12 rounded-lg object-cover shadow-md"
                      />
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-semibold truncate">{item.title}</div>
                        <div className="text-purple-200 text-sm truncate">{item.artist}</div>
                        <div className="flex items-center gap-3 mt-2 text-xs">
                          <span className="flex items-center gap-1 text-purple-300">
                            <Clock className="w-3 h-3" />
                            {fmtTime(item.duration)}
                          </span>
                          {item.isPaid && item.paymentAmount && (
                            <span className="flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded">
                              <Zap className="w-3 h-3" />
                              R$ {Number(item.paymentAmount).toFixed(2)}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </motion.section>

          {/* Catálogo Completo - largura completa */}
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold flex items-center gap-2">
                <Music className="w-5 h-5 text-blue-400" />
                Catálogo Completo
              </h2>
              <div className="flex items-center gap-3">
                <span className="text-sm text-purple-300">
                  {fullPlaylist.length} música{fullPlaylist.length !== 1 ? 's' : ''}
                </span>
                <button
                  onClick={refetchFullPlaylist}
                  disabled={playlistLoading}
                  className="flex items-center gap-1 text-xs text-blue-300 hover:text-blue-200 transition-colors disabled:opacity-50"
                >
                  <RefreshCw className={`w-3 h-3 ${playlistLoading ? 'animate-spin' : ''}`} />
                  {playlistLoading ? 'Carregando...' : 'Atualizar'}
                </button>
              </div>
            </div>

            {playlistError && (
              <div className="mb-4 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-300 text-sm">
                {playlistError}
              </div>
            )}

            <div className="max-h-[500px] overflow-y-auto space-y-2">
              {fullPlaylist.length === 0 && !playlistLoading ? (
                <div className="text-center py-8">
                  <Music className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                  <p className="text-gray-400">Nenhuma música cadastrada</p>
                  <p className="text-gray-500 text-sm">Configure a playlist no painel administrativo</p>
                </div>
              ) : (
                fullPlaylist.map((t, idx) => (
                  <motion.div
                    key={t.id || t.youtubeVideoId || idx}
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: idx * 0.02 }}
                    className="p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                        <span className="text-blue-300 text-sm font-medium">
                          #{(t.position && t.position > 0) ? t.position : (idx + 1)}
                        </span>
                      </div>
                      <img
                        src={t.thumbnailUrl || `https://img.youtube.com/vi/${t.youtubeVideoId}/mqdefault.jpg`}
                        alt={t.title}
                        className="w-14 h-10 rounded object-cover shadow-md"
                      />
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-medium truncate">{t.title}</div>
                        <div className="text-purple-200 text-sm truncate">{t.artist}</div>
                        <div className="flex items-center gap-3 mt-1 text-xs">
                          <span className="flex items-center gap-1 text-purple-300">
                            <Clock className="w-3 h-3" />
                            {t.formattedDuration || fmtTime(Math.max(0, Math.floor(t.duration || 0)))}
                          </span>
                          {t.isAvailable === false && (
                            <span className="flex items-center gap-1 text-amber-300 bg-amber-500/20 px-2 py-1 rounded">
                              <Eye className="w-3 h-3" />
                              Indisponível
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </motion.section>


        </div>
      </div>
    </div>
  );
};

export default CoverPage;
