"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Suggestion = exports.SuggestionSource = exports.SuggestionStatus = void 0;
const typeorm_1 = require("typeorm");
const Restaurant_1 = require("./Restaurant");
const Playlist_1 = require("./Playlist");
const Vote_1 = require("./Vote");
const User_1 = require("./User");
const ClientSession_1 = require("./ClientSession");
// import { Genre } from "./Genre";
var SuggestionStatus;
(function (SuggestionStatus) {
    SuggestionStatus["PENDING"] = "pending";
    SuggestionStatus["APPROVED"] = "approved";
    SuggestionStatus["REJECTED"] = "rejected";
    SuggestionStatus["PLAYING"] = "playing";
    SuggestionStatus["PLAYED"] = "played";
    SuggestionStatus["SKIPPED"] = "skipped";
    SuggestionStatus["COMPLETED"] = "completed";
    SuggestionStatus["EXPIRED"] = "expired";
})(SuggestionStatus = exports.SuggestionStatus || (exports.SuggestionStatus = {}));
var SuggestionSource;
(function (SuggestionSource) {
    SuggestionSource["CLIENT"] = "client";
    SuggestionSource["ADMIN"] = "admin";
    SuggestionSource["AUTO"] = "auto";
    SuggestionSource["IMPORT"] = "import";
})(SuggestionSource = exports.SuggestionSource || (exports.SuggestionSource = {}));
let Suggestion = class Suggestion {
    // Métodos da instância
    // Calcular score baseado em votos e tempo
    calculateScore() {
        const baseScore = this.upvotes - this.downvotes;
        const timeDecay = this.getTimeDecayFactor();
        return baseScore * timeDecay;
    }
    // Fator de decaimento baseado no tempo
    getTimeDecayFactor() {
        // createdAt pode ser nulo em dados antigos; nesse caso, não aplicar decaimento
        const created = this.createdAt ?? this.updatedAt ?? new Date();
        const createdMs = (() => {
            try {
                const t = new Date(created).getTime();
                return isFinite(t) ? t : Date.now();
            }
            catch {
                return Date.now();
            }
        })();
        const hoursOld = (Date.now() - createdMs) / (1000 * 60 * 60);
        return Math.exp(-hoursOld / 24); // Decai pela metade a cada 24 horas
    }
    // Verificar se pode ser votada
    canBeVoted() {
        return [SuggestionStatus.PENDING, SuggestionStatus.APPROVED].includes(this.status);
    }
    // Verificar se está na fila
    isInQueue() {
        return (this.status === SuggestionStatus.APPROVED && (this.queuePosition || 0) > 0);
    }
    // Obter duração formatada
    getFormattedDuration() {
        if (!this.duration)
            return "N/A";
        const minutes = Math.floor(this.duration / 60);
        const seconds = this.duration % 60;
        return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    }
    // Verificar se é conteúdo explícito
    isExplicit() {
        return this.metadata?.explicit || false;
    }
    // Verificar se é transmissão ao vivo
    isLive() {
        return this.metadata?.live || false;
    }
    // Verificar se é sugestão prioritária (paga)
    isPriorityPaid() {
        return this.isPriority && this.paymentStatus === "paid";
    }
    // Verificar se o pagamento está pendente
    isPaymentPending() {
        return this.isPriority && this.paymentStatus === "pending";
    }
    // Verificar se o pagamento expirou
    isPaymentExpired() {
        return this.isPriority && this.paymentStatus === "expired";
    }
    // Marcar pagamento como pago
    markAsPaid(paymentId) {
        this.isPaid = true;
        this.paidAt = new Date();
        this.paymentId = paymentId;
        this.paymentStatus = "paid";
        // Sugestões pagas são automaticamente aprovadas
        if (this.status === SuggestionStatus.PENDING) {
            this.status = SuggestionStatus.APPROVED;
        }
    }
    // Marcar pagamento como expirado
    markPaymentAsExpired() {
        this.paymentStatus = "expired";
    }
    // Obter URL do YouTube
    getYouTubeUrl() {
        return `https://www.youtube.com/watch?v=${this.youtubeVideoId}`;
    }
    // Obter URL de embed do YouTube
    getYouTubeEmbedUrl() {
        return `https://www.youtube.com/embed/${this.youtubeVideoId}`;
    }
    // Aprovar sugestão
    approve(moderator, reason) {
        this.status = SuggestionStatus.APPROVED;
        this.moderatedAt = new Date();
        this.moderatedBy = moderator;
        this.moderationReason = reason;
    }
    // Rejeitar sugestão
    reject(moderator, reason) {
        this.status = SuggestionStatus.REJECTED;
        this.moderatedAt = new Date();
        this.moderatedBy = moderator;
        this.moderationReason = reason;
    }
    // Marcar como tocando
    startPlaying() {
        this.status = SuggestionStatus.PLAYING;
        this.playedAt = new Date();
    }
    // Marcar como tocada
    finishPlaying(duration) {
        this.status = SuggestionStatus.PLAYED;
        if (duration) {
            this.playDuration = duration;
        }
    }
    // Marcar como pulada
    skip(reason) {
        this.status = SuggestionStatus.SKIPPED;
        this.skipReason = reason;
    }
    // Atualizar posição na fila
    updateQueuePosition(position) {
        this.queuePosition = position;
    }
    // Verificar se precisa de moderação
    needsModeration() {
        return this.status === SuggestionStatus.PENDING;
    }
    // Verificar se foi moderada
    isModerated() {
        return [SuggestionStatus.APPROVED, SuggestionStatus.REJECTED].includes(this.status);
    }
    // Serialização para JSON público
    toPublicJSON() {
        return {
            id: this.id,
            youtubeVideoId: this.youtubeVideoId,
            title: this.title,
            artist: this.artist,
            channelName: this.channelName,
            duration: this.duration,
            formattedDuration: this.getFormattedDuration(),
            thumbnailUrl: this.thumbnailUrl,
            status: this.status,
            voteCount: this.voteCount,
            upvotes: this.upvotes,
            downvotes: this.downvotes,
            votes: this.votes || [],
            queuePosition: this.queuePosition,
            score: this.calculateScore(),
            canBeVoted: this.canBeVoted(),
            isInQueue: this.isInQueue(),
            isExplicit: this.isExplicit(),
            isLive: this.isLive(),
            youtubeUrl: this.getYouTubeUrl(),
            createdAt: this.createdAt,
            // Campos de pagamento PIX
            isPriority: this.isPriority,
            isPaid: this.isPaid,
            paidAt: this.paidAt,
            paymentAmount: this.paymentAmount,
            paymentStatus: this.paymentStatus,
            isPriorityPaid: this.isPriorityPaid(),
            isPaymentPending: this.isPaymentPending(),
            // Campos do cliente
            clientName: this.clientName,
            tableNumber: this.tableNumber,
            metadata: {
                genre: this.metadata?.genre,
                mood: this.metadata?.mood,
                language: this.metadata?.language,
            },
        };
    }
    // Serialização para JSON administrativo
    toAdminJSON() {
        return {
            ...this.toPublicJSON(),
            clientSessionId: this.clientSessionId,
            clientIp: this.clientIp,
            source: this.source,
            moderatedAt: this.moderatedAt,
            moderationReason: this.moderationReason,
            moderatedBy: this.moderatedBy
                ? {
                    id: this.moderatedBy.id,
                    name: this.moderatedBy.name,
                    email: this.moderatedBy.email,
                }
                : null,
            playedAt: this.playedAt,
            playDuration: this.playDuration,
            skipReason: this.skipReason,
            moderationFlags: this.moderationFlags,
            fullMetadata: this.metadata,
        };
    }
};
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)("uuid"),
    __metadata("design:type", String)
], Suggestion.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "youtube_video_id", type: "varchar" }),
    __metadata("design:type", String)
], Suggestion.prototype, "youtubeVideoId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar" }),
    __metadata("design:type", String)
], Suggestion.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "artist", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "genre", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "channel_name", type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "channelName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "integer", nullable: true }),
    __metadata("design:type", Number)
], Suggestion.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "thumbnail_url", type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "thumbnailUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: SuggestionStatus,
        default: SuggestionStatus.PENDING,
    }),
    __metadata("design:type", String)
], Suggestion.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: SuggestionSource,
        default: SuggestionSource.CLIENT,
    }),
    __metadata("design:type", String)
], Suggestion.prototype, "source", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "client_session_id", type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "clientSessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "client_ip", type: "inet", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "clientIp", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "client_user_agent", type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "clientUserAgent", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "vote_count", type: "integer", default: 0 }),
    __metadata("design:type", Number)
], Suggestion.prototype, "voteCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "integer", default: 0 }),
    __metadata("design:type", Number)
], Suggestion.prototype, "upvotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "integer", default: 0 }),
    __metadata("design:type", Number)
], Suggestion.prototype, "downvotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "moderated_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], Suggestion.prototype, "moderatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "moderation_reason", type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "moderationReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "played_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], Suggestion.prototype, "playedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "play_duration", type: "integer", nullable: true }),
    __metadata("design:type", Number)
], Suggestion.prototype, "playDuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "skip_reason", type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "skipReason", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "json", nullable: true }),
    __metadata("design:type", Object)
], Suggestion.prototype, "metadata", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "moderation_flags", type: "json", nullable: true }),
    __metadata("design:type", Object)
], Suggestion.prototype, "moderationFlags", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_priority", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], Suggestion.prototype, "isPriority", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "is_paid", type: "boolean", default: false }),
    __metadata("design:type", Boolean)
], Suggestion.prototype, "isPaid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "paid_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], Suggestion.prototype, "paidAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "payment_amount",
        type: "decimal",
        precision: 10,
        scale: 2,
        nullable: true,
    }),
    __metadata("design:type", Number)
], Suggestion.prototype, "paymentAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "payment_id", type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "paymentId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: "payment_status",
        type: "varchar",
        nullable: true,
    }),
    __metadata("design:type", String)
], Suggestion.prototype, "paymentStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "pix_code", type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "pixCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "queue_position", type: "integer", nullable: true }),
    __metadata("design:type", Number)
], Suggestion.prototype, "queuePosition", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "client_name", type: "varchar", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "clientName", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "client_message", type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "clientMessage", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "table_number", type: "integer", nullable: true }),
    __metadata("design:type", Number)
], Suggestion.prototype, "tableNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "session_id", type: "uuid", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "sessionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "completed_at", type: "timestamp", nullable: true }),
    __metadata("design:type", Date)
], Suggestion.prototype, "completedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "votes_count", type: "integer", default: 0 }),
    __metadata("design:type", Number)
], Suggestion.prototype, "votesCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: "rejection_reason", type: "text", nullable: true }),
    __metadata("design:type", String)
], Suggestion.prototype, "rejectionReason", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Restaurant_1.Restaurant, (restaurant) => restaurant.suggestions, {
        onDelete: "CASCADE",
    }),
    (0, typeorm_1.JoinColumn)({ name: "restaurant_id" }),
    __metadata("design:type", Restaurant_1.Restaurant)
], Suggestion.prototype, "restaurant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Playlist_1.Playlist, (playlist) => playlist.suggestions, {
        nullable: true,
        onDelete: "SET NULL",
    }),
    (0, typeorm_1.JoinColumn)({ name: "playlist_id" }),
    __metadata("design:type", Playlist_1.Playlist)
], Suggestion.prototype, "playlist", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, {
        nullable: true,
        onDelete: "SET NULL",
    }),
    (0, typeorm_1.JoinColumn)({ name: "moderated_by" }),
    __metadata("design:type", User_1.User)
], Suggestion.prototype, "moderatedBy", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => User_1.User, {
        nullable: true,
        onDelete: "SET NULL",
    }),
    (0, typeorm_1.JoinColumn)({ name: "suggested_by" }),
    __metadata("design:type", User_1.User)
], Suggestion.prototype, "suggestedBy", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Vote_1.Vote, (vote) => vote.suggestion),
    __metadata("design:type", Array)
], Suggestion.prototype, "votes", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => ClientSession_1.ClientSession, {
        nullable: true,
        onDelete: "SET NULL",
    })
    // Importante: relacionar pela coluna session_id (UUID), não client_session_id (token string)
    ,
    (0, typeorm_1.JoinColumn)({ name: "session_id" }),
    __metadata("design:type", ClientSession_1.ClientSession)
], Suggestion.prototype, "clientSession", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: "created_at" }),
    __metadata("design:type", Date)
], Suggestion.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: "updated_at" }),
    __metadata("design:type", Date)
], Suggestion.prototype, "updatedAt", void 0);
Suggestion = __decorate([
    (0, typeorm_1.Entity)("suggestions")
], Suggestion);
exports.Suggestion = Suggestion;
//# sourceMappingURL=Suggestion.js.map