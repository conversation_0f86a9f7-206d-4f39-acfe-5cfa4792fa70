var ni=Object.defineProperty;var ii=(r,t,s)=>t in r?ni(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s;var _e=(r,t,s)=>(ii(r,typeof t!="symbol"?t+"":t,s),s);import{r as d,b as oi,c as li,a as ce,g as Js}from"./vendor-66b0ef43.js";import{u as ps,L as Bt,R as Hr,a as be,b as ci,c as At,N as Ft,d as Kr,B as di}from"./router-f729e475.js";import{A as et,m as B,L as br,X as jt,B as Mt,M as Yr,a as Xs,E as Gr,b as xs,c as Ce,S as wa,P as ot,d as Yt,e as ui,T as Je,Q as os,U as yt,f as Nt,H as Jr,g as hi,h as qs,V as mi,R as Xe,i as ls,j as ja,k as Xr,l as pi,n as Zr,o as sn,p as xi,q as rn,D as ea,r as an,s as rs,t as nn,C as zt,u as on,v as fi,G as gi,w as yi,x as Wt,y as ln,z as cn,F as bi,I as Zs,J as As,K as Pt,W as vi,N as wi,O as Qt,Y as dn,Z as ji,_ as Ni,$ as ki,a0 as ta,a1 as un,a2 as er,a3 as Na,a4 as ka,a5 as as,a6 as Si,a7 as Ci,a8 as Ei,a9 as hn,aa as mn,ab as sa,ac as Bs,ad as Ri,ae as vr,af as Pi,ag as Ii,ah as Ai,ai as cr,aj as _i,ak as Ti,al as Sa,am as Ca,an as Oi}from"./ui-d6218fb3.js";import{_ as Vi,a as Di,v as dr}from"./utils-08f61814.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))a(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const c of i.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&a(c)}).observe(document,{childList:!0,subtree:!0});function s(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(n){if(n.ep)return;n.ep=!0;const i=s(n);fetch(n.href,i)}})();var pn={exports:{}},tr={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $i=d,Li=Symbol.for("react.element"),Fi=Symbol.for("react.fragment"),Mi=Object.prototype.hasOwnProperty,Ui=$i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,qi={key:!0,ref:!0,__self:!0,__source:!0};function xn(r,t,s){var a,n={},i=null,c=null;s!==void 0&&(i=""+s),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(c=t.ref);for(a in t)Mi.call(t,a)&&!qi.hasOwnProperty(a)&&(n[a]=t[a]);if(r&&r.defaultProps)for(a in t=r.defaultProps,t)n[a]===void 0&&(n[a]=t[a]);return{$$typeof:Li,type:r,key:i,ref:c,props:n,_owner:Ui.current}}tr.Fragment=Fi;tr.jsx=xn;tr.jsxs=xn;pn.exports=tr;var e=pn.exports,wr={},Ea=oi;wr.createRoot=Ea.createRoot,wr.hydrateRoot=Ea.hydrateRoot;function Gt(r,t){r.prototype=Object.create(t.prototype),r.prototype.constructor=r,Vi(r,t)}var Jt=function(){function r(){this.listeners=[]}var t=r.prototype;return t.subscribe=function(a){var n=this,i=a||function(){};return this.listeners.push(i),this.onSubscribe(),function(){n.listeners=n.listeners.filter(function(c){return c!==i}),n.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},r}();function he(){return he=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var a in s)({}).hasOwnProperty.call(s,a)&&(r[a]=s[a])}return r},he.apply(null,arguments)}var Qs=typeof window>"u";function Ke(){}function Bi(r,t){return typeof r=="function"?r(t):r}function jr(r){return typeof r=="number"&&r>=0&&r!==1/0}function zs(r){return Array.isArray(r)?r:[r]}function fn(r,t){return Math.max(r+(t||0)-Date.now(),0)}function _s(r,t,s){return fs(r)?typeof t=="function"?he({},s,{queryKey:r,queryFn:t}):he({},t,{queryKey:r}):r}function Qi(r,t,s){return fs(r)?typeof t=="function"?he({},s,{mutationKey:r,mutationFn:t}):he({},t,{mutationKey:r}):typeof r=="function"?he({},t,{mutationFn:r}):he({},r)}function Et(r,t,s){return fs(r)?[he({},t,{queryKey:r}),s]:[r||{},t]}function zi(r,t){if(r===!0&&t===!0||r==null&&t==null)return"all";if(r===!1&&t===!1)return"none";var s=r??!t;return s?"active":"inactive"}function Ra(r,t){var s=r.active,a=r.exact,n=r.fetching,i=r.inactive,c=r.predicate,o=r.queryKey,l=r.stale;if(fs(o)){if(a){if(t.queryHash!==ra(o,t.options))return!1}else if(!Ws(t.queryKey,o))return!1}var h=zi(s,i);if(h==="none")return!1;if(h!=="all"){var u=t.isActive();if(h==="active"&&!u||h==="inactive"&&u)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||typeof n=="boolean"&&t.isFetching()!==n||c&&!c(t))}function Pa(r,t){var s=r.exact,a=r.fetching,n=r.predicate,i=r.mutationKey;if(fs(i)){if(!t.options.mutationKey)return!1;if(s){if($t(t.options.mutationKey)!==$t(i))return!1}else if(!Ws(t.options.mutationKey,i))return!1}return!(typeof a=="boolean"&&t.state.status==="loading"!==a||n&&!n(t))}function ra(r,t){var s=(t==null?void 0:t.queryKeyHashFn)||$t;return s(r)}function $t(r){var t=zs(r);return Wi(t)}function Wi(r){return JSON.stringify(r,function(t,s){return Nr(s)?Object.keys(s).sort().reduce(function(a,n){return a[n]=s[n],a},{}):s})}function Ws(r,t){return gn(zs(r),zs(t))}function gn(r,t){return r===t?!0:typeof r!=typeof t?!1:r&&t&&typeof r=="object"&&typeof t=="object"?!Object.keys(t).some(function(s){return!gn(r[s],t[s])}):!1}function Hs(r,t){if(r===t)return r;var s=Array.isArray(r)&&Array.isArray(t);if(s||Nr(r)&&Nr(t)){for(var a=s?r.length:Object.keys(r).length,n=s?t:Object.keys(t),i=n.length,c=s?[]:{},o=0,l=0;l<i;l++){var h=s?l:n[l];c[h]=Hs(r[h],t[h]),c[h]===r[h]&&o++}return a===i&&o===a?r:c}return t}function Hi(r,t){if(r&&!t||t&&!r)return!1;for(var s in r)if(r[s]!==t[s])return!1;return!0}function Nr(r){if(!Ia(r))return!1;var t=r.constructor;if(typeof t>"u")return!0;var s=t.prototype;return!(!Ia(s)||!s.hasOwnProperty("isPrototypeOf"))}function Ia(r){return Object.prototype.toString.call(r)==="[object Object]"}function fs(r){return typeof r=="string"||Array.isArray(r)}function Ki(r){return new Promise(function(t){setTimeout(t,r)})}function Aa(r){Promise.resolve().then(r).catch(function(t){return setTimeout(function(){throw t})})}function yn(){if(typeof AbortController=="function")return new AbortController}var Yi=function(r){Gt(t,r);function t(){var a;return a=r.call(this)||this,a.setup=function(n){var i;if(!Qs&&((i=window)!=null&&i.addEventListener)){var c=function(){return n()};return window.addEventListener("visibilitychange",c,!1),window.addEventListener("focus",c,!1),function(){window.removeEventListener("visibilitychange",c),window.removeEventListener("focus",c)}}},a}var s=t.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},s.setEventListener=function(n){var i,c=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(o){typeof o=="boolean"?c.setFocused(o):c.onFocus()})},s.setFocused=function(n){this.focused=n,n&&this.onFocus()},s.onFocus=function(){this.listeners.forEach(function(n){n()})},s.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(Jt),ns=new Yi,Gi=function(r){Gt(t,r);function t(){var a;return a=r.call(this)||this,a.setup=function(n){var i;if(!Qs&&((i=window)!=null&&i.addEventListener)){var c=function(){return n()};return window.addEventListener("online",c,!1),window.addEventListener("offline",c,!1),function(){window.removeEventListener("online",c),window.removeEventListener("offline",c)}}},a}var s=t.prototype;return s.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},s.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},s.setEventListener=function(n){var i,c=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(o){typeof o=="boolean"?c.setOnline(o):c.onOnline()})},s.setOnline=function(n){this.online=n,n&&this.onOnline()},s.onOnline=function(){this.listeners.forEach(function(n){n()})},s.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(Jt),Ts=new Gi;function Ji(r){return Math.min(1e3*Math.pow(2,r),3e4)}function Ks(r){return typeof(r==null?void 0:r.cancel)=="function"}var bn=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function Os(r){return r instanceof bn}var vn=function(t){var s=this,a=!1,n,i,c,o;this.abort=t.abort,this.cancel=function(f){return n==null?void 0:n(f)},this.cancelRetry=function(){a=!0},this.continueRetry=function(){a=!1},this.continue=function(){return i==null?void 0:i()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(f,m){c=f,o=m});var l=function(m){s.isResolved||(s.isResolved=!0,t.onSuccess==null||t.onSuccess(m),i==null||i(),c(m))},h=function(m){s.isResolved||(s.isResolved=!0,t.onError==null||t.onError(m),i==null||i(),o(m))},u=function(){return new Promise(function(m){i=m,s.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){i=void 0,s.isPaused=!1,t.onContinue==null||t.onContinue()})},p=function f(){if(!s.isResolved){var m;try{m=t.fn()}catch(x){m=Promise.reject(x)}n=function(g){if(!s.isResolved&&(h(new bn(g)),s.abort==null||s.abort(),Ks(m)))try{m.cancel()}catch{}},s.isTransportCancelable=Ks(m),Promise.resolve(m).then(l).catch(function(x){var g,w;if(!s.isResolved){var b=(g=t.retry)!=null?g:3,k=(w=t.retryDelay)!=null?w:Ji,S=typeof k=="function"?k(s.failureCount,x):k,J=b===!0||typeof b=="number"&&s.failureCount<b||typeof b=="function"&&b(s.failureCount,x);if(a||!J){h(x);return}s.failureCount++,t.onFail==null||t.onFail(s.failureCount,x),Ki(S).then(function(){if(!ns.isFocused()||!Ts.isOnline())return u()}).then(function(){a?h(x):f()})}})}};p()},Xi=function(){function r(){this.queue=[],this.transactions=0,this.notifyFn=function(s){s()},this.batchNotifyFn=function(s){s()}}var t=r.prototype;return t.batch=function(a){var n;this.transactions++;try{n=a()}finally{this.transactions--,this.transactions||this.flush()}return n},t.schedule=function(a){var n=this;this.transactions?this.queue.push(a):Aa(function(){n.notifyFn(a)})},t.batchCalls=function(a){var n=this;return function(){for(var i=arguments.length,c=new Array(i),o=0;o<i;o++)c[o]=arguments[o];n.schedule(function(){a.apply(void 0,c)})}},t.flush=function(){var a=this,n=this.queue;this.queue=[],n.length&&Aa(function(){a.batchNotifyFn(function(){n.forEach(function(i){a.notifyFn(i)})})})},t.setNotifyFunction=function(a){this.notifyFn=a},t.setBatchNotifyFunction=function(a){this.batchNotifyFn=a},r}(),Oe=new Xi,wn=console;function Ys(){return wn}function Zi(r){wn=r}var eo=function(){function r(s){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=s.defaultOptions,this.setOptions(s.options),this.observers=[],this.cache=s.cache,this.queryKey=s.queryKey,this.queryHash=s.queryHash,this.initialState=s.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=s.meta,this.scheduleGc()}var t=r.prototype;return t.setOptions=function(a){var n;this.options=he({},this.defaultOptions,a),this.meta=a==null?void 0:a.meta,this.cacheTime=Math.max(this.cacheTime||0,(n=this.options.cacheTime)!=null?n:5*60*1e3)},t.setDefaultOptions=function(a){this.defaultOptions=a},t.scheduleGc=function(){var a=this;this.clearGcTimeout(),jr(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){a.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(a,n){var i,c,o=this.state.data,l=Bi(a,o);return(i=(c=this.options).isDataEqual)!=null&&i.call(c,o,l)?l=o:this.options.structuralSharing!==!1&&(l=Hs(o,l)),this.dispatch({data:l,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt}),l},t.setState=function(a,n){this.dispatch({type:"setState",state:a,setStateOptions:n})},t.cancel=function(a){var n,i=this.promise;return(n=this.retryer)==null||n.cancel(a),i?i.then(Ke).catch(Ke):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(a){return a.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(a){return a.getCurrentResult().isStale})},t.isStaleByTime=function(a){return a===void 0&&(a=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!fn(this.state.dataUpdatedAt,a)},t.onFocus=function(){var a,n=this.observers.find(function(i){return i.shouldFetchOnWindowFocus()});n&&n.refetch(),(a=this.retryer)==null||a.continue()},t.onOnline=function(){var a,n=this.observers.find(function(i){return i.shouldFetchOnReconnect()});n&&n.refetch(),(a=this.retryer)==null||a.continue()},t.addObserver=function(a){this.observers.indexOf(a)===-1&&(this.observers.push(a),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:a}))},t.removeObserver=function(a){this.observers.indexOf(a)!==-1&&(this.observers=this.observers.filter(function(n){return n!==a}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:a}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(a,n){var i=this,c,o,l;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var h;return(h=this.retryer)==null||h.continueRetry(),this.promise}}if(a&&this.setOptions(a),!this.options.queryFn){var u=this.observers.find(function(k){return k.options.queryFn});u&&this.setOptions(u.options)}var p=zs(this.queryKey),f=yn(),m={queryKey:p,pageParam:void 0,meta:this.meta};Object.defineProperty(m,"signal",{enumerable:!0,get:function(){if(f)return i.abortSignalConsumed=!0,f.signal}});var x=function(){return i.options.queryFn?(i.abortSignalConsumed=!1,i.options.queryFn(m)):Promise.reject("Missing queryFn")},g={fetchOptions:n,options:this.options,queryKey:p,state:this.state,fetchFn:x,meta:this.meta};if((c=this.options.behavior)!=null&&c.onFetch){var w;(w=this.options.behavior)==null||w.onFetch(g)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((o=g.fetchOptions)==null?void 0:o.meta)){var b;this.dispatch({type:"fetch",meta:(b=g.fetchOptions)==null?void 0:b.meta})}return this.retryer=new vn({fn:g.fetchFn,abort:f==null||(l=f.abort)==null?void 0:l.bind(f),onSuccess:function(S){i.setData(S),i.cache.config.onSuccess==null||i.cache.config.onSuccess(S,i),i.cacheTime===0&&i.optionalRemove()},onError:function(S){Os(S)&&S.silent||i.dispatch({type:"error",error:S}),Os(S)||(i.cache.config.onError==null||i.cache.config.onError(S,i),Ys().error(S)),i.cacheTime===0&&i.optionalRemove()},onFail:function(){i.dispatch({type:"failed"})},onPause:function(){i.dispatch({type:"pause"})},onContinue:function(){i.dispatch({type:"continue"})},retry:g.options.retry,retryDelay:g.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(a){var n=this;this.state=this.reducer(this.state,a),Oe.batch(function(){n.observers.forEach(function(i){i.onQueryUpdate(a)}),n.cache.notify({query:n,type:"queryUpdated",action:a})})},t.getDefaultState=function(a){var n=typeof a.initialData=="function"?a.initialData():a.initialData,i=typeof a.initialData<"u",c=i?typeof a.initialDataUpdatedAt=="function"?a.initialDataUpdatedAt():a.initialDataUpdatedAt:0,o=typeof n<"u";return{data:n,dataUpdateCount:0,dataUpdatedAt:o?c??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:o?"success":"idle"}},t.reducer=function(a,n){var i,c;switch(n.type){case"failed":return he({},a,{fetchFailureCount:a.fetchFailureCount+1});case"pause":return he({},a,{isPaused:!0});case"continue":return he({},a,{isPaused:!1});case"fetch":return he({},a,{fetchFailureCount:0,fetchMeta:(i=n.meta)!=null?i:null,isFetching:!0,isPaused:!1},!a.dataUpdatedAt&&{error:null,status:"loading"});case"success":return he({},a,{data:n.data,dataUpdateCount:a.dataUpdateCount+1,dataUpdatedAt:(c=n.dataUpdatedAt)!=null?c:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=n.error;return Os(o)&&o.revert&&this.revertState?he({},this.revertState):he({},a,{error:o,errorUpdateCount:a.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:a.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return he({},a,{isInvalidated:!0});case"setState":return he({},a,n.state);default:return a}},r}(),to=function(r){Gt(t,r);function t(a){var n;return n=r.call(this)||this,n.config=a||{},n.queries=[],n.queriesMap={},n}var s=t.prototype;return s.build=function(n,i,c){var o,l=i.queryKey,h=(o=i.queryHash)!=null?o:ra(l,i),u=this.get(h);return u||(u=new eo({cache:this,queryKey:l,queryHash:h,options:n.defaultQueryOptions(i),state:c,defaultOptions:n.getQueryDefaults(l),meta:i.meta}),this.add(u)),u},s.add=function(n){this.queriesMap[n.queryHash]||(this.queriesMap[n.queryHash]=n,this.queries.push(n),this.notify({type:"queryAdded",query:n}))},s.remove=function(n){var i=this.queriesMap[n.queryHash];i&&(n.destroy(),this.queries=this.queries.filter(function(c){return c!==n}),i===n&&delete this.queriesMap[n.queryHash],this.notify({type:"queryRemoved",query:n}))},s.clear=function(){var n=this;Oe.batch(function(){n.queries.forEach(function(i){n.remove(i)})})},s.get=function(n){return this.queriesMap[n]},s.getAll=function(){return this.queries},s.find=function(n,i){var c=Et(n,i),o=c[0];return typeof o.exact>"u"&&(o.exact=!0),this.queries.find(function(l){return Ra(o,l)})},s.findAll=function(n,i){var c=Et(n,i),o=c[0];return Object.keys(o).length>0?this.queries.filter(function(l){return Ra(o,l)}):this.queries},s.notify=function(n){var i=this;Oe.batch(function(){i.listeners.forEach(function(c){c(n)})})},s.onFocus=function(){var n=this;Oe.batch(function(){n.queries.forEach(function(i){i.onFocus()})})},s.onOnline=function(){var n=this;Oe.batch(function(){n.queries.forEach(function(i){i.onOnline()})})},t}(Jt),so=function(){function r(s){this.options=he({},s.defaultOptions,s.options),this.mutationId=s.mutationId,this.mutationCache=s.mutationCache,this.observers=[],this.state=s.state||jn(),this.meta=s.meta}var t=r.prototype;return t.setState=function(a){this.dispatch({type:"setState",state:a})},t.addObserver=function(a){this.observers.indexOf(a)===-1&&this.observers.push(a)},t.removeObserver=function(a){this.observers=this.observers.filter(function(n){return n!==a})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(Ke).catch(Ke)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var a=this,n,i=this.state.status==="loading",c=Promise.resolve();return i||(this.dispatch({type:"loading",variables:this.options.variables}),c=c.then(function(){a.mutationCache.config.onMutate==null||a.mutationCache.config.onMutate(a.state.variables,a)}).then(function(){return a.options.onMutate==null?void 0:a.options.onMutate(a.state.variables)}).then(function(o){o!==a.state.context&&a.dispatch({type:"loading",context:o,variables:a.state.variables})})),c.then(function(){return a.executeMutation()}).then(function(o){n=o,a.mutationCache.config.onSuccess==null||a.mutationCache.config.onSuccess(n,a.state.variables,a.state.context,a)}).then(function(){return a.options.onSuccess==null?void 0:a.options.onSuccess(n,a.state.variables,a.state.context)}).then(function(){return a.options.onSettled==null?void 0:a.options.onSettled(n,null,a.state.variables,a.state.context)}).then(function(){return a.dispatch({type:"success",data:n}),n}).catch(function(o){return a.mutationCache.config.onError==null||a.mutationCache.config.onError(o,a.state.variables,a.state.context,a),Ys().error(o),Promise.resolve().then(function(){return a.options.onError==null?void 0:a.options.onError(o,a.state.variables,a.state.context)}).then(function(){return a.options.onSettled==null?void 0:a.options.onSettled(void 0,o,a.state.variables,a.state.context)}).then(function(){throw a.dispatch({type:"error",error:o}),o})})},t.executeMutation=function(){var a=this,n;return this.retryer=new vn({fn:function(){return a.options.mutationFn?a.options.mutationFn(a.state.variables):Promise.reject("No mutationFn found")},onFail:function(){a.dispatch({type:"failed"})},onPause:function(){a.dispatch({type:"pause"})},onContinue:function(){a.dispatch({type:"continue"})},retry:(n=this.options.retry)!=null?n:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(a){var n=this;this.state=ro(this.state,a),Oe.batch(function(){n.observers.forEach(function(i){i.onMutationUpdate(a)}),n.mutationCache.notify(n)})},r}();function jn(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function ro(r,t){switch(t.type){case"failed":return he({},r,{failureCount:r.failureCount+1});case"pause":return he({},r,{isPaused:!0});case"continue":return he({},r,{isPaused:!1});case"loading":return he({},r,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return he({},r,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return he({},r,{data:void 0,error:t.error,failureCount:r.failureCount+1,isPaused:!1,status:"error"});case"setState":return he({},r,t.state);default:return r}}var ao=function(r){Gt(t,r);function t(a){var n;return n=r.call(this)||this,n.config=a||{},n.mutations=[],n.mutationId=0,n}var s=t.prototype;return s.build=function(n,i,c){var o=new so({mutationCache:this,mutationId:++this.mutationId,options:n.defaultMutationOptions(i),state:c,defaultOptions:i.mutationKey?n.getMutationDefaults(i.mutationKey):void 0,meta:i.meta});return this.add(o),o},s.add=function(n){this.mutations.push(n),this.notify(n)},s.remove=function(n){this.mutations=this.mutations.filter(function(i){return i!==n}),n.cancel(),this.notify(n)},s.clear=function(){var n=this;Oe.batch(function(){n.mutations.forEach(function(i){n.remove(i)})})},s.getAll=function(){return this.mutations},s.find=function(n){return typeof n.exact>"u"&&(n.exact=!0),this.mutations.find(function(i){return Pa(n,i)})},s.findAll=function(n){return this.mutations.filter(function(i){return Pa(n,i)})},s.notify=function(n){var i=this;Oe.batch(function(){i.listeners.forEach(function(c){c(n)})})},s.onFocus=function(){this.resumePausedMutations()},s.onOnline=function(){this.resumePausedMutations()},s.resumePausedMutations=function(){var n=this.mutations.filter(function(i){return i.state.isPaused});return Oe.batch(function(){return n.reduce(function(i,c){return i.then(function(){return c.continue().catch(Ke)})},Promise.resolve())})},t}(Jt);function no(){return{onFetch:function(t){t.fetchFn=function(){var s,a,n,i,c,o,l=(s=t.fetchOptions)==null||(a=s.meta)==null?void 0:a.refetchPage,h=(n=t.fetchOptions)==null||(i=n.meta)==null?void 0:i.fetchMore,u=h==null?void 0:h.pageParam,p=(h==null?void 0:h.direction)==="forward",f=(h==null?void 0:h.direction)==="backward",m=((c=t.state.data)==null?void 0:c.pages)||[],x=((o=t.state.data)==null?void 0:o.pageParams)||[],g=yn(),w=g==null?void 0:g.signal,b=x,k=!1,S=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},J=function(z,le,X,U){return b=U?[le].concat(b):[].concat(b,[le]),U?[X].concat(z):[].concat(z,[X])},Z=function(z,le,X,U){if(k)return Promise.reject("Cancelled");if(typeof X>"u"&&!le&&z.length)return Promise.resolve(z);var I={queryKey:t.queryKey,signal:w,pageParam:X,meta:t.meta},ee=S(I),Y=Promise.resolve(ee).then(function(te){return J(z,X,te,U)});if(Ks(ee)){var ye=Y;ye.cancel=ee.cancel}return Y},se;if(!m.length)se=Z([]);else if(p){var N=typeof u<"u",$=N?u:_a(t.options,m);se=Z(m,N,$)}else if(f){var W=typeof u<"u",P=W?u:io(t.options,m);se=Z(m,W,P,!0)}else(function(){b=[];var A=typeof t.options.getNextPageParam>"u",z=l&&m[0]?l(m[0],0,m):!0;se=z?Z([],A,x[0]):Promise.resolve(J([],x[0],m[0]));for(var le=function(I){se=se.then(function(ee){var Y=l&&m[I]?l(m[I],I,m):!0;if(Y){var ye=A?x[I]:_a(t.options,ee);return Z(ee,A,ye)}return Promise.resolve(J(ee,x[I],m[I]))})},X=1;X<m.length;X++)le(X)})();var H=se.then(function(A){return{pages:A,pageParams:b}}),re=H;return re.cancel=function(){k=!0,g==null||g.abort(),Ks(se)&&se.cancel()},H}}}}function _a(r,t){return r.getNextPageParam==null?void 0:r.getNextPageParam(t[t.length-1],t)}function io(r,t){return r.getPreviousPageParam==null?void 0:r.getPreviousPageParam(t[0],t)}var oo=function(){function r(s){s===void 0&&(s={}),this.queryCache=s.queryCache||new to,this.mutationCache=s.mutationCache||new ao,this.defaultOptions=s.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=r.prototype;return t.mount=function(){var a=this;this.unsubscribeFocus=ns.subscribe(function(){ns.isFocused()&&Ts.isOnline()&&(a.mutationCache.onFocus(),a.queryCache.onFocus())}),this.unsubscribeOnline=Ts.subscribe(function(){ns.isFocused()&&Ts.isOnline()&&(a.mutationCache.onOnline(),a.queryCache.onOnline())})},t.unmount=function(){var a,n;(a=this.unsubscribeFocus)==null||a.call(this),(n=this.unsubscribeOnline)==null||n.call(this)},t.isFetching=function(a,n){var i=Et(a,n),c=i[0];return c.fetching=!0,this.queryCache.findAll(c).length},t.isMutating=function(a){return this.mutationCache.findAll(he({},a,{fetching:!0})).length},t.getQueryData=function(a,n){var i;return(i=this.queryCache.find(a,n))==null?void 0:i.state.data},t.getQueriesData=function(a){return this.getQueryCache().findAll(a).map(function(n){var i=n.queryKey,c=n.state,o=c.data;return[i,o]})},t.setQueryData=function(a,n,i){var c=_s(a),o=this.defaultQueryOptions(c);return this.queryCache.build(this,o).setData(n,i)},t.setQueriesData=function(a,n,i){var c=this;return Oe.batch(function(){return c.getQueryCache().findAll(a).map(function(o){var l=o.queryKey;return[l,c.setQueryData(l,n,i)]})})},t.getQueryState=function(a,n){var i;return(i=this.queryCache.find(a,n))==null?void 0:i.state},t.removeQueries=function(a,n){var i=Et(a,n),c=i[0],o=this.queryCache;Oe.batch(function(){o.findAll(c).forEach(function(l){o.remove(l)})})},t.resetQueries=function(a,n,i){var c=this,o=Et(a,n,i),l=o[0],h=o[1],u=this.queryCache,p=he({},l,{active:!0});return Oe.batch(function(){return u.findAll(l).forEach(function(f){f.reset()}),c.refetchQueries(p,h)})},t.cancelQueries=function(a,n,i){var c=this,o=Et(a,n,i),l=o[0],h=o[1],u=h===void 0?{}:h;typeof u.revert>"u"&&(u.revert=!0);var p=Oe.batch(function(){return c.queryCache.findAll(l).map(function(f){return f.cancel(u)})});return Promise.all(p).then(Ke).catch(Ke)},t.invalidateQueries=function(a,n,i){var c,o,l,h=this,u=Et(a,n,i),p=u[0],f=u[1],m=he({},p,{active:(c=(o=p.refetchActive)!=null?o:p.active)!=null?c:!0,inactive:(l=p.refetchInactive)!=null?l:!1});return Oe.batch(function(){return h.queryCache.findAll(p).forEach(function(x){x.invalidate()}),h.refetchQueries(m,f)})},t.refetchQueries=function(a,n,i){var c=this,o=Et(a,n,i),l=o[0],h=o[1],u=Oe.batch(function(){return c.queryCache.findAll(l).map(function(f){return f.fetch(void 0,he({},h,{meta:{refetchPage:l==null?void 0:l.refetchPage}}))})}),p=Promise.all(u).then(Ke);return h!=null&&h.throwOnError||(p=p.catch(Ke)),p},t.fetchQuery=function(a,n,i){var c=_s(a,n,i),o=this.defaultQueryOptions(c);typeof o.retry>"u"&&(o.retry=!1);var l=this.queryCache.build(this,o);return l.isStaleByTime(o.staleTime)?l.fetch(o):Promise.resolve(l.state.data)},t.prefetchQuery=function(a,n,i){return this.fetchQuery(a,n,i).then(Ke).catch(Ke)},t.fetchInfiniteQuery=function(a,n,i){var c=_s(a,n,i);return c.behavior=no(),this.fetchQuery(c)},t.prefetchInfiniteQuery=function(a,n,i){return this.fetchInfiniteQuery(a,n,i).then(Ke).catch(Ke)},t.cancelMutations=function(){var a=this,n=Oe.batch(function(){return a.mutationCache.getAll().map(function(i){return i.cancel()})});return Promise.all(n).then(Ke).catch(Ke)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(a){return this.mutationCache.build(this,a).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(a){this.defaultOptions=a},t.setQueryDefaults=function(a,n){var i=this.queryDefaults.find(function(c){return $t(a)===$t(c.queryKey)});i?i.defaultOptions=n:this.queryDefaults.push({queryKey:a,defaultOptions:n})},t.getQueryDefaults=function(a){var n;return a?(n=this.queryDefaults.find(function(i){return Ws(a,i.queryKey)}))==null?void 0:n.defaultOptions:void 0},t.setMutationDefaults=function(a,n){var i=this.mutationDefaults.find(function(c){return $t(a)===$t(c.mutationKey)});i?i.defaultOptions=n:this.mutationDefaults.push({mutationKey:a,defaultOptions:n})},t.getMutationDefaults=function(a){var n;return a?(n=this.mutationDefaults.find(function(i){return Ws(a,i.mutationKey)}))==null?void 0:n.defaultOptions:void 0},t.defaultQueryOptions=function(a){if(a!=null&&a._defaulted)return a;var n=he({},this.defaultOptions.queries,this.getQueryDefaults(a==null?void 0:a.queryKey),a,{_defaulted:!0});return!n.queryHash&&n.queryKey&&(n.queryHash=ra(n.queryKey,n)),n},t.defaultQueryObserverOptions=function(a){return this.defaultQueryOptions(a)},t.defaultMutationOptions=function(a){return a!=null&&a._defaulted?a:he({},this.defaultOptions.mutations,this.getMutationDefaults(a==null?void 0:a.mutationKey),a,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},r}(),lo=function(r){Gt(t,r);function t(a,n){var i;return i=r.call(this)||this,i.client=a,i.options=n,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(n),i}var s=t.prototype;return s.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},s.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),Ta(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},s.onUnsubscribe=function(){this.listeners.length||this.destroy()},s.shouldFetchOnReconnect=function(){return kr(this.currentQuery,this.options,this.options.refetchOnReconnect)},s.shouldFetchOnWindowFocus=function(){return kr(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},s.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},s.setOptions=function(n,i){var c=this.options,o=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(n),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=c.queryKey),this.updateQuery();var l=this.hasListeners();l&&Oa(this.currentQuery,o,this.options,c)&&this.executeFetch(),this.updateResult(i),l&&(this.currentQuery!==o||this.options.enabled!==c.enabled||this.options.staleTime!==c.staleTime)&&this.updateStaleTimeout();var h=this.computeRefetchInterval();l&&(this.currentQuery!==o||this.options.enabled!==c.enabled||h!==this.currentRefetchInterval)&&this.updateRefetchInterval(h)},s.getOptimisticResult=function(n){var i=this.client.defaultQueryObserverOptions(n),c=this.client.getQueryCache().build(this.client,i);return this.createResult(c,i)},s.getCurrentResult=function(){return this.currentResult},s.trackResult=function(n,i){var c=this,o={},l=function(u){c.trackedProps.includes(u)||c.trackedProps.push(u)};return Object.keys(n).forEach(function(h){Object.defineProperty(o,h,{configurable:!1,enumerable:!0,get:function(){return l(h),n[h]}})}),(i.useErrorBoundary||i.suspense)&&l("error"),o},s.getNextResult=function(n){var i=this;return new Promise(function(c,o){var l=i.subscribe(function(h){h.isFetching||(l(),h.isError&&(n!=null&&n.throwOnError)?o(h.error):c(h))})})},s.getCurrentQuery=function(){return this.currentQuery},s.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},s.refetch=function(n){return this.fetch(he({},n,{meta:{refetchPage:n==null?void 0:n.refetchPage}}))},s.fetchOptimistic=function(n){var i=this,c=this.client.defaultQueryObserverOptions(n),o=this.client.getQueryCache().build(this.client,c);return o.fetch().then(function(){return i.createResult(o,c)})},s.fetch=function(n){var i=this;return this.executeFetch(n).then(function(){return i.updateResult(),i.currentResult})},s.executeFetch=function(n){this.updateQuery();var i=this.currentQuery.fetch(this.options,n);return n!=null&&n.throwOnError||(i=i.catch(Ke)),i},s.updateStaleTimeout=function(){var n=this;if(this.clearStaleTimeout(),!(Qs||this.currentResult.isStale||!jr(this.options.staleTime))){var i=fn(this.currentResult.dataUpdatedAt,this.options.staleTime),c=i+1;this.staleTimeoutId=setTimeout(function(){n.currentResult.isStale||n.updateResult()},c)}},s.computeRefetchInterval=function(){var n;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(n=this.options.refetchInterval)!=null?n:!1},s.updateRefetchInterval=function(n){var i=this;this.clearRefetchInterval(),this.currentRefetchInterval=n,!(Qs||this.options.enabled===!1||!jr(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(i.options.refetchIntervalInBackground||ns.isFocused())&&i.executeFetch()},this.currentRefetchInterval))},s.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},s.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},s.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},s.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},s.createResult=function(n,i){var c=this.currentQuery,o=this.options,l=this.currentResult,h=this.currentResultState,u=this.currentResultOptions,p=n!==c,f=p?n.state:this.currentQueryInitialState,m=p?this.currentResult:this.previousQueryResult,x=n.state,g=x.dataUpdatedAt,w=x.error,b=x.errorUpdatedAt,k=x.isFetching,S=x.status,J=!1,Z=!1,se;if(i.optimisticResults){var N=this.hasListeners(),$=!N&&Ta(n,i),W=N&&Oa(n,c,i,o);($||W)&&(k=!0,g||(S="loading"))}if(i.keepPreviousData&&!x.dataUpdateCount&&(m!=null&&m.isSuccess)&&S!=="error")se=m.data,g=m.dataUpdatedAt,S=m.status,J=!0;else if(i.select&&typeof x.data<"u")if(l&&x.data===(h==null?void 0:h.data)&&i.select===this.selectFn)se=this.selectResult;else try{this.selectFn=i.select,se=i.select(x.data),i.structuralSharing!==!1&&(se=Hs(l==null?void 0:l.data,se)),this.selectResult=se,this.selectError=null}catch(re){Ys().error(re),this.selectError=re}else se=x.data;if(typeof i.placeholderData<"u"&&typeof se>"u"&&(S==="loading"||S==="idle")){var P;if(l!=null&&l.isPlaceholderData&&i.placeholderData===(u==null?void 0:u.placeholderData))P=l.data;else if(P=typeof i.placeholderData=="function"?i.placeholderData():i.placeholderData,i.select&&typeof P<"u")try{P=i.select(P),i.structuralSharing!==!1&&(P=Hs(l==null?void 0:l.data,P)),this.selectError=null}catch(re){Ys().error(re),this.selectError=re}typeof P<"u"&&(S="success",se=P,Z=!0)}this.selectError&&(w=this.selectError,se=this.selectResult,b=Date.now(),S="error");var H={status:S,isLoading:S==="loading",isSuccess:S==="success",isError:S==="error",isIdle:S==="idle",data:se,dataUpdatedAt:g,error:w,errorUpdatedAt:b,failureCount:x.fetchFailureCount,errorUpdateCount:x.errorUpdateCount,isFetched:x.dataUpdateCount>0||x.errorUpdateCount>0,isFetchedAfterMount:x.dataUpdateCount>f.dataUpdateCount||x.errorUpdateCount>f.errorUpdateCount,isFetching:k,isRefetching:k&&S!=="loading",isLoadingError:S==="error"&&x.dataUpdatedAt===0,isPlaceholderData:Z,isPreviousData:J,isRefetchError:S==="error"&&x.dataUpdatedAt!==0,isStale:aa(n,i),refetch:this.refetch,remove:this.remove};return H},s.shouldNotifyListeners=function(n,i){if(!i)return!0;var c=this.options,o=c.notifyOnChangeProps,l=c.notifyOnChangePropsExclusions;if(!o&&!l||o==="tracked"&&!this.trackedProps.length)return!0;var h=o==="tracked"?this.trackedProps:o;return Object.keys(n).some(function(u){var p=u,f=n[p]!==i[p],m=h==null?void 0:h.some(function(g){return g===u}),x=l==null?void 0:l.some(function(g){return g===u});return f&&!x&&(!h||m)})},s.updateResult=function(n){var i=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!Hi(this.currentResult,i)){var c={cache:!0};(n==null?void 0:n.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,i)&&(c.listeners=!0),this.notify(he({},c,n))}},s.updateQuery=function(){var n=this.client.getQueryCache().build(this.client,this.options);if(n!==this.currentQuery){var i=this.currentQuery;this.currentQuery=n,this.currentQueryInitialState=n.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(i==null||i.removeObserver(this),n.addObserver(this))}},s.onQueryUpdate=function(n){var i={};n.type==="success"?i.onSuccess=!0:n.type==="error"&&!Os(n.error)&&(i.onError=!0),this.updateResult(i),this.hasListeners()&&this.updateTimers()},s.notify=function(n){var i=this;Oe.batch(function(){n.onSuccess?(i.options.onSuccess==null||i.options.onSuccess(i.currentResult.data),i.options.onSettled==null||i.options.onSettled(i.currentResult.data,null)):n.onError&&(i.options.onError==null||i.options.onError(i.currentResult.error),i.options.onSettled==null||i.options.onSettled(void 0,i.currentResult.error)),n.listeners&&i.listeners.forEach(function(c){c(i.currentResult)}),n.cache&&i.client.getQueryCache().notify({query:i.currentQuery,type:"observerResultsUpdated"})})},t}(Jt);function co(r,t){return t.enabled!==!1&&!r.state.dataUpdatedAt&&!(r.state.status==="error"&&t.retryOnMount===!1)}function Ta(r,t){return co(r,t)||r.state.dataUpdatedAt>0&&kr(r,t,t.refetchOnMount)}function kr(r,t,s){if(t.enabled!==!1){var a=typeof s=="function"?s(r):s;return a==="always"||a!==!1&&aa(r,t)}return!1}function Oa(r,t,s,a){return s.enabled!==!1&&(r!==t||a.enabled===!1)&&(!s.suspense||r.state.status!=="error")&&aa(r,s)}function aa(r,t){return r.isStaleByTime(t.staleTime)}var uo=function(r){Gt(t,r);function t(a,n){var i;return i=r.call(this)||this,i.client=a,i.setOptions(n),i.bindMethods(),i.updateResult(),i}var s=t.prototype;return s.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},s.setOptions=function(n){this.options=this.client.defaultMutationOptions(n)},s.onUnsubscribe=function(){if(!this.listeners.length){var n;(n=this.currentMutation)==null||n.removeObserver(this)}},s.onMutationUpdate=function(n){this.updateResult();var i={listeners:!0};n.type==="success"?i.onSuccess=!0:n.type==="error"&&(i.onError=!0),this.notify(i)},s.getCurrentResult=function(){return this.currentResult},s.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},s.mutate=function(n,i){return this.mutateOptions=i,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,he({},this.options,{variables:typeof n<"u"?n:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},s.updateResult=function(){var n=this.currentMutation?this.currentMutation.state:jn(),i=he({},n,{isLoading:n.status==="loading",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=i},s.notify=function(n){var i=this;Oe.batch(function(){i.mutateOptions&&(n.onSuccess?(i.mutateOptions.onSuccess==null||i.mutateOptions.onSuccess(i.currentResult.data,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(i.currentResult.data,null,i.currentResult.variables,i.currentResult.context)):n.onError&&(i.mutateOptions.onError==null||i.mutateOptions.onError(i.currentResult.error,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(void 0,i.currentResult.error,i.currentResult.variables,i.currentResult.context))),n.listeners&&i.listeners.forEach(function(c){c(i.currentResult)})})},t}(Jt),ho=li.unstable_batchedUpdates;Oe.setBatchNotifyFunction(ho);var mo=console;Zi(mo);var Va=ce.createContext(void 0),Nn=ce.createContext(!1);function kn(r){return r&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Va),window.ReactQueryClientContext):Va}var na=function(){var t=ce.useContext(kn(ce.useContext(Nn)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},po=function(t){var s=t.client,a=t.contextSharing,n=a===void 0?!1:a,i=t.children;ce.useEffect(function(){return s.mount(),function(){s.unmount()}},[s]);var c=kn(n);return ce.createElement(Nn.Provider,{value:n},ce.createElement(c.Provider,{value:s},i))};function xo(){var r=!1;return{clearReset:function(){r=!1},reset:function(){r=!0},isReset:function(){return r}}}var fo=ce.createContext(xo()),go=function(){return ce.useContext(fo)};function Sn(r,t,s){return typeof t=="function"?t.apply(void 0,s):typeof t=="boolean"?t:!!r}function yo(r,t,s){var a=ce.useRef(!1),n=ce.useState(0),i=n[1],c=Qi(r,t,s),o=na(),l=ce.useRef();l.current?l.current.setOptions(c):l.current=new uo(o,c);var h=l.current.getCurrentResult();ce.useEffect(function(){a.current=!0;var p=l.current.subscribe(Oe.batchCalls(function(){a.current&&i(function(f){return f+1})}));return function(){a.current=!1,p()}},[]);var u=ce.useCallback(function(p,f){l.current.mutate(p,f).catch(Ke)},[]);if(h.error&&Sn(void 0,l.current.options.useErrorBoundary,[h.error]))throw h.error;return he({},h,{mutate:u,mutateAsync:h.mutate})}function bo(r,t){var s=ce.useRef(!1),a=ce.useState(0),n=a[1],i=na(),c=go(),o=i.defaultQueryObserverOptions(r);o.optimisticResults=!0,o.onError&&(o.onError=Oe.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=Oe.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=Oe.batchCalls(o.onSettled)),o.suspense&&(typeof o.staleTime!="number"&&(o.staleTime=1e3),o.cacheTime===0&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(c.isReset()||(o.retryOnMount=!1));var l=ce.useState(function(){return new t(i,o)}),h=l[0],u=h.getOptimisticResult(o);if(ce.useEffect(function(){s.current=!0,c.clearReset();var p=h.subscribe(Oe.batchCalls(function(){s.current&&n(function(f){return f+1})}));return h.updateResult(),function(){s.current=!1,p()}},[c,h]),ce.useEffect(function(){h.setOptions(o,{listeners:!1})},[o,h]),o.suspense&&u.isLoading)throw h.fetchOptimistic(o).then(function(p){var f=p.data;o.onSuccess==null||o.onSuccess(f),o.onSettled==null||o.onSettled(f,null)}).catch(function(p){c.clearReset(),o.onError==null||o.onError(p),o.onSettled==null||o.onSettled(void 0,p)});if(u.isError&&!c.isReset()&&!u.isFetching&&Sn(o.suspense,o.useErrorBoundary,[u.error,h.getCurrentQuery()]))throw u.error;return o.notifyOnChangeProps==="tracked"&&(u=h.trackResult(u,o)),u}function Vs(r,t,s){var a=_s(r,t,s);return bo(a,lo)}let vo={data:""},wo=r=>typeof window=="object"?((r?r.querySelector("#_goober"):window._goober)||Object.assign((r||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:r||vo,jo=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,No=/\/\*[^]*?\*\/|  +/g,Da=/\n+/g,Rt=(r,t)=>{let s="",a="",n="";for(let i in r){let c=r[i];i[0]=="@"?i[1]=="i"?s=i+" "+c+";":a+=i[1]=="f"?Rt(c,i):i+"{"+Rt(c,i[1]=="k"?"":t)+"}":typeof c=="object"?a+=Rt(c,t?t.replace(/([^,])+/g,o=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,l=>/&/.test(l)?l.replace(/&/g,o):o?o+" "+l:l)):i):c!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=Rt.p?Rt.p(i,c):i+":"+c+";")}return s+(t&&n?t+"{"+n+"}":n)+a},wt={},Cn=r=>{if(typeof r=="object"){let t="";for(let s in r)t+=s+Cn(r[s]);return t}return r},ko=(r,t,s,a,n)=>{let i=Cn(r),c=wt[i]||(wt[i]=(l=>{let h=0,u=11;for(;h<l.length;)u=101*u+l.charCodeAt(h++)>>>0;return"go"+u})(i));if(!wt[c]){let l=i!==r?r:(h=>{let u,p,f=[{}];for(;u=jo.exec(h.replace(No,""));)u[4]?f.shift():u[3]?(p=u[3].replace(Da," ").trim(),f.unshift(f[0][p]=f[0][p]||{})):f[0][u[1]]=u[2].replace(Da," ").trim();return f[0]})(r);wt[c]=Rt(n?{["@keyframes "+c]:l}:l,s?"":"."+c)}let o=s&&wt.g?wt.g:null;return s&&(wt.g=wt[c]),((l,h,u,p)=>{p?h.data=h.data.replace(p,l):h.data.indexOf(l)===-1&&(h.data=u?l+h.data:h.data+l)})(wt[c],t,a,o),c},So=(r,t,s)=>r.reduce((a,n,i)=>{let c=t[i];if(c&&c.call){let o=c(s),l=o&&o.props&&o.props.className||/^go/.test(o)&&o;c=l?"."+l:o&&typeof o=="object"?o.props?"":Rt(o,""):o===!1?"":o}return a+n+(c??"")},"");function sr(r){let t=this||{},s=r.call?r(t.p):r;return ko(s.unshift?s.raw?So(s,[].slice.call(arguments,1),t.p):s.reduce((a,n)=>Object.assign(a,n&&n.call?n(t.p):n),{}):s,wo(t.target),t.g,t.o,t.k)}let En,Sr,Cr;sr.bind({g:1});let kt=sr.bind({k:1});function Co(r,t,s,a){Rt.p=t,En=r,Sr=s,Cr=a}function _t(r,t){let s=this||{};return function(){let a=arguments;function n(i,c){let o=Object.assign({},i),l=o.className||n.className;s.p=Object.assign({theme:Sr&&Sr()},o),s.o=/ *go\d+/.test(l),o.className=sr.apply(s,a)+(l?" "+l:""),t&&(o.ref=c);let h=r;return r[0]&&(h=o.as||r,delete o.as),Cr&&h[0]&&Cr(o),En(h,o)}return t?t(n):n}}var Eo=r=>typeof r=="function",Gs=(r,t)=>Eo(r)?r(t):r,Ro=(()=>{let r=0;return()=>(++r).toString()})(),Rn=(()=>{let r;return()=>{if(r===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");r=!t||t.matches}return r}})(),Po=20,Pn=(r,t)=>{switch(t.type){case 0:return{...r,toasts:[t.toast,...r.toasts].slice(0,Po)};case 1:return{...r,toasts:r.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:s}=t;return Pn(r,{type:r.toasts.find(i=>i.id===s.id)?1:0,toast:s});case 3:let{toastId:a}=t;return{...r,toasts:r.toasts.map(i=>i.id===a||a===void 0?{...i,dismissed:!0,visible:!1}:i)};case 4:return t.toastId===void 0?{...r,toasts:[]}:{...r,toasts:r.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...r,pausedAt:t.time};case 6:let n=t.time-(r.pausedAt||0);return{...r,pausedAt:void 0,toasts:r.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},Ds=[],Lt={toasts:[],pausedAt:void 0},Ut=r=>{Lt=Pn(Lt,r),Ds.forEach(t=>{t(Lt)})},Io={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Ao=(r={})=>{let[t,s]=d.useState(Lt),a=d.useRef(Lt);d.useEffect(()=>(a.current!==Lt&&s(Lt),Ds.push(s),()=>{let i=Ds.indexOf(s);i>-1&&Ds.splice(i,1)}),[]);let n=t.toasts.map(i=>{var c,o,l;return{...r,...r[i.type],...i,removeDelay:i.removeDelay||((c=r[i.type])==null?void 0:c.removeDelay)||(r==null?void 0:r.removeDelay),duration:i.duration||((o=r[i.type])==null?void 0:o.duration)||(r==null?void 0:r.duration)||Io[i.type],style:{...r.style,...(l=r[i.type])==null?void 0:l.style,...i.style}}});return{...t,toasts:n}},_o=(r,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:r,pauseDuration:0,...s,id:(s==null?void 0:s.id)||Ro()}),gs=r=>(t,s)=>{let a=_o(t,r,s);return Ut({type:2,toast:a}),a.id},v=(r,t)=>gs("blank")(r,t);v.error=gs("error");v.success=gs("success");v.loading=gs("loading");v.custom=gs("custom");v.dismiss=r=>{Ut({type:3,toastId:r})};v.remove=r=>Ut({type:4,toastId:r});v.promise=(r,t,s)=>{let a=v.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof r=="function"&&(r=r()),r.then(n=>{let i=t.success?Gs(t.success,n):void 0;return i?v.success(i,{id:a,...s,...s==null?void 0:s.success}):v.dismiss(a),n}).catch(n=>{let i=t.error?Gs(t.error,n):void 0;i?v.error(i,{id:a,...s,...s==null?void 0:s.error}):v.dismiss(a)}),r};var To=(r,t)=>{Ut({type:1,toast:{id:r,height:t}})},Oo=()=>{Ut({type:5,time:Date.now()})},is=new Map,Vo=1e3,Do=(r,t=Vo)=>{if(is.has(r))return;let s=setTimeout(()=>{is.delete(r),Ut({type:4,toastId:r})},t);is.set(r,s)},$o=r=>{let{toasts:t,pausedAt:s}=Ao(r);d.useEffect(()=>{if(s)return;let i=Date.now(),c=t.map(o=>{if(o.duration===1/0)return;let l=(o.duration||0)+o.pauseDuration-(i-o.createdAt);if(l<0){o.visible&&v.dismiss(o.id);return}return setTimeout(()=>v.dismiss(o.id),l)});return()=>{c.forEach(o=>o&&clearTimeout(o))}},[t,s]);let a=d.useCallback(()=>{s&&Ut({type:6,time:Date.now()})},[s]),n=d.useCallback((i,c)=>{let{reverseOrder:o=!1,gutter:l=8,defaultPosition:h}=c||{},u=t.filter(m=>(m.position||h)===(i.position||h)&&m.height),p=u.findIndex(m=>m.id===i.id),f=u.filter((m,x)=>x<p&&m.visible).length;return u.filter(m=>m.visible).slice(...o?[f+1]:[0,f]).reduce((m,x)=>m+(x.height||0)+l,0)},[t]);return d.useEffect(()=>{t.forEach(i=>{if(i.dismissed)Do(i.id,i.removeDelay);else{let c=is.get(i.id);c&&(clearTimeout(c),is.delete(i.id))}})},[t]),{toasts:t,handlers:{updateHeight:To,startPause:Oo,endPause:a,calculateOffset:n}}},Lo=kt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Fo=kt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Mo=kt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Uo=_t("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${r=>r.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Lo} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Fo} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${r=>r.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Mo} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,qo=kt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Bo=_t("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${r=>r.secondary||"#e0e0e0"};
  border-right-color: ${r=>r.primary||"#616161"};
  animation: ${qo} 1s linear infinite;
`,Qo=kt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,zo=kt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Wo=_t("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${r=>r.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Qo} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${zo} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${r=>r.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Ho=_t("div")`
  position: absolute;
`,Ko=_t("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Yo=kt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Go=_t("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Yo} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Jo=({toast:r})=>{let{icon:t,type:s,iconTheme:a}=r;return t!==void 0?typeof t=="string"?d.createElement(Go,null,t):t:s==="blank"?null:d.createElement(Ko,null,d.createElement(Bo,{...a}),s!=="loading"&&d.createElement(Ho,null,s==="error"?d.createElement(Uo,{...a}):d.createElement(Wo,{...a})))},Xo=r=>`
0% {transform: translate3d(0,${r*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Zo=r=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${r*-150}%,-1px) scale(.6); opacity:0;}
`,el="0%{opacity:0;} 100%{opacity:1;}",tl="0%{opacity:1;} 100%{opacity:0;}",sl=_t("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,rl=_t("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,al=(r,t)=>{let s=r.includes("top")?1:-1,[a,n]=Rn()?[el,tl]:[Xo(s),Zo(s)];return{animation:t?`${kt(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${kt(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},nl=d.memo(({toast:r,position:t,style:s,children:a})=>{let n=r.height?al(r.position||t||"top-center",r.visible):{opacity:0},i=d.createElement(Jo,{toast:r}),c=d.createElement(rl,{...r.ariaProps},Gs(r.message,r));return d.createElement(sl,{className:r.className,style:{...n,...s,...r.style}},typeof a=="function"?a({icon:i,message:c}):d.createElement(d.Fragment,null,i,c))});Co(d.createElement);var il=({id:r,className:t,style:s,onHeightUpdate:a,children:n})=>{let i=d.useCallback(c=>{if(c){let o=()=>{let l=c.getBoundingClientRect().height;a(r,l)};o(),new MutationObserver(o).observe(c,{subtree:!0,childList:!0,characterData:!0})}},[r,a]);return d.createElement("div",{ref:i,className:t,style:s},n)},ol=(r,t)=>{let s=r.includes("top"),a=s?{top:0}:{bottom:0},n=r.includes("center")?{justifyContent:"center"}:r.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:Rn()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...a,...n}},ll=sr`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ns=16,In=({reverseOrder:r,position:t="top-center",toastOptions:s,gutter:a,children:n,containerStyle:i,containerClassName:c})=>{let{toasts:o,handlers:l}=$o(s);return d.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Ns,left:Ns,right:Ns,bottom:Ns,pointerEvents:"none",...i},className:c,onMouseEnter:l.startPause,onMouseLeave:l.endPause},o.map(h=>{let u=h.position||t,p=l.calculateOffset(h,{reverseOrder:r,gutter:a,defaultPosition:t}),f=ol(u,p);return d.createElement(il,{id:h.id,key:h.id,onHeightUpdate:l.updateHeight,className:h.visible?ll:"",style:f},h.type==="custom"?Gs(h.message,h):n?n(h):d.createElement(nl,{toast:h,position:u}))}))},Vu=v;const $a=r=>{let t;const s=new Set,a=(u,p)=>{const f=typeof u=="function"?u(t):u;if(!Object.is(f,t)){const m=t;t=p??(typeof f!="object"||f===null)?f:Object.assign({},t,f),s.forEach(x=>x(t,m))}},n=()=>t,l={setState:a,getState:n,getInitialState:()=>h,subscribe:u=>(s.add(u),()=>s.delete(u)),destroy:()=>{s.clear()}},h=t=r(a,n,l);return l},cl=r=>r?$a(r):$a;var An={exports:{}},_n={},Tn={exports:{}},On={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ht=d;function dl(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var ul=typeof Object.is=="function"?Object.is:dl,hl=Ht.useState,ml=Ht.useEffect,pl=Ht.useLayoutEffect,xl=Ht.useDebugValue;function fl(r,t){var s=t(),a=hl({inst:{value:s,getSnapshot:t}}),n=a[0].inst,i=a[1];return pl(function(){n.value=s,n.getSnapshot=t,ur(n)&&i({inst:n})},[r,s,t]),ml(function(){return ur(n)&&i({inst:n}),r(function(){ur(n)&&i({inst:n})})},[r]),xl(s),s}function ur(r){var t=r.getSnapshot;r=r.value;try{var s=t();return!ul(r,s)}catch{return!0}}function gl(r,t){return t()}var yl=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?gl:fl;On.useSyncExternalStore=Ht.useSyncExternalStore!==void 0?Ht.useSyncExternalStore:yl;Tn.exports=On;var bl=Tn.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rr=d,vl=bl;function wl(r,t){return r===t&&(r!==0||1/r===1/t)||r!==r&&t!==t}var jl=typeof Object.is=="function"?Object.is:wl,Nl=vl.useSyncExternalStore,kl=rr.useRef,Sl=rr.useEffect,Cl=rr.useMemo,El=rr.useDebugValue;_n.useSyncExternalStoreWithSelector=function(r,t,s,a,n){var i=kl(null);if(i.current===null){var c={hasValue:!1,value:null};i.current=c}else c=i.current;i=Cl(function(){function l(m){if(!h){if(h=!0,u=m,m=a(m),n!==void 0&&c.hasValue){var x=c.value;if(n(x,m))return p=x}return p=m}if(x=p,jl(u,m))return x;var g=a(m);return n!==void 0&&n(x,g)?(u=m,x):(u=m,p=g)}var h=!1,u,p,f=s===void 0?null:s;return[function(){return l(t())},f===null?void 0:function(){return l(f())}]},[t,s,a,n]);var o=Nl(r,i[0],i[1]);return Sl(function(){c.hasValue=!0,c.value=o},[o]),El(o),o};An.exports=_n;var Rl=An.exports;const Pl=Js(Rl),{useDebugValue:Il}=ce,{useSyncExternalStoreWithSelector:Al}=Pl;const _l=r=>r;function Tl(r,t=_l,s){const a=Al(r.subscribe,r.getState,r.getServerState||r.getInitialState,t,s);return Il(a),a}const La=r=>{const t=typeof r=="function"?cl(r):r,s=(a,n)=>Tl(t,a,n);return Object.assign(s,t),s},Ol=r=>r?La(r):La,Er=new Map,ks=r=>{const t=Er.get(r);return t?Object.fromEntries(Object.entries(t.stores).map(([s,a])=>[s,a.getState()])):{}},Vl=(r,t,s)=>{if(r===void 0)return{type:"untracked",connection:t.connect(s)};const a=Er.get(s.name);if(a)return{type:"tracked",store:r,...a};const n={connection:t.connect(s),stores:{}};return Er.set(s.name,n),{type:"tracked",store:r,...n}},Dl=(r,t={})=>(s,a,n)=>{const{enabled:i,anonymousActionType:c,store:o,...l}=t;let h;try{h=(i??!1)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!h)return r(s,a,n);const{connection:u,...p}=Vl(o,h,l);let f=!0;n.setState=(g,w,b)=>{const k=s(g,w);if(!f)return k;const S=b===void 0?{type:c||"anonymous"}:typeof b=="string"?{type:b}:b;return o===void 0?(u==null||u.send(S,a()),k):(u==null||u.send({...S,type:`${o}/${S.type}`},{...ks(l.name),[o]:n.getState()}),k)};const m=(...g)=>{const w=f;f=!1,s(...g),f=w},x=r(n.setState,a,n);if(p.type==="untracked"?u==null||u.init(x):(p.stores[p.store]=n,u==null||u.init(Object.fromEntries(Object.entries(p.stores).map(([g,w])=>[g,g===p.store?x:w.getState()])))),n.dispatchFromDevtools&&typeof n.dispatch=="function"){let g=!1;const w=n.dispatch;n.dispatch=(...b)=>{w(...b)}}return u.subscribe(g=>{var w;switch(g.type){case"ACTION":if(typeof g.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return hr(g.payload,b=>{if(b.type==="__setState"){if(o===void 0){m(b.state);return}Object.keys(b.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const k=b.state[o];if(k==null)return;JSON.stringify(n.getState())!==JSON.stringify(k)&&m(k);return}n.dispatchFromDevtools&&typeof n.dispatch=="function"&&n.dispatch(b)});case"DISPATCH":switch(g.payload.type){case"RESET":return m(x),o===void 0?u==null?void 0:u.init(n.getState()):u==null?void 0:u.init(ks(l.name));case"COMMIT":if(o===void 0){u==null||u.init(n.getState());return}return u==null?void 0:u.init(ks(l.name));case"ROLLBACK":return hr(g.state,b=>{if(o===void 0){m(b),u==null||u.init(n.getState());return}m(b[o]),u==null||u.init(ks(l.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return hr(g.state,b=>{if(o===void 0){m(b);return}JSON.stringify(n.getState())!==JSON.stringify(b[o])&&m(b[o])});case"IMPORT_STATE":{const{nextLiftedState:b}=g.payload,k=(w=b.computedStates.slice(-1)[0])==null?void 0:w.state;if(!k)return;m(o===void 0?k:k[o]),u==null||u.send(null,b);return}case"PAUSE_RECORDING":return f=!f}return}}),x},$l=Dl,hr=(r,t)=>{let s;try{s=JSON.parse(r)}catch(a){console.error("[zustand devtools middleware] Could not parse the received json",a)}s!==void 0&&t(s)};function Ll(r,t){let s;try{s=r()}catch{return}return{getItem:n=>{var i;const c=l=>l===null?null:JSON.parse(l,t==null?void 0:t.reviver),o=(i=s.getItem(n))!=null?i:null;return o instanceof Promise?o.then(c):c(o)},setItem:(n,i)=>s.setItem(n,JSON.stringify(i,t==null?void 0:t.replacer)),removeItem:n=>s.removeItem(n)}}const cs=r=>t=>{try{const s=r(t);return s instanceof Promise?s:{then(a){return cs(a)(s)},catch(a){return this}}}catch(s){return{then(a){return this},catch(a){return cs(a)(s)}}}},Fl=(r,t)=>(s,a,n)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:w=>w,version:0,merge:(w,b)=>({...b,...w}),...t},c=!1;const o=new Set,l=new Set;let h;try{h=i.getStorage()}catch{}if(!h)return r((...w)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...w)},a,n);const u=cs(i.serialize),p=()=>{const w=i.partialize({...a()});let b;const k=u({state:w,version:i.version}).then(S=>h.setItem(i.name,S)).catch(S=>{b=S});if(b)throw b;return k},f=n.setState;n.setState=(w,b)=>{f(w,b),p()};const m=r((...w)=>{s(...w),p()},a,n);let x;const g=()=>{var w;if(!h)return;c=!1,o.forEach(k=>k(a()));const b=((w=i.onRehydrateStorage)==null?void 0:w.call(i,a()))||void 0;return cs(h.getItem.bind(h))(i.name).then(k=>{if(k)return i.deserialize(k)}).then(k=>{if(k)if(typeof k.version=="number"&&k.version!==i.version){if(i.migrate)return i.migrate(k.state,k.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return k.state}).then(k=>{var S;return x=i.merge(k,(S=a())!=null?S:m),s(x,!0),p()}).then(()=>{b==null||b(x,void 0),c=!0,l.forEach(k=>k(x))}).catch(k=>{b==null||b(void 0,k)})};return n.persist={setOptions:w=>{i={...i,...w},w.getStorage&&(h=w.getStorage())},clearStorage:()=>{h==null||h.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>g(),hasHydrated:()=>c,onHydrate:w=>(o.add(w),()=>{o.delete(w)}),onFinishHydration:w=>(l.add(w),()=>{l.delete(w)})},g(),x||m},Ml=(r,t)=>(s,a,n)=>{let i={storage:Ll(()=>localStorage),partialize:g=>g,version:0,merge:(g,w)=>({...w,...g}),...t},c=!1;const o=new Set,l=new Set;let h=i.storage;if(!h)return r((...g)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),s(...g)},a,n);const u=()=>{const g=i.partialize({...a()});return h.setItem(i.name,{state:g,version:i.version})},p=n.setState;n.setState=(g,w)=>{p(g,w),u()};const f=r((...g)=>{s(...g),u()},a,n);n.getInitialState=()=>f;let m;const x=()=>{var g,w;if(!h)return;c=!1,o.forEach(k=>{var S;return k((S=a())!=null?S:f)});const b=((w=i.onRehydrateStorage)==null?void 0:w.call(i,(g=a())!=null?g:f))||void 0;return cs(h.getItem.bind(h))(i.name).then(k=>{if(k)if(typeof k.version=="number"&&k.version!==i.version){if(i.migrate)return[!0,i.migrate(k.state,k.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,k.state];return[!1,void 0]}).then(k=>{var S;const[J,Z]=k;if(m=i.merge(Z,(S=a())!=null?S:f),s(m,!0),J)return u()}).then(()=>{b==null||b(m,void 0),m=a(),c=!0,l.forEach(k=>k(m))}).catch(k=>{b==null||b(void 0,k)})};return n.persist={setOptions:g=>{i={...i,...g},g.storage&&(h=g.storage)},clearStorage:()=>{h==null||h.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>x(),hasHydrated:()=>c,onHydrate:g=>(o.add(g),()=>{o.delete(g)}),onFinishHydration:g=>(l.add(g),()=>{l.delete(g)})},i.skipHydration||x(),m||f},Ul=(r,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?Fl(r,t):Ml(r,t),ql=Ul,Fa={user:null,isAuthenticated:!1,authToken:null,currentRestaurant:null,suggestions:[],playQueue:null,currentlyPlaying:null,theme:"auto",deviceType:"desktop",isOnline:navigator.onLine,connectionStatus:"disconnected",notifications:[],settings:{autoRefresh:!0,soundEnabled:!0,showNotifications:!0,language:"pt-BR",maxSuggestionsPerSession:3,notificationPosition:"top-left"},loading:{suggestions:!1,queue:!1,search:!1,voting:!1}},Xt=Ol()($l(ql((r,t)=>({...Fa,setUser:s=>r(a=>({user:s,isAuthenticated:!!s&&!!a.authToken})),setAuthToken:s=>{r(a=>({authToken:s,isAuthenticated:!!s&&!!a.user})),s?localStorage.setItem("authToken",s):localStorage.removeItem("authToken")},setCurrentRestaurant:s=>r({currentRestaurant:s}),setSuggestions:s=>r({suggestions:s}),addSuggestion:s=>r(a=>({suggestions:[s,...a.suggestions]})),updateSuggestion:(s,a)=>r(n=>({suggestions:n.suggestions.map(i=>i.id===s?{...i,...a}:i)})),removeSuggestion:s=>r(a=>({suggestions:a.suggestions.filter(n=>n.id!==s)})),setPlayQueue:s=>r({playQueue:s}),setCurrentlyPlaying:s=>r({currentlyPlaying:s}),setTheme:s=>{r({theme:s});const a=document.documentElement;s==="dark"?a.classList.add("dark"):s==="light"?a.classList.remove("dark"):window.matchMedia("(prefers-color-scheme: dark)").matches?a.classList.add("dark"):a.classList.remove("dark")},setDeviceType:s=>r({deviceType:s}),setOnlineStatus:s=>r({isOnline:s}),setConnectionStatus:s=>r({connectionStatus:s}),addNotification:s=>{const a=Date.now().toString(),n={...s,id:a,createdAt:new Date().toISOString()};r(i=>({notifications:[n,...i.notifications]})),s.duration&&s.duration>0&&setTimeout(()=>{t().removeNotification(a)},s.duration)},removeNotification:s=>r(a=>({notifications:a.notifications.filter(n=>n.id!==s)})),clearNotifications:()=>r({notifications:[]}),updateSettings:s=>r(a=>({settings:{...a.settings,...s}})),setLoading:(s,a)=>r(n=>({loading:{...n.loading,[s]:a}})),reset:()=>r(()=>({...Fa,suggestions:[],notifications:[],isOnline:navigator.onLine}))}),{name:"restaurant-playlist-store",partialize:r=>({user:r.user,isAuthenticated:r.isAuthenticated,authToken:r.authToken,theme:r.theme,settings:r.settings})}),{name:"restaurant-playlist-store"})),ys=()=>{const{user:r,isAuthenticated:t,authToken:s,setUser:a,setAuthToken:n}=Xt();return{user:r,isAuthenticated:t,authToken:s,setUser:a,setAuthToken:n}},Bl=()=>{const{notifications:r,addNotification:t,removeNotification:s,clearNotifications:a}=Xt();return{notifications:r,addNotification:t,removeNotification:s,clearNotifications:a}},Vn=()=>{const{settings:r,updateSettings:t}=Xt();return{settings:r,updateSettings:t}},Ql=()=>{var a;const r=Xt.getState(),t=()=>{const n=window.innerWidth;return n<768?"mobile":n<1024?"tablet":"desktop"};r.setDeviceType(t()),r.setTheme(r.theme);try{const n=(a=r.settings)==null?void 0:a.notificationPosition,c=n?{"top-right":"top-left","bottom-right":"bottom-left"}[n]||n:"top-left";c!==n&&r.updateSettings({notificationPosition:c})}catch{}window.addEventListener("online",()=>r.setOnlineStatus(!0)),window.addEventListener("offline",()=>r.setOnlineStatus(!1)),window.addEventListener("resize",()=>{r.setDeviceType(t())}),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",()=>{r.theme==="auto"&&r.setTheme("auto")})};function Dn(r){var t,s,a="";if(typeof r=="string"||typeof r=="number")a+=r;else if(typeof r=="object")if(Array.isArray(r)){var n=r.length;for(t=0;t<n;t++)r[t]&&(s=Dn(r[t]))&&(a&&(a+=" "),a+=s)}else for(s in r)r[s]&&(a&&(a+=" "),a+=s);return a}function Rr(){for(var r,t,s=0,a="",n=arguments.length;s<n;s++)(r=arguments[s])&&(t=Dn(r))&&(a&&(a+=" "),a+=t);return a}const nt=({variant:r="primary",size:t="md",loading:s=!1,icon:a,children:n,className:i,disabled:c,...o})=>{const l="inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",h={primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-500 dark:hover:bg-primary-600",secondary:"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600",success:"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",outline:"border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",ghost:"bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-300 dark:hover:bg-gray-800"},u={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"};return e.jsxs("button",{className:Rr(l,h[r],u[t],i),disabled:c||s,...o,children:[s&&e.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!s&&a&&e.jsx("span",{className:"mr-2",children:a}),n]})},$n={BASE_URL:"http://localhost:8001",API_VERSION:"v1",get API_BASE(){return`${this.BASE_URL}/api/${this.API_VERSION}`},ENDPOINTS:{RESTAURANTS:"/restaurants",SUGGESTIONS:"/suggestions",PLAYBACK:"/playback",ANALYTICS:"/analytics",NOTIFICATIONS:"/notifications",GENRES:"/genres"}},ge=(r,t)=>{try{const s="http://localhost:8001",a=(s&&s.trim()!==""?s:"http://localhost:8001").replace(/\/$/,""),n=new URL(`/api/${$n.API_VERSION}${r}`,a);return t&&Object.entries(t).forEach(([i,c])=>{c&&c.trim()!==""&&n.searchParams.append(i,c)}),n.toString()}catch(s){return console.error("❌ Erro ao construir URL da API:",s,{endpoint:r,params:t}),`/api/v1${r}`}},Kt=(r="application/json")=>{const t=typeof localStorage<"u"?localStorage.getItem("authToken"):null,s={};return r&&(s["Content-Type"]=r),t&&(s.Authorization=`Bearer ${t}`),s},zl=({isOpen:r,onClose:t,onLogin:s})=>{const[a,n]=d.useState(""),[i,c]=d.useState(""),[o,l]=d.useState(""),[h,u]=d.useState(!1),[p,f]=d.useState("admin"),[m,x]=d.useState(!1),g=async b=>{if(b.preventDefault(),!a||!i){v.error("Por favor, preencha todos os campos");return}if(p==="restaurant"&&!o){v.error("Por favor, informe o ID do restaurante");return}x(!0);try{const k=await fetch(ge("/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:i})});if(!k.ok){const J=await k.json();throw new Error(J.error||"Erro ao fazer login")}const S=await k.json();localStorage.setItem("authToken",S.token),localStorage.setItem("user",JSON.stringify(S.user)),S.restaurant&&localStorage.setItem("restaurant",JSON.stringify(S.restaurant)),s({email:a,password:i,restaurantId:p==="restaurant"?o:void 0}),v.success("Login realizado com sucesso!"),t(),n(""),c(""),l("")}catch(k){console.error("Erro no login:",k),v.error(k.message||"Erro ao fazer login. Verifique suas credenciais.")}finally{x(!1)}},w=()=>{n("<EMAIL>"),c("admin123"),p==="restaurant"&&l("demo-restaurant")};return r?e.jsx(et,{children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4",children:e.jsxs(B.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{duration:.2},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md overflow-hidden",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-2 bg-white/20 rounded-lg",children:e.jsx(br,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Login"}),e.jsx("p",{className:"text-blue-100 text-sm",children:"Acesse sua conta"})]})]}),e.jsx("button",{onClick:t,className:"p-2 hover:bg-white/20 rounded-lg transition-colors",children:e.jsx(jt,{className:"w-5 h-5"})})]})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mb-6",children:[e.jsx("button",{type:"button",onClick:()=>f("admin"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${p==="admin"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}`,children:"Admin Geral"}),e.jsx("button",{type:"button",onClick:()=>f("restaurant"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${p==="restaurant"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"}`,children:"Restaurante"})]}),e.jsxs("form",{onSubmit:g,className:"space-y-4",children:[p==="restaurant"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"ID do Restaurante"}),e.jsxs("div",{className:"relative",children:[e.jsx(Mt,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"text",value:o,onChange:b=>l(b.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"demo-restaurant"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Yr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",value:a,onChange:b=>n(b.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"<EMAIL>",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Xs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:h?"text":"password",value:i,onChange:b=>c(b.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"••••••••",required:!0}),e.jsx("button",{type:"button",onClick:()=>u(!h),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:h?e.jsx(Gr,{className:"w-5 h-5"}):e.jsx(xs,{className:"w-5 h-5"})})]})]}),e.jsx("button",{type:"button",onClick:w,className:"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-center py-2",children:"Usar credenciais de demonstração"}),e.jsx(nt,{type:"submit",disabled:m,className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3",children:m?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Entrando..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(br,{className:"w-5 h-5"}),e.jsx("span",{children:"Entrar"})]})})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Não tem uma conta?"," ",e.jsx("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium",children:"Entre em contato"})]})})]})]})})}):null},Wl=()=>{const r=ps(),[t,s]=d.useState("");d.useState("");const[a,n]=d.useState(!1),i=()=>{t.trim()&&r(`/restaurant/${t.trim()}`)},c=()=>{r("/restaurant/demo-restaurant")},o=()=>{r("/admin/login")},l=()=>{r("/login")},h=m=>{console.log("Login credentials:",m),m.restaurantId?r(`/restaurant/${m.restaurantId}/admin`):r("/admin")},u=[{icon:os,title:"QR Code Inteligente",description:"Clientes escaneiam QR codes únicos por mesa e acessam instantaneamente o sistema de sugestões musicais",color:"from-blue-500 to-cyan-500"},{icon:Ce,title:"Integração YouTube Premium",description:"Acesso completo ao catálogo do YouTube sem anúncios, com suas próprias playlists personalizadas",color:"from-red-500 to-pink-500"},{icon:yt,title:"Experiência Colaborativa",description:"Clientes sugerem e votam em músicas em tempo real, criando uma atmosfera única e envolvente",color:"from-green-500 to-emerald-500"},{icon:Nt,title:"Analytics Avançados",description:"Dashboard completo com métricas, relatórios e insights sobre preferências musicais dos clientes",color:"from-purple-500 to-violet-500"}],p=[{icon:Je,title:"Aumente o Engajamento",description:"Clientes ficam mais tempo no restaurante quando participam da experiência musical",stats:"+35% tempo de permanência"},{icon:Jr,title:"Melhore a Satisfação",description:"Música personalizada pelos próprios clientes resulta em experiências mais memoráveis",stats:"94% aprovação dos clientes"},{icon:hi,title:"Reduza Reclamações",description:"Elimine reclamações sobre música inadequada - os clientes escolhem o que querem ouvir",stats:"-80% reclamações sobre música"},{icon:wa,title:"Diferencial Competitivo",description:"Seja o primeiro restaurante da região com tecnologia interativa de música",stats:"Inovação garantida"}],f=[{step:"1",title:"Cliente Escaneia QR Code",description:"Cada mesa tem um QR code único que direciona para a interface de sugestões",icon:os},{step:"2",title:"Sugere Músicas",description:"Busca no YouTube ou nas playlists do restaurante e sugere suas favoritas",icon:qs},{step:"3",title:"Comunidade Vota",description:"Outros clientes votam nas sugestões, criando uma fila democrática",icon:mi},{step:"4",title:"Música Toca Automaticamente",description:"Sistema reproduz as músicas mais votadas sem intervenção manual",icon:ot}];return e.jsxs("div",{className:"min-h-screen bg-white dark:bg-gray-900",children:[e.jsx("nav",{className:"fixed top-0 w-full bg-white/80 dark:bg-gray-900/80 backdrop-blur-md z-40 border-b border-gray-200 dark:border-gray-800",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:e.jsx(Ce,{className:"w-6 h-6 text-white"})}),e.jsx("span",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"PlaylistInterativa"})]}),e.jsxs("div",{className:"hidden md:flex items-center space-x-8",children:[e.jsx("a",{href:"#features",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Recursos"}),e.jsx("a",{href:"#how-it-works",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Como Funciona"}),e.jsx("a",{href:"#benefits",className:"text-gray-600 dark:text-gray-300 hover:text-blue-600 transition-colors",children:"Benefícios"}),e.jsx(nt,{onClick:l,variant:"outline",size:"sm",children:"Login"}),e.jsx(nt,{onClick:c,size:"sm",children:"Demo Grátis"})]})]})})}),e.jsx("section",{className:"pt-24 pb-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center",children:[e.jsxs("div",{className:"inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-800 dark:text-blue-200 text-sm font-medium mb-8",children:[e.jsx(wa,{className:"w-4 h-4 mr-2"}),"Revolucione a experiência musical do seu restaurante"]}),e.jsxs("h1",{className:"text-5xl md:text-7xl font-bold text-gray-900 dark:text-white mb-6 leading-tight",children:["Seus Clientes"," ",e.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"Escolhem"})," ","a Música"]}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed",children:"Sistema interativo que permite aos clientes sugerir e votar em músicas através de QR codes. Transforme seu restaurante em uma experiência única e memorável."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16",children:[e.jsxs(nt,{onClick:c,size:"lg",className:"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-lg px-8 py-4",children:[e.jsx(ot,{className:"w-5 h-5 mr-2"}),"Experimentar Demo"]}),e.jsxs(nt,{onClick:o,variant:"outline",size:"lg",className:"text-lg px-8 py-4",children:[e.jsx(Yt,{className:"w-5 h-5 mr-2"}),"Área Administrativa"]})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-6 max-w-md mx-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Acesso Rápido"}),e.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[e.jsx("input",{type:"text",placeholder:"Código do restaurante (ex.: demo-restaurant)",value:t,onChange:m=>s(m.target.value),className:"flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:m=>m.key==="Enter"&&i()}),e.jsx(nt,{onClick:i,className:"px-6 py-3",children:e.jsx(ui,{className:"w-4 h-4"})})]})]})]})})}),e.jsx("section",{id:"features",className:"py-20 bg-white dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Recursos Principais"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Tecnologia avançada para criar a experiência musical perfeita no seu restaurante"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:u.map((m,x)=>e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:x*.1},className:"bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow",children:[e.jsx("div",{className:`w-12 h-12 bg-gradient-to-r ${m.color} rounded-lg flex items-center justify-center mb-4`,children:e.jsx(m.icon,{className:"w-6 h-6 text-white"})}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:m.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:m.description})]},m.title))})]})}),e.jsx("section",{id:"how-it-works",className:"py-20 bg-gray-50 dark:bg-gray-800",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Como Funciona"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Processo simples e intuitivo que seus clientes vão adorar"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:f.map((m,x)=>e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:x*.1},className:"text-center",children:[e.jsxs("div",{className:"relative mb-6",children:[e.jsx("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(m.icon,{className:"w-8 h-8 text-white"})}),e.jsx("div",{className:"absolute -top-2 -right-2 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:m.step})]}),e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:m.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:m.description})]},m.step))})]})}),e.jsx("section",{id:"benefits",className:"py-20 bg-white dark:bg-gray-900",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"text-center mb-16",children:[e.jsx("h2",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"Por que Escolher Nossa Solução?"}),e.jsx("p",{className:"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto",children:"Resultados comprovados que transformam a experiência do seu restaurante"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:p.map((m,x)=>e.jsx(B.div,{initial:{opacity:0,x:x%2===0?-20:20},animate:{opacity:1,x:0},transition:{duration:.5,delay:x*.1},className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8",children:e.jsxs("div",{className:"flex items-start space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0",children:e.jsx(m.icon,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:m.title}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-3",children:m.description}),e.jsxs("div",{className:"inline-flex items-center px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full text-green-800 dark:text-green-200 text-sm font-medium",children:[e.jsx(Je,{className:"w-4 h-4 mr-1"}),m.stats]})]})]})},m.title))})]})}),e.jsx("section",{className:"py-20 bg-gradient-to-r from-blue-600 to-purple-600",children:e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8},children:[e.jsx("h2",{className:"text-4xl font-bold text-white mb-6",children:"Pronto para Revolucionar seu Restaurante?"}),e.jsx("p",{className:"text-xl text-blue-100 mb-8 max-w-2xl mx-auto",children:"Junte-se aos restaurantes que já estão oferecendo uma experiência musical única aos seus clientes"}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[e.jsxs("button",{onClick:c,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-blue-600 bg-white rounded-lg shadow-lg hover:bg-gray-100 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",children:[e.jsx(ot,{className:"w-5 h-5 mr-2"}),"Testar Gratuitamente"]}),e.jsxs("button",{onClick:o,className:"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg shadow-lg hover:bg-white hover:text-blue-600 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2",children:[e.jsx(xs,{className:"w-5 h-5 mr-2"}),"Ver Dashboard"]})]})]})})}),e.jsx("footer",{className:"bg-gray-900 text-white py-12",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"col-span-1 md:col-span-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:e.jsx(Ce,{className:"w-6 h-6 text-white"})}),e.jsx("span",{className:"text-xl font-bold",children:"PlaylistInterativa"})]}),e.jsx("p",{className:"text-gray-400 mb-4 max-w-md",children:"Transformando a experiência musical em restaurantes através de tecnologia interativa e colaborativa."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(nt,{onClick:c,size:"sm",children:"Experimentar Demo"}),e.jsx(nt,{onClick:o,variant:"outline",size:"sm",children:"Área Admin"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Recursos"}),e.jsxs("ul",{className:"space-y-2 text-gray-400",children:[e.jsx("li",{children:"QR Codes Dinâmicos"}),e.jsx("li",{children:"Integração YouTube"}),e.jsx("li",{children:"Analytics Avançados"}),e.jsx("li",{children:"Dashboard Admin"})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Suporte"}),e.jsxs("ul",{className:"space-y-2 text-gray-400",children:[e.jsx("li",{children:"Documentação"}),e.jsx("li",{children:"Tutoriais"}),e.jsx("li",{children:"Suporte Técnico"}),e.jsx("li",{children:"FAQ"})]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:e.jsxs("p",{children:["© 2025 Uniqsuporte. Todos os direitos reservados. |"," ",e.jsx("a",{href:"https://www.uniqsuporte.com.br",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"www.uniqsuporte.com.br"})," ","|"," ",e.jsx("a",{href:"tel:+5522997986724",className:"text-blue-400 hover:text-blue-300 transition-colors",children:"22 99798-6724"})]})})]})}),e.jsx(zl,{isOpen:a,onClose:()=>n(!1),onLogin:h})]})},Zt=({size:r="md",color:t="primary",className:s})=>{const a={sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12"},n={primary:"text-primary-600 dark:text-primary-400",secondary:"text-secondary-600 dark:text-secondary-400",white:"text-white",gray:"text-gray-600 dark:text-gray-400"};return e.jsx(B.div,{initial:{opacity:0},animate:{opacity:1},className:Rr("inline-block",s),children:e.jsxs("svg",{className:Rr("animate-spin",a[r],n[t]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})},Hl="http://localhost:8001";class Kl{constructor(){_e(this,"client");_e(this,"sessionId",null);this.client=Di.create({baseURL:`${Hl}/api/v1`,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.setupInterceptors(),this.initializeSessionId()}setupInterceptors(){this.client.interceptors.request.use(t=>{const s=this.getAuthToken();return s&&(t.headers.Authorization=`Bearer ${s}`),this.sessionId&&(t.headers["X-Session-ID"]=this.sessionId),t.headers["X-Device-Info"]=JSON.stringify({userAgent:navigator.userAgent,language:navigator.language,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,screenResolution:`${screen.width}x${screen.height}`}),t},t=>Promise.reject(t)),this.client.interceptors.response.use(t=>t,t=>(this.handleApiError(t),Promise.reject(t)))}initializeSessionId(){let t=localStorage.getItem("sessionId");t||(t=this.generateSessionId(),localStorage.setItem("sessionId",t)),this.sessionId=t}generateSessionId(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){const s=Math.random()*16|0;return(t==="x"?s:s&3|8).toString(16)})}getAuthToken(){return localStorage.getItem("authToken")}setAuthToken(t){localStorage.setItem("authToken",t)}removeAuthToken(){localStorage.removeItem("authToken")}handleApiError(t){var a,n;const s=(a=t.response)==null?void 0:a.data;switch((n=t.response)==null?void 0:n.status){case 401:this.removeAuthToken(),window.location.pathname.includes("/admin")&&(window.location.href="/admin/login");break;case 403:v.error("Acesso negado");break;case 404:v.error("Recurso não encontrado");break;case 429:v.error("Muitas requisições. Tente novamente em alguns minutos.");break;case 500:v.error("Erro interno do servidor");break;default:s!=null&&s.message?v.error(s.message):t.message?v.error(t.message):v.error("Erro inesperado")}}async login(t,s){const a=await this.client.post("/auth/login",{email:t,password:s});return this.setAuthToken(a.data.token),a.data}async logout(){this.removeAuthToken()}async getCurrentUser(){return(await this.client.get("/auth/me")).data}async getRestaurant(t){return(await this.client.get(`/restaurants/${t}`)).data}async searchYouTube(t,s,a=10,n,i=!1){return(await this.client.get("/youtube/search",{params:{q:t,maxResults:a,pageToken:n,useYouTubeAPI:i.toString(),restaurantId:s}})).data}async getVideoInfo(t){return(await this.client.get(`/youtube/video/${t}`)).data}async createSuggestion(t){return(await this.client.post("/suggestions",t)).data}async getSuggestions(t,s){const a={...s};return a.status==="all"&&delete a.status,(await this.client.get(`/suggestions/${t}`,{params:a})).data}async getPlayQueue(t){return(await this.client.get(`/playback-queue/${t}`)).data}async voteSuggestion(t,s){return(await this.client.post(`/suggestions/${t}/vote`,{voteType:s})).data}async getPlaylists(t){return(await this.client.get(`/playlists/${t}`)).data}async getPlaylist(t){return(await this.client.get(`/playlists/${t}`)).data}async getAnalytics(t,s){return(await this.client.get(`/analytics/dashboard/${t}`,{params:s})).data}async getYouTubeQuota(){return(await this.client.get("/youtube/quota")).data}getSessionId(){return this.sessionId}isAuthenticated(){return!!this.getAuthToken()}async request(t){return(await this.client.request(t)).data}}const Ge=new Kl,Yl=()=>{const r=ps(),{setUser:t,setAuthToken:s}=ys(),[a,n]=d.useState({email:"",password:""}),[i,c]=d.useState(!1),o=yo(({email:u,password:p})=>Ge.login(u,p),{onSuccess:u=>{s(u.token),t(u.user),v.success("Login realizado com sucesso!"),r("/admin/dashboard")},onError:u=>{var f,m;const p=((m=(f=u.response)==null?void 0:f.data)==null?void 0:m.message)||"Erro ao fazer login";v.error(p)}}),l=async u=>{if(u.preventDefault(),!a.email||!a.password){v.error("Preencha todos os campos");return}try{if(await new Promise(p=>setTimeout(p,1e3)),a.email==="<EMAIL>"&&a.password==="Adm!n2024#Secure$"){console.log("Login Admin Principal válido, redirecionando para /admin/dashboard");const p={id:"super-admin-1",name:"Admin Principal",email:"<EMAIL>",role:"super_admin",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},f="mock-admin-token-"+Date.now();t(p),s(f),v.success("Login realizado com sucesso!"),r("/admin/dashboard")}else{v.error("Credenciais inválidas. Verifique email e senha.");return}}catch{v.error("Erro ao fazer login. Verifique suas credenciais.")}},h=u=>{const{name:p,value:f}=u.target;n(m=>({...m,[p]:f}))};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4",children:e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(B.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg",children:e.jsx(Ce,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Admin Principal"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Gerencie todos os restaurantes da plataforma"})]}),e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700",children:[e.jsxs("form",{onSubmit:l,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Yr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:a.email,onChange:h,className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>"})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Xs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{id:"password",name:"password",type:i?"text":"password",autoComplete:"current-password",required:!0,value:a.password,onChange:h,className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"••••••••"}),e.jsx("button",{type:"button",onClick:()=>c(!i),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:i?e.jsx(Gr,{className:"w-5 h-5"}):e.jsx(xs,{className:"w-5 h-5"})})]})]}),e.jsx(nt,{type:"submit",disabled:o.isLoading,className:"w-full",size:"lg",children:o.isLoading?e.jsxs(e.Fragment,{children:[e.jsx(Zt,{size:"sm"}),"Entrando..."]}):"Entrar"})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsx("button",{onClick:()=>r("/"),className:"text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors",children:"← Voltar para página inicial"})})]}),e.jsx("div",{className:"mt-8 text-center text-sm text-gray-500 dark:text-gray-400",children:"Restaurant Playlist System © 2024"})]})})},Gl=()=>{const r=ps(),{setUser:t,setAuthToken:s}=ys(),[a,n]=d.useState("<EMAIL>"),[i,c]=d.useState("admin123"),[o,l]=d.useState(!1),[h,u]=d.useState(!1),p=async m=>{if(m.preventDefault(),!a||!i){v.error("Por favor, preencha todos os campos");return}u(!0);try{const x=await fetch(ge("/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:a,password:i})});if(!x.ok){const w=await x.json();throw new Error(w.error||"Erro ao fazer login")}const g=await x.json();console.log("🔐 Login realizado com sucesso:",g),localStorage.setItem("authToken",g.token),localStorage.setItem("user",JSON.stringify(g.user)),g.restaurant&&localStorage.setItem("restaurant",JSON.stringify(g.restaurant)),t(g.user),s(g.token),v.success("Login realizado com sucesso!"),g.restaurant?(console.log(`🔄 Redirecionando para /restaurant/${g.restaurant.id}/dashboard`),r(`/restaurant/${g.restaurant.id}/dashboard`,{replace:!0})):r("/restaurant/demo-restaurant/dashboard",{replace:!0})}catch(x){console.error("Erro no login:",x),v.error(x.message||"Erro ao fazer login. Verifique suas credenciais.")}finally{u(!1)}},f=()=>{n("<EMAIL>"),c("admin123")};return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(B.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4",children:e.jsx(Ce,{className:"w-8 h-8 text-white"})}),e.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Restaurante Admin"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Acesse o painel do seu restaurante"})]}),e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.5},className:"bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8",children:[e.jsxs("form",{onSubmit:p,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),e.jsxs("div",{className:"relative",children:[e.jsx(Yr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:"email",value:a,onChange:m=>n(m.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"<EMAIL>",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Senha"}),e.jsxs("div",{className:"relative",children:[e.jsx(Xs,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),e.jsx("input",{type:o?"text":"password",value:i,onChange:m=>c(m.target.value),className:"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",placeholder:"••••••••",required:!0}),e.jsx("button",{type:"button",onClick:()=>l(!o),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:o?e.jsx(Gr,{className:"w-5 h-5"}):e.jsx(xs,{className:"w-5 h-5"})})]})]}),e.jsx("button",{type:"button",onClick:f,className:"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-center py-2 transition-colors",children:"← Usar credenciais de demonstração"}),e.jsx(nt,{type:"submit",disabled:h,className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-3 font-medium transition-all duration-200 transform hover:scale-[1.02]",children:h?e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),e.jsx("span",{children:"Entrando..."})]}):e.jsxs("div",{className:"flex items-center justify-center space-x-2",children:[e.jsx(br,{className:"w-5 h-5"}),e.jsx("span",{children:"Entrar"})]})})]}),e.jsxs("div",{className:"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[e.jsx("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2",children:"Credenciais para demonstração:"}),e.jsxs("div",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Email:"})," <EMAIL>"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Senha:"})," admin123"]})]})]}),e.jsx("div",{className:"mt-6 text-center",children:e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Não tem uma conta?"," ",e.jsx("button",{onClick:()=>r("/"),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors",children:"Voltar ao início"})]})})]})]})})},Jl=()=>{const[r,t]=d.useState([]),[s,a]=d.useState(!0),[n,i]=d.useState(null),[c,o]=d.useState(!1),[l,h]=d.useState(null),[u,p]=d.useState({email:"",password:""}),[f,m]=d.useState({name:"",email:"",description:"",address:"",phone:"",password:""});d.useEffect(()=>{x()},[]);const x=async(N=!1)=>{var $;try{a(!0),i(null);const W=await Ge.client.get("/admin/restaurants");console.log("Resposta da API:",W.data);const P=(($=W.data.restaurants)==null?void 0:$.map(H=>({...H,status:H.isActive?"active":"inactive"})))||[];t(P),N&&v.success("Lista de restaurantes atualizada com sucesso!")}catch(W){console.error("Erro ao carregar restaurantes:",W),i("Erro ao carregar restaurantes");const P=[{id:"demo-restaurant",name:"Restaurante Demo",email:"<EMAIL>",description:"Restaurante de demonstração",phone:"(11) 99999-9999",address:"Rua Demo, 123",isActive:!0,status:"active",createdAt:new Date().toISOString(),settings:{allowSuggestions:!0}}];t(P),v.error("Erro ao carregar restaurantes - usando dados de demonstração")}finally{a(!1)}},g=async()=>{var N,$;try{const P=(await Ge.client.post("/admin/restaurants",f)).data;v.success("Restaurante criado com sucesso!"),v.success(`Login: ${P.credentials.email}
Senha: ${P.credentials.password}
URL: ${P.loginUrl}`,{duration:1e4}),o(!1),m({name:"",email:"",description:"",address:"",phone:"",password:""}),x()}catch(W){console.error("Erro ao criar restaurante:",W),v.error((($=(N=W.response)==null?void 0:N.data)==null?void 0:$.error)||"Erro ao criar restaurante")}},w=async()=>{var N,$;if(l)try{const W={name:l.name,description:l.description,phone:l.phone,address:l.address};u.email.trim()&&(W.email=u.email),u.password.trim()&&(W.password=u.password);const P=await Ge.client.put(`/admin/restaurants/${l.id}`,W);v.success("Restaurante atualizado com sucesso!"),h(null),p({email:"",password:""}),x()}catch(W){console.error("Erro ao atualizar restaurante:",W),v.error((($=(N=W.response)==null?void 0:N.data)==null?void 0:$.error)||"Erro ao atualizar restaurante")}},b=async(N,$)=>{var W,P;try{const H=await Ge.client.patch(`/admin/restaurants/${N}/status`,{isActive:$}),re=$?"ativado":"desativado";v.success(`Restaurante ${re} com sucesso!`),x()}catch(H){console.error("Erro ao atualizar restaurante:",H),v.error(((P=(W=H.response)==null?void 0:W.data)==null?void 0:P.error)||"Erro ao atualizar restaurante")}},k=async N=>{var $,W;if(confirm("Tem certeza que deseja suspender este restaurante?"))try{await Ge.client.patch(`/admin/restaurants/${N}/status`,{isActive:!1}),v.success("Restaurante suspenso com sucesso!"),x()}catch(P){console.error("Erro ao suspender restaurante:",P),v.error(((W=($=P.response)==null?void 0:$.data)==null?void 0:W.error)||"Erro ao suspender restaurante")}},S=async N=>{var $,W;try{await Ge.client.patch(`/admin/restaurants/${N}/status`,{isActive:!0}),v.success("Restaurante reativado com sucesso!"),x()}catch(P){console.error("Erro ao reativar restaurante:",P),v.error(((W=($=P.response)==null?void 0:$.data)==null?void 0:W.error)||"Erro ao reativar restaurante")}},J=async N=>{var $,W;if(confirm("Tem certeza que deseja deletar este restaurante? Esta ação não pode ser desfeita."))try{await Ge.client.delete(`/admin/restaurants/${N}`),v.success("Restaurante deletado com sucesso!"),x()}catch(P){console.error("Erro ao deletar restaurante:",P),v.error(((W=($=P.response)==null?void 0:$.data)==null?void 0:W.error)||"Erro ao deletar restaurante")}},Z=N=>{switch(N.status){case"active":return"text-green-600 bg-green-100 dark:bg-green-900/20";case"inactive":return"text-gray-600 bg-gray-100 dark:bg-gray-900/20";case"suspended":return"text-red-600 bg-red-100 dark:bg-red-900/20";case"trial":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}},se=N=>{switch(N.status){case"active":return"Ativo";case"inactive":return"Inativo";case"suspended":return"Suspenso";case"trial":return"Trial";default:return"Desconhecido"}};return s?e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciamento de Restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie todos os restaurantes da plataforma"})]})}),e.jsxs("div",{className:"flex flex-col justify-center items-center h-64 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Xe,{className:"w-12 h-12 animate-spin text-blue-600"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-4 border-blue-100 dark:border-blue-900/20 rounded-full animate-pulse"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Carregando restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Por favor, aguarde..."})]})]})]}):n&&r.length===0?e.jsxs("div",{className:"flex flex-col justify-center items-center h-64 space-y-4",children:[e.jsx(ls,{className:"w-12 h-12 text-red-500"}),e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Erro ao carregar restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:n}),e.jsxs("button",{onClick:()=>x(!0),disabled:s,className:"mt-6 inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(Xe,{className:`w-5 h-5 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Tentar novamente"})]})]})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Gerenciamento de Restaurantes"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Gerencie todos os restaurantes da plataforma"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:()=>x(!0),disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar lista de restaurantes",children:[e.jsx(Xe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>o(!0),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(ja,{className:"w-4 h-4"}),e.jsx("span",{children:"Novo Restaurante"})]})]})]}),r.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4",children:e.jsx(yt,{className:"w-12 h-12 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Nenhum restaurante encontrado"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Comece criando seu primeiro restaurante na plataforma."}),e.jsxs("button",{onClick:()=>o(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(ja,{className:"w-5 h-5"}),e.jsx("span",{children:"Criar Primeiro Restaurante"})]})]}):e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:r.map(N=>{var $,W;return e.jsxs(B.div,{layout:!0,initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3},className:"bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300",children:[e.jsxs("div",{className:"flex justify-between items-start mb-6",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:N.name}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400 flex items-center",children:[e.jsxs("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e.jsx("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),N.email]})]}),e.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${Z(N)}`,children:se(N)})]}),N.description&&e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg",children:N.description})}),e.jsxs("div",{className:"space-y-2 mb-6",children:[N.phone&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})}),N.phone]}),N.address&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[e.jsx("svg",{className:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})}),N.address]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 mb-6 space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Sugestões"}),e.jsx("span",{className:`font-medium ${($=N.settings)!=null&&$.allowSuggestions?"text-green-600":"text-red-600"}`,children:(W=N.settings)!=null&&W.allowSuggestions?"Ativas":"Inativas"})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Criado em"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:new Date(N.createdAt).toLocaleDateString("pt-BR")})]}),N.lastActivityAt&&e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Última atividade"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:new Date(N.lastActivityAt).toLocaleDateString("pt-BR")})]})]}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("button",{onClick:()=>window.open(`/restaurant/${N.id}/dashboard`,"_blank"),className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium",title:"Acessar Dashboard do Restaurante",children:[e.jsx(Xr,{className:"w-4 h-4"}),e.jsx("span",{children:"Acessar Dashboard"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("button",{onClick:()=>{h(N),N.adminUser?p({email:N.adminUser.email||"",password:""}):p({email:"",password:""})},className:"flex items-center justify-center space-x-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm",title:"Editar Restaurante",children:[e.jsx(pi,{className:"w-4 h-4"}),e.jsx("span",{children:"Editar"})]}),e.jsxs("button",{onClick:()=>b(N.id,!N.isActive),className:`flex items-center justify-center space-x-1 px-3 py-2 rounded-lg transition-colors text-sm font-medium ${N.isActive?"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50":"bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50"}`,title:N.isActive?"Desativar Restaurante":"Ativar Restaurante",children:[N.isActive?e.jsx(Zr,{className:"w-4 h-4"}):e.jsx(ot,{className:"w-4 h-4"}),e.jsx("span",{children:N.isActive?"Pausar":"Ativar"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[N.status!=="suspended"?e.jsxs("button",{onClick:()=>k(N.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-900/50 transition-colors text-sm",title:"Suspender Restaurante",children:[e.jsx(sn,{className:"w-4 h-4"}),e.jsx("span",{children:"Suspender"})]}):e.jsxs("button",{onClick:()=>S(N.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors text-sm",title:"Reativar Restaurante",children:[e.jsx(xi,{className:"w-4 h-4"}),e.jsx("span",{children:"Reativar"})]}),e.jsxs("button",{onClick:()=>J(N.id),className:"flex items-center justify-center space-x-1 px-3 py-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors text-sm",title:"Deletar Restaurante",children:[e.jsx(rn,{className:"w-4 h-4"}),e.jsx("span",{children:"Deletar"})]})]})]})]},N.id)})}),e.jsxs(et,{children:[c&&e.jsx(B.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(B.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Criar Novo Restaurante"}),"              ",e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Restaurante *"}),e.jsx("input",{type:"text",value:f.name,onChange:N=>m({...f,name:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Restaurante do João"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email de Login *"}),e.jsx("input",{type:"email",value:f.email,onChange:N=>m({...f,email:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Senha *"}),e.jsx("input",{type:"password",value:f.password,onChange:N=>m({...f,password:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Senha segura"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:f.description,onChange:N=>m({...f,description:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:2,placeholder:"Descrição do restaurante"})]})]}),e.jsxs("div",{className:"flex space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>o(!1),className:"flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300",children:"Cancelar"}),e.jsx("button",{onClick:g,disabled:!f.name||!f.email||!f.password,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Criar"})]})]})}),l&&e.jsx(B.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs(B.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Editar Restaurante"}),"              ",e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nome do Restaurante *"}),e.jsx("input",{type:"text",value:l.name,onChange:N=>h({...l,name:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Ex: Restaurante do João"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Descrição"}),e.jsx("textarea",{value:l.description,onChange:N=>h({...l,description:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:2,placeholder:"Descrição do restaurante"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Telefone"}),e.jsx("input",{type:"text",value:l.phone||"",onChange:N=>h({...l,phone:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"(11) 99999-9999"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Endereço"}),e.jsx("input",{type:"text",value:l.address||"",onChange:N=>h({...l,address:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Rua, número, bairro"})]}),e.jsxs("div",{className:"mt-6 pt-4 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:"Credenciais de Login"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email de Login"}),e.jsx("input",{type:"email",value:u.email,onChange:N=>p({...u,email:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"<EMAIL>"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Deixe em branco para manter o email atual"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Nova Senha"}),e.jsx("input",{type:"password",value:u.password,onChange:N=>p({...u,password:N.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"Nova senha (opcional)"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Deixe em branco para manter a senha atual"})]})]})]})]}),e.jsxs("div",{className:"flex space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>{h(null),p({email:"",password:""})},className:"flex-1 px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300",children:"Cancelar"}),e.jsx("button",{onClick:w,disabled:!l.name,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Salvar"})]})]})})]})]})},Xl=()=>{const[r,t]=d.useState(null),[s,a]=d.useState(!0),[n,i]=d.useState("30d"),[c,o]=d.useState("overview");d.useEffect(()=>{l()},[n]);const l=async()=>{var u;try{a(!0);const{data:p}=await Ge.client.get("/admin/analytics");if(p!=null&&p.success&&((u=p.analytics)!=null&&u.overview)){const f=p.analytics.overview,m=p.analytics.topRestaurants||[];t({totalRestaurants:f.totalRestaurants??0,activeRestaurants:f.activeRestaurants??0,totalRevenue:f.totalRevenue??0,monthlyRevenue:f.monthlyGrowth??0,totalPayments:f.totalPayments??0,totalVotes:f.totalVotes??0,topRestaurants:m.map(x=>({id:x.id,name:x.name,revenue:x.revenue??0,transactions:x.transactions??x.payments??0})),revenueByMonth:[]})}else throw new Error("Resposta inválida do servidor")}catch(p){console.error("Erro ao carregar analytics globais:",p),v.error("Erro ao carregar dados de analytics")}finally{a(!1)}},h=async()=>{try{v.success("Exportando dados...")}catch{v.error("Erro ao exportar dados")}};return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Zt,{})}):r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Analytics Globais"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Métricas consolidadas de todos os restaurantes"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("select",{value:n,onChange:u=>i(u.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"})]}),e.jsxs("button",{onClick:h,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar dados de analytics",children:[e.jsx(ea,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar"})]}),e.jsxs("button",{onClick:l,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados de analytics",children:[e.jsx(Xe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>o("revenue"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(an,{className:"w-4 h-4"}),e.jsx("span",{children:"Relatório"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{key:"overview",label:"Visão Geral",icon:Nt},{key:"revenue",label:"Receitas",icon:rs},{key:"restaurants",label:"Restaurantes",icon:Mt}].map(u=>e.jsxs("button",{onClick:()=>o(u.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${c===u.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(u.icon,{className:"w-4 h-4"}),e.jsx("span",{children:u.label})]},u.key))}),c==="overview"&&e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Total de Restaurantes",value:r.totalRestaurants,subtitle:`${r.activeRestaurants} ativos`,icon:Mt,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Total",value:`R$ ${r.totalRevenue.toFixed(2)}`,subtitle:`+R$ ${r.monthlyRevenue.toFixed(2)} este mês`,icon:rs,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Pagamentos (Supervoto)",value:r.totalPayments.toLocaleString(),subtitle:"Últimos 30 dias",icon:rs,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Votos Totais",value:r.totalVotes.toLocaleString(),subtitle:"Engajamento na plataforma",icon:nn,color:"text-yellow-600",bgColor:"bg-yellow-100 dark:bg-yellow-900/20"}].map((u,p)=>e.jsx(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:p*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:u.title}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:u.value}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:u.subtitle})]}),e.jsx("div",{className:`p-3 rounded-lg ${u.bgColor}`,children:e.jsx(u.icon,{className:`w-6 h-6 ${u.color}`})})]})},p))}),c==="restaurants"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Restaurantes por Receita"}),e.jsx("div",{className:"space-y-4",children:r.topRestaurants.map((u,p)=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/20 text-blue-600 rounded-full text-sm font-bold",children:p+1}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:u.name}),typeof u.transactions=="number"&&e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[u.transactions," transações"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",u.revenue.toFixed(2)]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"receita"})]})]},u.id))})]}),c==="revenue"&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Receita por Mês"}),e.jsx("div",{className:"space-y-3",children:r.revenueByMonth.map(u=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:u.month}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",u.revenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[u.payments," pagamentos"]})]})]},u.month))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Atividade de Pagamentos"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Em breve: distribuição por método de pagamento, horários de pico e mais."})]})]})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Nenhum dado disponível"})})},Zl=()=>{const[r,t]=d.useState(null),[s,a]=d.useState(!0),[n,i]=d.useState("30d"),[c,o]=d.useState("overview"),l=10;d.useEffect(()=>{h()},[n]);const h=async()=>{try{a(!0);const f=await(await fetch(`/api/v1/admin/revenue?period=${n}`)).json();if(f.success)t(f.revenue);else throw new Error("Erro ao carregar dados de receita")}catch(p){console.error("Erro ao carregar dados de receita:",p),v.error("Erro ao carregar dados de receita"),t({totalRevenue:12450.75,platformRevenue:1245.08,restaurantRevenue:11205.67,monthlyGrowth:15.3,totalTransactions:623,averageTransactionValue:2,revenueByRestaurant:[{restaurantId:"1",restaurantName:"Restaurante A",totalRevenue:3250.5,platformShare:325.05,transactions:162,averageValue:2.01},{restaurantId:"2",restaurantName:"Restaurante B",totalRevenue:2890.3,platformShare:289.03,transactions:144,averageValue:2.01},{restaurantId:"3",restaurantName:"Restaurante C",totalRevenue:2150.2,platformShare:215.02,transactions:107,averageValue:2.01},{restaurantId:"4",restaurantName:"Restaurante D",totalRevenue:1980.15,platformShare:198.02,transactions:99,averageValue:2},{restaurantId:"5",restaurantName:"Restaurante E",totalRevenue:1679.6,platformShare:167.96,transactions:84,averageValue:1.99}],revenueByMonth:[{month:"Jan",totalRevenue:8200,platformRevenue:820,transactions:410},{month:"Fev",totalRevenue:9350,platformRevenue:935,transactions:467},{month:"Mar",totalRevenue:10180,platformRevenue:1018,transactions:509},{month:"Abr",totalRevenue:11420,platformRevenue:1142,transactions:571},{month:"Mai",totalRevenue:12450,platformRevenue:1245,transactions:623}],paymentMethods:[{method:"PIX",count:589,revenue:11780,percentage:94.6},{method:"Cartão",count:34,revenue:670.75,percentage:5.4}]})}finally{a(!1)}},u=async()=>{try{v.success("Exportando relatório de receitas...");const p=await fetch(`/api/v1/admin/revenue/export?period=${n}`);if(!p.ok)throw new Error("Falha ao exportar");const f=await p.blob(),m=window.URL.createObjectURL(f),x=document.createElement("a");x.href=m,x.download=`revenue_${n}_${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(x),x.click(),x.remove(),window.URL.revokeObjectURL(m)}catch{v.error("Erro ao exportar dados")}};return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Zt,{})}):r?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Dashboard de Receitas"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Controle financeiro e receitas compartilhadas"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("select",{value:n,onChange:p=>i(p.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"}),e.jsx("option",{value:"1y",children:"Último ano"})]}),e.jsxs("button",{onClick:u,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",title:"Exportar dados de receita",children:[e.jsx(ea,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar"})]}),e.jsxs("button",{onClick:h,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados de receita",children:[e.jsx(Xe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>o("overview"),className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(an,{className:"w-4 h-4"}),e.jsx("span",{children:"Relatório"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[{key:"overview",label:"Visão Geral",icon:Nt},{key:"restaurants",label:"Por Restaurante",icon:Mt},{key:"trends",label:"Tendências",icon:Je}].map(p=>e.jsxs("button",{onClick:()=>o(p.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${c===p.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(p.icon,{className:"w-4 h-4"}),e.jsx("span",{children:p.label})]},p.key))}),c==="overview"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Receita Total",value:`R$ ${r.totalRevenue.toFixed(2)}`,subtitle:`${r.totalTransactions} transações`,icon:rs,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Nossa Receita",value:`R$ ${r.platformRevenue.toFixed(2)}`,subtitle:`${l}% da receita total`,icon:Je,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Restaurantes",value:`R$ ${r.restaurantRevenue.toFixed(2)}`,subtitle:`${100-l}% da receita total`,icon:Mt,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Ticket Médio",value:`R$ ${r.averageTransactionValue.toFixed(2)}`,subtitle:`+${r.monthlyGrowth}% este mês`,icon:zt,color:"text-orange-600",bgColor:"bg-orange-100 dark:bg-orange-900/20"}].map((p,f)=>e.jsx(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:f*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:p.title}),e.jsx("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:p.value}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:p.subtitle})]}),e.jsx("div",{className:`p-3 rounded-lg ${p.bgColor}`,children:e.jsx(p.icon,{className:`w-6 h-6 ${p.color}`})})]})},f))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Métodos de Pagamento"}),e.jsx("div",{className:"space-y-4",children:r.paymentMethods.map(p=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsx("span",{className:"text-gray-900 dark:text-white font-medium",children:p.method})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",p.revenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-gray-500",children:[p.count," transações (",p.percentage,"%)"]})]})]},p.method))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Distribuição de Receita"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),e.jsxs("span",{className:"text-gray-900 dark:text-white font-medium",children:["Plataforma (",l,"%)"]})]}),e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",r.platformRevenue.toFixed(2)]})]}),e.jsxs("div",{className:"flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),e.jsxs("span",{className:"text-gray-900 dark:text-white font-medium",children:["Restaurantes (",100-l,"%)"]})]}),e.jsxs("span",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",r.restaurantRevenue.toFixed(2)]})]})]})]})]})]}),c==="restaurants"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Receita por Restaurante"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-gray-200 dark:border-gray-700",children:[e.jsx("th",{className:"text-left py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Restaurante"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Receita Total"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Nossa Parte"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Transações"}),e.jsx("th",{className:"text-right py-3 px-4 font-medium text-gray-900 dark:text-white",children:"Ticket Médio"})]})}),e.jsx("tbody",{children:r.revenueByRestaurant.map(p=>e.jsxs("tr",{className:"border-b border-gray-100 dark:border-gray-800",children:[e.jsx("td",{className:"py-3 px-4",children:e.jsx("div",{className:"font-medium text-gray-900 dark:text-white",children:p.restaurantName})}),e.jsxs("td",{className:"py-3 px-4 text-right font-semibold text-gray-900 dark:text-white",children:["R$ ",p.totalRevenue.toFixed(2)]}),e.jsxs("td",{className:"py-3 px-4 text-right font-semibold text-green-600",children:["R$ ",p.platformShare.toFixed(2)]}),e.jsx("td",{className:"py-3 px-4 text-right text-gray-600 dark:text-gray-400",children:p.transactions}),e.jsxs("td",{className:"py-3 px-4 text-right text-gray-600 dark:text-gray-400",children:["R$ ",p.averageValue.toFixed(2)]})]},p.restaurantId))})]})})]}),c==="trends"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Evolução da Receita"}),e.jsx("div",{className:"space-y-4",children:r.revenueByMonth.map(p=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:p.month}),e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[p.transactions," transações"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"font-semibold text-gray-900 dark:text-white",children:["R$ ",p.totalRevenue.toFixed(2)]}),e.jsxs("p",{className:"text-sm text-green-600",children:["+R$ ",p.platformRevenue.toFixed(2)," nossa parte"]})]})]},p.month))})]})]}):e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Nenhum dado de receita disponível"})})},ec=()=>{const[r,t]=d.useState(null),[s,a]=d.useState(!0),[n,i]=d.useState(!1),[c,o]=d.useState("platform");d.useEffect(()=>{l()},[]);const l=async()=>{try{a(!0);const{data:m}=await Ge.client.get("/admin/settings");if(m.success)t({platform:{name:"Restaurant Playlist System",version:"2.1.0",maintenanceMode:m.settings.system.maintenanceMode,maxRestaurants:m.settings.system.maxRestaurantsPerPlan,defaultLanguage:"pt-BR",allowNewRegistrations:!!m.settings.system.allowNewRegistrations,defaultTrialDays:Number(m.settings.system.defaultTrialDays||30)},revenue:{platformFeePercentage:m.settings.payments.commissionRate,minimumPaymentAmount:2,paymentMethods:["pix","credit_card"],autoPayoutEnabled:!0,payoutFrequency:"weekly",defaultCurrency:m.settings.payments.defaultCurrency||"BRL"},notifications:{emailEnabled:m.settings.notifications.emailNotifications,smsEnabled:m.settings.notifications.smsNotifications,webhookEnabled:!!m.settings.notifications.webhookUrl,webhookUrl:m.settings.notifications.webhookUrl||"",adminNotifications:{newRestaurant:!0,paymentIssues:!0,systemErrors:!0,maintenanceAlerts:!0}},security:{sessionTimeout:3600,maxLoginAttempts:5,requireTwoFactor:!1,allowedDomains:["localhost","yourdomain.com"],rateLimitEnabled:!0,rateLimitRequests:100},integrations:{mercadoPago:{enabled:m.settings.payments.pixEnabled,environment:"sandbox",webhookUrl:m.settings.notifications.webhookUrl||""},youtube:{enabled:m.settings.features.analyticsEnabled,apiQuotaLimit:1e4,cacheEnabled:!0}}});else throw new Error("Erro ao carregar configurações")}catch(m){console.error("Erro ao carregar configurações:",m),v.error("Erro ao carregar configurações"),t({platform:{name:"Restaurant Playlist System",version:"2.1.0",maintenanceMode:!1,maxRestaurants:100,defaultLanguage:"pt-BR",allowNewRegistrations:!0,defaultTrialDays:30},revenue:{platformFeePercentage:10,minimumPaymentAmount:2,paymentMethods:["pix","credit_card"],autoPayoutEnabled:!0,payoutFrequency:"weekly",defaultCurrency:"BRL"},notifications:{emailEnabled:!0,smsEnabled:!1,webhookEnabled:!0,webhookUrl:"https://yourdomain.com/api/v1/payments/webhook",adminNotifications:{newRestaurant:!0,paymentIssues:!0,systemErrors:!0,maintenanceAlerts:!0}},security:{sessionTimeout:3600,maxLoginAttempts:5,requireTwoFactor:!1,allowedDomains:["localhost","yourdomain.com"],rateLimitEnabled:!0,rateLimitRequests:100},integrations:{mercadoPago:{enabled:!0,environment:"sandbox",webhookUrl:"https://yourdomain.com/api/v1/payments/webhook"},youtube:{enabled:!0,apiQuotaLimit:1e4,cacheEnabled:!0}}})}finally{a(!1)}},h=async()=>{if(r)try{i(!0);const m={settings:{system:{maintenanceMode:r.platform.maintenanceMode,allowNewRegistrations:!!r.platform.allowNewRegistrations,maxRestaurantsPerPlan:r.platform.maxRestaurants,defaultTrialDays:r.platform.defaultTrialDays??30},notifications:{emailNotifications:r.notifications.emailEnabled,smsNotifications:r.notifications.smsEnabled,webhookUrl:r.notifications.webhookEnabled&&r.notifications.webhookUrl||""},payments:{stripeEnabled:r.revenue.paymentMethods.includes("credit_card"),pixEnabled:r.integrations.mercadoPago.enabled,defaultCurrency:r.revenue.defaultCurrency||"BRL",commissionRate:r.revenue.platformFeePercentage},features:{analyticsEnabled:r.integrations.youtube.enabled,competitiveVotingEnabled:!0,playlistSchedulingEnabled:!0,qrCodeGenerationEnabled:!0}}};await Ge.client.put("/admin/settings",m),v.success("Configurações salvas com sucesso!")}catch(m){console.error("Erro ao salvar configurações:",m),v.error("Erro ao salvar configurações")}finally{i(!1)}},u=(m,x,g)=>{r&&t({...r,[m]:{...r[m],[x]:g}})},p=(m,x,g,w)=>{r&&t({...r,[m]:{...r[m],[x]:{...r[m][x],[g]:w}}})};if(s)return e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Zt,{})});if(!r)return e.jsx("div",{className:"text-center py-12",children:e.jsx("p",{className:"text-gray-500",children:"Erro ao carregar configurações"})});const f=[{key:"platform",label:"Plataforma",icon:gi},{key:"revenue",label:"Receitas",icon:rs},{key:"notifications",label:"Notificações",icon:yi},{key:"security",label:"Segurança",icon:sn},{key:"integrations",label:"Integrações",icon:Yt}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Configurações do Sistema"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Configurações globais da plataforma"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:l,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar configurações",children:[e.jsx(Xe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:h,disabled:n||s,className:"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",title:"Salvar todas as configurações",children:[e.jsx(on,{className:`w-4 h-4 ${n?"animate-pulse":""}`}),e.jsx("span",{children:n?"Salvando...":"Salvar"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/backup",className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(fi,{className:"w-4 h-4"}),e.jsx("span",{children:"Backup"})]})]})]}),e.jsx("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:f.map(m=>e.jsxs("button",{onClick:()=>o(m.key),className:`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${c===m.key?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"}`,children:[e.jsx(m.icon,{className:"w-4 h-4"}),e.jsx("span",{children:m.label})]},m.key))}),c==="platform"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações da Plataforma"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome da Plataforma"}),e.jsx("input",{type:"text",value:r.platform.name,onChange:m=>u("platform","name",m.target.value),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Versão"}),e.jsx("input",{type:"text",value:r.platform.version,onChange:m=>u("platform","version",m.target.value),className:"input-field",readOnly:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Restaurantes"}),e.jsx("input",{type:"number",value:r.platform.maxRestaurants,onChange:m=>u("platform","maxRestaurants",parseInt(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Idioma Padrão"}),e.jsxs("select",{value:r.platform.defaultLanguage,onChange:m=>u("platform","defaultLanguage",m.target.value),className:"input-field",children:[e.jsx("option",{value:"pt-BR",children:"Português (Brasil)"}),e.jsx("option",{value:"en-US",children:"English (US)"}),e.jsx("option",{value:"es-ES",children:"Español"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Dias de Teste (Trial)"}),e.jsx("input",{type:"number",min:0,value:r.platform.defaultTrialDays??30,onChange:m=>u("platform","defaultTrialDays",parseInt(m.target.value||"0")),className:"input-field"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.platform.maintenanceMode,onChange:m=>u("platform","maintenanceMode",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Modo de Manutenção"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Quando ativado, apenas administradores podem acessar o sistema"}),e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:!!r.platform.allowNewRegistrations,onChange:m=>u("platform","allowNewRegistrations",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Permitir novos cadastros"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Controla se novos restaurantes podem se registrar na plataforma"})]})]})]}),c==="revenue"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações de Receita"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Taxa da Plataforma (%)"}),e.jsx("input",{type:"number",min:0,max:100,step:.1,value:r.revenue.platformFeePercentage,onChange:m=>u("revenue","platformFeePercentage",parseFloat(m.target.value)),className:"input-field"}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Porcentagem que a plataforma recebe de cada transação"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Moeda Padrão"}),e.jsxs("select",{value:r.revenue.defaultCurrency||"BRL",onChange:m=>u("revenue","defaultCurrency",m.target.value),className:"input-field",children:[e.jsx("option",{value:"BRL",children:"BRL (R$)"}),e.jsx("option",{value:"USD",children:"USD ($)"}),e.jsx("option",{value:"EUR",children:"EUR (€)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Valor Mínimo de Pagamento (R$)"}),e.jsx("input",{type:"number",min:0,step:.01,value:r.revenue.minimumPaymentAmount,onChange:m=>u("revenue","minimumPaymentAmount",parseFloat(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Frequência de Repasse"}),e.jsxs("select",{value:r.revenue.payoutFrequency,onChange:m=>u("revenue","payoutFrequency",m.target.value),className:"input-field",children:[e.jsx("option",{value:"daily",children:"Diário"}),e.jsx("option",{value:"weekly",children:"Semanal"}),e.jsx("option",{value:"monthly",children:"Mensal"})]})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.revenue.autoPayoutEnabled,onChange:m=>u("revenue","autoPayoutEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Repasse Automático"})]}),e.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Quando ativado, os repasses são feitos automaticamente"})]})]}),c==="notifications"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Notificações"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40",children:[e.jsxs("label",{className:"flex items-center space-x-3 mb-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.emailEnabled,onChange:m=>u("notifications","emailEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"Email"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.smsEnabled,onChange:m=>u("notifications","smsEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"SMS"})]})]}),e.jsxs("div",{className:"p-4 rounded-lg bg-gray-50 dark:bg-gray-700/40",children:[e.jsxs("label",{className:"flex items-center space-x-3 mb-3",children:[e.jsx("input",{type:"checkbox",checked:r.notifications.webhookEnabled,onChange:m=>u("notifications","webhookEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{children:"Webhook"})]}),e.jsxs("div",{className:"mt-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"URL do Webhook"}),e.jsx("input",{type:"url",placeholder:"https://seuservico.com/webhook",value:r.notifications.webhookUrl||"",onChange:m=>u("notifications","webhookUrl",m.target.value),className:"input-field",disabled:!r.notifications.webhookEnabled})]})]})]})]}),c==="security"&&e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Configurações de Segurança"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Timeout de Sessão (segundos)"}),e.jsx("input",{type:"number",min:"300",value:r.security.sessionTimeout,onChange:m=>u("security","sessionTimeout",parseInt(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Máximo de Tentativas de Login"}),e.jsx("input",{type:"number",min:"1",max:"10",value:r.security.maxLoginAttempts,onChange:m=>u("security","maxLoginAttempts",parseInt(m.target.value)),className:"input-field"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Rate Limit (requisições/minuto)"}),e.jsx("input",{type:"number",min:"10",value:r.security.rateLimitRequests,onChange:m=>u("security","rateLimitRequests",parseInt(m.target.value)),className:"input-field"})]})]}),e.jsxs("div",{className:"mt-6 space-y-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.security.requireTwoFactor,onChange:m=>u("security","requireTwoFactor",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Exigir Autenticação de Dois Fatores"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.security.rateLimitEnabled,onChange:m=>u("security","rateLimitEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Rate Limiting"})]})]})]}),c==="integrations"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Mercado Pago"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Ambiente"}),e.jsxs("select",{value:r.integrations.mercadoPago.environment,onChange:m=>p("integrations","mercadoPago","environment",m.target.value),className:"input-field",children:[e.jsx("option",{value:"sandbox",children:"Sandbox (Teste)"}),e.jsx("option",{value:"production",children:"Produção"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Webhook URL"}),e.jsx("input",{type:"url",value:r.integrations.mercadoPago.webhookUrl,onChange:m=>p("integrations","mercadoPago","webhookUrl",m.target.value),className:"input-field"})]})]}),e.jsx("div",{className:"mt-4",children:e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.mercadoPago.enabled,onChange:m=>p("integrations","mercadoPago","enabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Integração Mercado Pago"})]})})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"YouTube API"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Limite de Quota da API"}),e.jsx("input",{type:"number",min:"1000",value:r.integrations.youtube.apiQuotaLimit,onChange:m=>p("integrations","youtube","apiQuotaLimit",parseInt(m.target.value)),className:"input-field"})]})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.youtube.enabled,onChange:m=>p("integrations","youtube","enabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Integração YouTube"})]}),e.jsxs("label",{className:"flex items-center space-x-3",children:[e.jsx("input",{type:"checkbox",checked:r.integrations.youtube.cacheEnabled,onChange:m=>p("integrations","youtube","cacheEnabled",m.target.checked),className:"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"}),e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Ativar Cache de Dados"})]})]})]})]})]})},mr=()=>{var l;const[r,t]=d.useState(null),[s,a]=d.useState(!0),[n,i]=d.useState(null);d.useEffect(()=>{c()},[]);const c=async()=>{var h,u;try{a(!0);const[p,f]=await Promise.all([fetch("/api/v1/admin/analytics"),fetch("/api/v1/admin/revenue?period=30d")]),[m,x]=await Promise.all([p.json(),f.json()]);if(m!=null&&m.success&&((h=m.analytics)!=null&&h.overview))t({totalRestaurants:m.analytics.overview.totalRestaurants||0,activeRestaurants:m.analytics.overview.activeRestaurants||0,totalRevenue:m.analytics.overview.totalRevenue||0,monthlyRevenue:m.analytics.overview.monthlyGrowth||0,totalSuggestions:m.analytics.overview.totalSuggestions||0,totalPayments:m.analytics.overview.totalPayments||0,averageRating:m.analytics.overview.averageRating||0,systemUptime:m.analytics.overview.systemUptime||"—"}),x!=null&&x.success&&((u=x.revenue)==null?void 0:u.totalRevenue)!=null&&i({totalRevenue:x.revenue.totalRevenue});else throw new Error("Resposta inválida de /admin/analytics")}catch(p){console.error("Erro ao carregar estatísticas globais:",p),v.error("Erro ao carregar dados do dashboard")}finally{a(!1)}},o=[{title:"Restaurantes Ativos",value:(r==null?void 0:r.activeRestaurants)||"0",total:(r==null?void 0:r.totalRestaurants)||"0",change:"+2 este mês",icon:Mt,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Receita Total",value:`R$ ${((n==null?void 0:n.totalRevenue)??(r==null?void 0:r.totalRevenue)??0).toFixed(2)}`,change:`+R$ ${((l=r==null?void 0:r.monthlyRevenue)==null?void 0:l.toFixed(2))||"0,00"} este mês`,icon:Je,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Pagamentos (Supervoto)",value:(r==null?void 0:r.totalPayments)||"0",change:"Últimos 30 dias",icon:Je,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Uptime do Sistema",value:(r==null?void 0:r.systemUptime)||"—",change:"Últimas 24h",icon:Xe,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20"}];return s?e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Zt,{})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Visão geral do sistema e controle de receitas"})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("button",{onClick:c,disabled:s,className:"flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Recarregar dados do dashboard",children:[e.jsx(Xe,{className:`w-4 h-4 ${s?"animate-spin":""}`}),e.jsx("span",{children:"Recarregar"})]}),e.jsxs("button",{onClick:()=>window.location.href="/admin/analytics",className:"flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:[e.jsx(Nt,{className:"w-4 h-4"}),e.jsx("span",{children:"Ver Analytics"})]})]})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:o.map((h,u)=>e.jsx(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:u*.1},className:"card p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:h.title}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[h.value,h.total&&e.jsxs("span",{className:"text-sm text-gray-500 ml-1",children:["/ ",h.total]})]}),e.jsxs("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[h.change," vs ontem"]})]}),e.jsx("div",{className:`w-12 h-12 rounded-lg ${h.bgColor} flex items-center justify-center`,children:e.jsx(h.icon,{className:`w-6 h-6 ${h.color}`})})]})},u))}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Transações Recentes (Supervoto)"}),e.jsx("div",{className:"space-y-3",children:[1,2,3,4].map(h=>e.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsx("div",{className:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:e.jsx(Je,{className:"w-4 h-4 text-green-600 dark:text-green-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["R$ ",(5*h).toFixed(2)," • Restaurante #",h]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["há ",h," min • Supervoto"]})]})]},h))})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Restaurantes por Receita (30d)"}),e.jsx("div",{className:"space-y-3",children:[1,2,3,4].map(h=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-100 dark:bg-blue-900/20 rounded text-xs flex items-center justify-center font-medium",children:h}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Restaurante #",h]}),e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["ID: demo-",h]})]})]}),e.jsxs("div",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:["R$ ",(1e3*h).toFixed(2)]})]},h))})]})]})]})},tc=()=>e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(Bt,{to:"/admin",className:"flex items-center space-x-3 hover:opacity-80 transition-opacity",children:[e.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:e.jsx(Ce,{className:"w-5 h-5 text-white"})}),e.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Restaurant Playlist Admin"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Admin"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center",children:e.jsx(Wt,{className:"w-4 h-4 text-gray-600 dark:text-gray-300"})}),e.jsxs(Bt,{to:"/",className:"flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium",title:"Sair do Admin",children:[e.jsx(ln,{className:"w-4 h-4"}),e.jsx("span",{children:"Sair"})]})]})]})]})})}),e.jsx("nav",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex space-x-8",children:[{name:"Dashboard",icon:cn,path:"/admin/dashboard"},{name:"Restaurantes",icon:Mt,path:"/admin/restaurants"},{name:"Analytics Globais",icon:Nt,path:"/admin/analytics"},{name:"Receitas",icon:Je,path:"/admin/revenue"},{name:"Configurações",icon:Yt,path:"/admin/settings"}].map(r=>e.jsxs(Bt,{to:r.path,className:"flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300",children:[e.jsx(r.icon,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:r.name})]},r.name))})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs(Hr,{children:[e.jsx(be,{path:"/",element:e.jsx(mr,{})}),e.jsx(be,{path:"/dashboard",element:e.jsx(mr,{})}),e.jsx(be,{path:"/restaurants",element:e.jsx(Jl,{})}),e.jsx(be,{path:"/analytics",element:e.jsx(Xl,{})}),e.jsx(be,{path:"/revenue",element:e.jsx(Zl,{})}),e.jsx(be,{path:"/settings",element:e.jsx(ec,{})}),e.jsx(be,{path:"*",element:e.jsx(mr,{})})]})})]}),sc="modulepreload",rc=function(r){return"/"+r},Ma={},pt=function(t,s,a){if(!s||s.length===0)return t();const n=document.getElementsByTagName("link");return Promise.all(s.map(i=>{if(i=rc(i),i in Ma)return;Ma[i]=!0;const c=i.endsWith(".css"),o=c?'[rel="stylesheet"]':"";if(!!a)for(let u=n.length-1;u>=0;u--){const p=n[u];if(p.href===i&&(!c||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${o}`))return;const h=document.createElement("link");if(h.rel=c?"stylesheet":sc,c||(h.as="script",h.crossOrigin=""),h.href=i,document.head.appendChild(h),c)return new Promise((u,p)=>{h.addEventListener("load",u),h.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t()).catch(i=>{const c=new Event("vite:preloadError",{cancelable:!0});if(c.payload=i,window.dispatchEvent(c),!c.defaultPrevented)throw i})},bt=Object.create(null);bt.open="0";bt.close="1";bt.ping="2";bt.pong="3";bt.message="4";bt.upgrade="5";bt.noop="6";const $s=Object.create(null);Object.keys(bt).forEach(r=>{$s[bt[r]]=r});const Pr={type:"error",data:"parser error"},Ln=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Fn=typeof ArrayBuffer=="function",Mn=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r&&r.buffer instanceof ArrayBuffer,ia=({type:r,data:t},s,a)=>Ln&&t instanceof Blob?s?a(t):Ua(t,a):Fn&&(t instanceof ArrayBuffer||Mn(t))?s?a(t):Ua(new Blob([t]),a):a(bt[r]+(t||"")),Ua=(r,t)=>{const s=new FileReader;return s.onload=function(){const a=s.result.split(",")[1];t("b"+(a||""))},s.readAsDataURL(r)};function qa(r){return r instanceof Uint8Array?r:r instanceof ArrayBuffer?new Uint8Array(r):new Uint8Array(r.buffer,r.byteOffset,r.byteLength)}let pr;function ac(r,t){if(Ln&&r.data instanceof Blob)return r.data.arrayBuffer().then(qa).then(t);if(Fn&&(r.data instanceof ArrayBuffer||Mn(r.data)))return t(qa(r.data));ia(r,!1,s=>{pr||(pr=new TextEncoder),t(pr.encode(s))})}const Ba="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ss=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let r=0;r<Ba.length;r++)ss[Ba.charCodeAt(r)]=r;const nc=r=>{let t=r.length*.75,s=r.length,a,n=0,i,c,o,l;r[r.length-1]==="="&&(t--,r[r.length-2]==="="&&t--);const h=new ArrayBuffer(t),u=new Uint8Array(h);for(a=0;a<s;a+=4)i=ss[r.charCodeAt(a)],c=ss[r.charCodeAt(a+1)],o=ss[r.charCodeAt(a+2)],l=ss[r.charCodeAt(a+3)],u[n++]=i<<2|c>>4,u[n++]=(c&15)<<4|o>>2,u[n++]=(o&3)<<6|l&63;return h},ic=typeof ArrayBuffer=="function",oa=(r,t)=>{if(typeof r!="string")return{type:"message",data:Un(r,t)};const s=r.charAt(0);return s==="b"?{type:"message",data:oc(r.substring(1),t)}:$s[s]?r.length>1?{type:$s[s],data:r.substring(1)}:{type:$s[s]}:Pr},oc=(r,t)=>{if(ic){const s=nc(r);return Un(s,t)}else return{base64:!0,data:r}},Un=(r,t)=>{switch(t){case"blob":return r instanceof Blob?r:new Blob([r]);case"arraybuffer":default:return r instanceof ArrayBuffer?r:r.buffer}},qn=String.fromCharCode(30),lc=(r,t)=>{const s=r.length,a=new Array(s);let n=0;r.forEach((i,c)=>{ia(i,!1,o=>{a[c]=o,++n===s&&t(a.join(qn))})})},cc=(r,t)=>{const s=r.split(qn),a=[];for(let n=0;n<s.length;n++){const i=oa(s[n],t);if(a.push(i),i.type==="error")break}return a};function dc(){return new TransformStream({transform(r,t){ac(r,s=>{const a=s.length;let n;if(a<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,a);else if(a<65536){n=new Uint8Array(3);const i=new DataView(n.buffer);i.setUint8(0,126),i.setUint16(1,a)}else{n=new Uint8Array(9);const i=new DataView(n.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(a))}r.data&&typeof r.data!="string"&&(n[0]|=128),t.enqueue(n),t.enqueue(s)})}})}let xr;function Ss(r){return r.reduce((t,s)=>t+s.length,0)}function Cs(r,t){if(r[0].length===t)return r.shift();const s=new Uint8Array(t);let a=0;for(let n=0;n<t;n++)s[n]=r[0][a++],a===r[0].length&&(r.shift(),a=0);return r.length&&a<r[0].length&&(r[0]=r[0].slice(a)),s}function uc(r,t){xr||(xr=new TextDecoder);const s=[];let a=0,n=-1,i=!1;return new TransformStream({transform(c,o){for(s.push(c);;){if(a===0){if(Ss(s)<1)break;const l=Cs(s,1);i=(l[0]&128)===128,n=l[0]&127,n<126?a=3:n===126?a=1:a=2}else if(a===1){if(Ss(s)<2)break;const l=Cs(s,2);n=new DataView(l.buffer,l.byteOffset,l.length).getUint16(0),a=3}else if(a===2){if(Ss(s)<8)break;const l=Cs(s,8),h=new DataView(l.buffer,l.byteOffset,l.length),u=h.getUint32(0);if(u>Math.pow(2,53-32)-1){o.enqueue(Pr);break}n=u*Math.pow(2,32)+h.getUint32(4),a=3}else{if(Ss(s)<n)break;const l=Cs(s,n);o.enqueue(oa(i?l:xr.decode(l),t)),a=0}if(n===0||n>r){o.enqueue(Pr);break}}}})}const Bn=4;function Le(r){if(r)return hc(r)}function hc(r){for(var t in Le.prototype)r[t]=Le.prototype[t];return r}Le.prototype.on=Le.prototype.addEventListener=function(r,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+r]=this._callbacks["$"+r]||[]).push(t),this};Le.prototype.once=function(r,t){function s(){this.off(r,s),t.apply(this,arguments)}return s.fn=t,this.on(r,s),this};Le.prototype.off=Le.prototype.removeListener=Le.prototype.removeAllListeners=Le.prototype.removeEventListener=function(r,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var s=this._callbacks["$"+r];if(!s)return this;if(arguments.length==1)return delete this._callbacks["$"+r],this;for(var a,n=0;n<s.length;n++)if(a=s[n],a===t||a.fn===t){s.splice(n,1);break}return s.length===0&&delete this._callbacks["$"+r],this};Le.prototype.emit=function(r){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),s=this._callbacks["$"+r],a=1;a<arguments.length;a++)t[a-1]=arguments[a];if(s){s=s.slice(0);for(var a=0,n=s.length;a<n;++a)s[a].apply(this,t)}return this};Le.prototype.emitReserved=Le.prototype.emit;Le.prototype.listeners=function(r){return this._callbacks=this._callbacks||{},this._callbacks["$"+r]||[]};Le.prototype.hasListeners=function(r){return!!this.listeners(r).length};const ar=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,s)=>s(t,0))(),it=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),mc="arraybuffer";function Qn(r,...t){return t.reduce((s,a)=>(r.hasOwnProperty(a)&&(s[a]=r[a]),s),{})}const pc=it.setTimeout,xc=it.clearTimeout;function nr(r,t){t.useNativeTimers?(r.setTimeoutFn=pc.bind(it),r.clearTimeoutFn=xc.bind(it)):(r.setTimeoutFn=it.setTimeout.bind(it),r.clearTimeoutFn=it.clearTimeout.bind(it))}const fc=1.33;function gc(r){return typeof r=="string"?yc(r):Math.ceil((r.byteLength||r.size)*fc)}function yc(r){let t=0,s=0;for(let a=0,n=r.length;a<n;a++)t=r.charCodeAt(a),t<128?s+=1:t<2048?s+=2:t<55296||t>=57344?s+=3:(a++,s+=4);return s}function zn(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function bc(r){let t="";for(let s in r)r.hasOwnProperty(s)&&(t.length&&(t+="&"),t+=encodeURIComponent(s)+"="+encodeURIComponent(r[s]));return t}function vc(r){let t={},s=r.split("&");for(let a=0,n=s.length;a<n;a++){let i=s[a].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}class wc extends Error{constructor(t,s,a){super(t),this.description=s,this.context=a,this.type="TransportError"}}class la extends Le{constructor(t){super(),this.writable=!1,nr(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,s,a){return super.emitReserved("error",new wc(t,s,a)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const s=oa(t,this.socket.binaryType);this.onPacket(s)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,s={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(s)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const s=bc(t);return s.length?"?"+s:""}}class jc extends la{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const s=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let a=0;this._polling&&(a++,this.once("pollComplete",function(){--a||s()})),this.writable||(a++,this.once("drain",function(){--a||s()}))}else s()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const s=a=>{if(this.readyState==="opening"&&a.type==="open"&&this.onOpen(),a.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(a)};cc(t,this.socket.binaryType).forEach(s),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,lc(t,s=>{this.doWrite(s,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",s=this.query||{};return this.opts.timestampRequests!==!1&&(s[this.opts.timestampParam]=zn()),!this.supportsBinary&&!s.sid&&(s.b64=1),this.createUri(t,s)}}let Wn=!1;try{Wn=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const Nc=Wn;function kc(){}class Sc extends jc{constructor(t){if(super(t),typeof location<"u"){const s=location.protocol==="https:";let a=location.port;a||(a=s?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||a!==t.port}}doWrite(t,s){const a=this.request({method:"POST",data:t});a.on("success",s),a.on("error",(n,i)=>{this.onError("xhr post error",n,i)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(s,a)=>{this.onError("xhr poll error",s,a)}),this.pollXhr=t}}class gt extends Le{constructor(t,s,a){super(),this.createRequest=t,nr(this,a),this._opts=a,this._method=a.method||"GET",this._uri=s,this._data=a.data!==void 0?a.data:null,this._create()}_create(){var t;const s=Qn(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");s.xdomain=!!this._opts.xd;const a=this._xhr=this.createRequest(s);try{a.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){a.setDisableHeaderCheck&&a.setDisableHeaderCheck(!0);for(let n in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(n)&&a.setRequestHeader(n,this._opts.extraHeaders[n])}}catch{}if(this._method==="POST")try{a.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{a.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(a),"withCredentials"in a&&(a.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(a.timeout=this._opts.requestTimeout),a.onreadystatechange=()=>{var n;a.readyState===3&&((n=this._opts.cookieJar)===null||n===void 0||n.parseCookies(a.getResponseHeader("set-cookie"))),a.readyState===4&&(a.status===200||a.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof a.status=="number"?a.status:0)},0))},a.send(this._data)}catch(n){this.setTimeoutFn(()=>{this._onError(n)},0);return}typeof document<"u"&&(this._index=gt.requestsCount++,gt.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=kc,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete gt.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}gt.requestsCount=0;gt.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Qa);else if(typeof addEventListener=="function"){const r="onpagehide"in it?"pagehide":"unload";addEventListener(r,Qa,!1)}}function Qa(){for(let r in gt.requests)gt.requests.hasOwnProperty(r)&&gt.requests[r].abort()}const Cc=function(){const r=Hn({xdomain:!1});return r&&r.responseType!==null}();class Ec extends Sc{constructor(t){super(t);const s=t&&t.forceBase64;this.supportsBinary=Cc&&!s}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new gt(Hn,this.uri(),t)}}function Hn(r){const t=r.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||Nc))return new XMLHttpRequest}catch{}if(!t)try{return new it[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Kn=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Rc extends la{get name(){return"websocket"}doOpen(){const t=this.uri(),s=this.opts.protocols,a=Kn?{}:Qn(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(a.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,s,a)}catch(n){return this.emitReserved("error",n)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const a=t[s],n=s===t.length-1;ia(a,this.supportsBinary,i=>{try{this.doWrite(a,i)}catch{}n&&ar(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",s=this.query||{};return this.opts.timestampRequests&&(s[this.opts.timestampParam]=zn()),this.supportsBinary||(s.b64=1),this.createUri(t,s)}}const fr=it.WebSocket||it.MozWebSocket;class Pc extends Rc{createSocket(t,s,a){return Kn?new fr(t,s,a):s?new fr(t,s):new fr(t)}doWrite(t,s){this.ws.send(s)}}class Ic extends la{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const s=uc(Number.MAX_SAFE_INTEGER,this.socket.binaryType),a=t.readable.pipeThrough(s).getReader(),n=dc();n.readable.pipeTo(t.writable),this._writer=n.writable.getWriter();const i=()=>{a.read().then(({done:o,value:l})=>{o||(this.onPacket(l),i())}).catch(o=>{})};i();const c={type:"open"};this.query.sid&&(c.data=`{"sid":"${this.query.sid}"}`),this._writer.write(c).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let s=0;s<t.length;s++){const a=t[s],n=s===t.length-1;this._writer.write(a).then(()=>{n&&ar(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const Ac={websocket:Pc,webtransport:Ic,polling:Ec},_c=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Tc=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Ir(r){if(r.length>8e3)throw"URI too long";const t=r,s=r.indexOf("["),a=r.indexOf("]");s!=-1&&a!=-1&&(r=r.substring(0,s)+r.substring(s,a).replace(/:/g,";")+r.substring(a,r.length));let n=_c.exec(r||""),i={},c=14;for(;c--;)i[Tc[c]]=n[c]||"";return s!=-1&&a!=-1&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=Oc(i,i.path),i.queryKey=Vc(i,i.query),i}function Oc(r,t){const s=/\/{2,9}/g,a=t.replace(s,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&a.splice(0,1),t.slice(-1)=="/"&&a.splice(a.length-1,1),a}function Vc(r,t){const s={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(a,n,i){n&&(s[n]=i)}),s}const Ar=typeof addEventListener=="function"&&typeof removeEventListener=="function",Ls=[];Ar&&addEventListener("offline",()=>{Ls.forEach(r=>r())},!1);class It extends Le{constructor(t,s){if(super(),this.binaryType=mc,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(s=t,t=null),t){const a=Ir(t);s.hostname=a.host,s.secure=a.protocol==="https"||a.protocol==="wss",s.port=a.port,a.query&&(s.query=a.query)}else s.host&&(s.hostname=Ir(s.host).host);nr(this,s),this.secure=s.secure!=null?s.secure:typeof location<"u"&&location.protocol==="https:",s.hostname&&!s.port&&(s.port=this.secure?"443":"80"),this.hostname=s.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=s.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},s.transports.forEach(a=>{const n=a.prototype.name;this.transports.push(n),this._transportsByName[n]=a}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},s),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=vc(this.opts.query)),Ar&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Ls.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const s=Object.assign({},this.opts.query);s.EIO=Bn,s.transport=t,this.id&&(s.sid=this.id);const a=Object.assign({},this.opts,{query:s,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](a)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&It.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const s=this.createTransport(t);s.open(),this.setTransport(s)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",s=>this._onClose("transport close",s))}onOpen(){this.readyState="open",It.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const s=new Error("server error");s.code=t.data,this._onError(s);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let s=1;for(let a=0;a<this.writeBuffer.length;a++){const n=this.writeBuffer[a].data;if(n&&(s+=gc(n)),a>0&&s>this._maxPayload)return this.writeBuffer.slice(0,a);s+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,ar(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,s,a){return this._sendPacket("message",t,s,a),this}send(t,s,a){return this._sendPacket("message",t,s,a),this}_sendPacket(t,s,a,n){if(typeof s=="function"&&(n=s,s=void 0),typeof a=="function"&&(n=a,a=null),this.readyState==="closing"||this.readyState==="closed")return;a=a||{},a.compress=a.compress!==!1;const i={type:t,data:s,options:a};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},s=()=>{this.off("upgrade",s),this.off("upgradeError",s),t()},a=()=>{this.once("upgrade",s),this.once("upgradeError",s)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?a():t()}):this.upgrading?a():t()),this}_onError(t){if(It.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,s){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Ar&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const a=Ls.indexOf(this._offlineEventListener);a!==-1&&Ls.splice(a,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,s),this.writeBuffer=[],this._prevBufferLen=0}}}It.protocol=Bn;class Dc extends It{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let s=this.createTransport(t),a=!1;It.priorWebsocketSuccess=!1;const n=()=>{a||(s.send([{type:"ping",data:"probe"}]),s.once("packet",p=>{if(!a)if(p.type==="pong"&&p.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",s),!s)return;It.priorWebsocketSuccess=s.name==="websocket",this.transport.pause(()=>{a||this.readyState!=="closed"&&(u(),this.setTransport(s),s.send([{type:"upgrade"}]),this.emitReserved("upgrade",s),s=null,this.upgrading=!1,this.flush())})}else{const f=new Error("probe error");f.transport=s.name,this.emitReserved("upgradeError",f)}}))};function i(){a||(a=!0,u(),s.close(),s=null)}const c=p=>{const f=new Error("probe error: "+p);f.transport=s.name,i(),this.emitReserved("upgradeError",f)};function o(){c("transport closed")}function l(){c("socket closed")}function h(p){s&&p.name!==s.name&&i()}const u=()=>{s.removeListener("open",n),s.removeListener("error",c),s.removeListener("close",o),this.off("close",l),this.off("upgrading",h)};s.once("open",n),s.once("error",c),s.once("close",o),this.once("close",l),this.once("upgrading",h),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{a||s.open()},200):s.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const s=[];for(let a=0;a<t.length;a++)~this.transports.indexOf(t[a])&&s.push(t[a]);return s}}let $c=class extends Dc{constructor(t,s={}){const a=typeof t=="object"?t:s;(!a.transports||a.transports&&typeof a.transports[0]=="string")&&(a.transports=(a.transports||["polling","websocket","webtransport"]).map(n=>Ac[n]).filter(n=>!!n)),super(t,a)}};function Lc(r,t="",s){let a=r;s=s||typeof location<"u"&&location,r==null&&(r=s.protocol+"//"+s.host),typeof r=="string"&&(r.charAt(0)==="/"&&(r.charAt(1)==="/"?r=s.protocol+r:r=s.host+r),/^(https?|wss?):\/\//.test(r)||(typeof s<"u"?r=s.protocol+"//"+r:r="https://"+r),a=Ir(r)),a.port||(/^(http|ws)$/.test(a.protocol)?a.port="80":/^(http|ws)s$/.test(a.protocol)&&(a.port="443")),a.path=a.path||"/";const i=a.host.indexOf(":")!==-1?"["+a.host+"]":a.host;return a.id=a.protocol+"://"+i+":"+a.port+t,a.href=a.protocol+"://"+i+(s&&s.port===a.port?"":":"+a.port),a}const Fc=typeof ArrayBuffer=="function",Mc=r=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(r):r.buffer instanceof ArrayBuffer,Yn=Object.prototype.toString,Uc=typeof Blob=="function"||typeof Blob<"u"&&Yn.call(Blob)==="[object BlobConstructor]",qc=typeof File=="function"||typeof File<"u"&&Yn.call(File)==="[object FileConstructor]";function ca(r){return Fc&&(r instanceof ArrayBuffer||Mc(r))||Uc&&r instanceof Blob||qc&&r instanceof File}function Fs(r,t){if(!r||typeof r!="object")return!1;if(Array.isArray(r)){for(let s=0,a=r.length;s<a;s++)if(Fs(r[s]))return!0;return!1}if(ca(r))return!0;if(r.toJSON&&typeof r.toJSON=="function"&&arguments.length===1)return Fs(r.toJSON(),!0);for(const s in r)if(Object.prototype.hasOwnProperty.call(r,s)&&Fs(r[s]))return!0;return!1}function Bc(r){const t=[],s=r.data,a=r;return a.data=_r(s,t),a.attachments=t.length,{packet:a,buffers:t}}function _r(r,t){if(!r)return r;if(ca(r)){const s={_placeholder:!0,num:t.length};return t.push(r),s}else if(Array.isArray(r)){const s=new Array(r.length);for(let a=0;a<r.length;a++)s[a]=_r(r[a],t);return s}else if(typeof r=="object"&&!(r instanceof Date)){const s={};for(const a in r)Object.prototype.hasOwnProperty.call(r,a)&&(s[a]=_r(r[a],t));return s}return r}function Qc(r,t){return r.data=Tr(r.data,t),delete r.attachments,r}function Tr(r,t){if(!r)return r;if(r&&r._placeholder===!0){if(typeof r.num=="number"&&r.num>=0&&r.num<t.length)return t[r.num];throw new Error("illegal attachments")}else if(Array.isArray(r))for(let s=0;s<r.length;s++)r[s]=Tr(r[s],t);else if(typeof r=="object")for(const s in r)Object.prototype.hasOwnProperty.call(r,s)&&(r[s]=Tr(r[s],t));return r}const zc=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Wc=5;var pe;(function(r){r[r.CONNECT=0]="CONNECT",r[r.DISCONNECT=1]="DISCONNECT",r[r.EVENT=2]="EVENT",r[r.ACK=3]="ACK",r[r.CONNECT_ERROR=4]="CONNECT_ERROR",r[r.BINARY_EVENT=5]="BINARY_EVENT",r[r.BINARY_ACK=6]="BINARY_ACK"})(pe||(pe={}));class Hc{constructor(t){this.replacer=t}encode(t){return(t.type===pe.EVENT||t.type===pe.ACK)&&Fs(t)?this.encodeAsBinary({type:t.type===pe.EVENT?pe.BINARY_EVENT:pe.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let s=""+t.type;return(t.type===pe.BINARY_EVENT||t.type===pe.BINARY_ACK)&&(s+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(s+=t.nsp+","),t.id!=null&&(s+=t.id),t.data!=null&&(s+=JSON.stringify(t.data,this.replacer)),s}encodeAsBinary(t){const s=Bc(t),a=this.encodeAsString(s.packet),n=s.buffers;return n.unshift(a),n}}function za(r){return Object.prototype.toString.call(r)==="[object Object]"}class da extends Le{constructor(t){super(),this.reviver=t}add(t){let s;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");s=this.decodeString(t);const a=s.type===pe.BINARY_EVENT;a||s.type===pe.BINARY_ACK?(s.type=a?pe.EVENT:pe.ACK,this.reconstructor=new Kc(s),s.attachments===0&&super.emitReserved("decoded",s)):super.emitReserved("decoded",s)}else if(ca(t)||t.base64)if(this.reconstructor)s=this.reconstructor.takeBinaryData(t),s&&(this.reconstructor=null,super.emitReserved("decoded",s));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let s=0;const a={type:Number(t.charAt(0))};if(pe[a.type]===void 0)throw new Error("unknown packet type "+a.type);if(a.type===pe.BINARY_EVENT||a.type===pe.BINARY_ACK){const i=s+1;for(;t.charAt(++s)!=="-"&&s!=t.length;);const c=t.substring(i,s);if(c!=Number(c)||t.charAt(s)!=="-")throw new Error("Illegal attachments");a.attachments=Number(c)}if(t.charAt(s+1)==="/"){const i=s+1;for(;++s&&!(t.charAt(s)===","||s===t.length););a.nsp=t.substring(i,s)}else a.nsp="/";const n=t.charAt(s+1);if(n!==""&&Number(n)==n){const i=s+1;for(;++s;){const c=t.charAt(s);if(c==null||Number(c)!=c){--s;break}if(s===t.length)break}a.id=Number(t.substring(i,s+1))}if(t.charAt(++s)){const i=this.tryParse(t.substr(s));if(da.isPayloadValid(a.type,i))a.data=i;else throw new Error("invalid payload")}return a}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,s){switch(t){case pe.CONNECT:return za(s);case pe.DISCONNECT:return s===void 0;case pe.CONNECT_ERROR:return typeof s=="string"||za(s);case pe.EVENT:case pe.BINARY_EVENT:return Array.isArray(s)&&(typeof s[0]=="number"||typeof s[0]=="string"&&zc.indexOf(s[0])===-1);case pe.ACK:case pe.BINARY_ACK:return Array.isArray(s)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Kc{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const s=Qc(this.reconPack,this.buffers);return this.finishedReconstruction(),s}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Yc=Object.freeze(Object.defineProperty({__proto__:null,Decoder:da,Encoder:Hc,get PacketType(){return pe},protocol:Wc},Symbol.toStringTag,{value:"Module"}));function mt(r,t,s){return r.on(t,s),function(){r.off(t,s)}}const Gc=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Gn extends Le{constructor(t,s,a){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=s,a&&a.auth&&(this.auth=a.auth),this._opts=Object.assign({},a),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[mt(t,"open",this.onopen.bind(this)),mt(t,"packet",this.onpacket.bind(this)),mt(t,"error",this.onerror.bind(this)),mt(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...s){var a,n,i;if(Gc.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(s.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(s),this;const c={type:pe.EVENT,data:s};if(c.options={},c.options.compress=this.flags.compress!==!1,typeof s[s.length-1]=="function"){const u=this.ids++,p=s.pop();this._registerAckCallback(u,p),c.id=u}const o=(n=(a=this.io.engine)===null||a===void 0?void 0:a.transport)===null||n===void 0?void 0:n.writable,l=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!o||(l?(this.notifyOutgoingListeners(c),this.packet(c)):this.sendBuffer.push(c)),this.flags={},this}_registerAckCallback(t,s){var a;const n=(a=this.flags.timeout)!==null&&a!==void 0?a:this._opts.ackTimeout;if(n===void 0){this.acks[t]=s;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let o=0;o<this.sendBuffer.length;o++)this.sendBuffer[o].id===t&&this.sendBuffer.splice(o,1);s.call(this,new Error("operation has timed out"))},n),c=(...o)=>{this.io.clearTimeoutFn(i),s.apply(this,o)};c.withError=!0,this.acks[t]=c}emitWithAck(t,...s){return new Promise((a,n)=>{const i=(c,o)=>c?n(c):a(o);i.withError=!0,s.push(i),this.emit(t,...s)})}_addToQueue(t){let s;typeof t[t.length-1]=="function"&&(s=t.pop());const a={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((n,...i)=>a!==this._queue[0]?void 0:(n!==null?a.tryCount>this._opts.retries&&(this._queue.shift(),s&&s(n)):(this._queue.shift(),s&&s(null,...i)),a.pending=!1,this._drainQueue())),this._queue.push(a),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const s=this._queue[0];s.pending&&!t||(s.pending=!0,s.tryCount++,this.flags=s.flags,this.emit.apply(this,s.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:pe.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,s){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,s),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(a=>String(a.id)===t)){const a=this.acks[t];delete this.acks[t],a.withError&&a.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case pe.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case pe.EVENT:case pe.BINARY_EVENT:this.onevent(t);break;case pe.ACK:case pe.BINARY_ACK:this.onack(t);break;case pe.DISCONNECT:this.ondisconnect();break;case pe.CONNECT_ERROR:this.destroy();const a=new Error(t.data.message);a.data=t.data.data,this.emitReserved("connect_error",a);break}}onevent(t){const s=t.data||[];t.id!=null&&s.push(this.ack(t.id)),this.connected?this.emitEvent(s):this.receiveBuffer.push(Object.freeze(s))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const s=this._anyListeners.slice();for(const a of s)a.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const s=this;let a=!1;return function(...n){a||(a=!0,s.packet({type:pe.ACK,id:t,data:n}))}}onack(t){const s=this.acks[t.id];typeof s=="function"&&(delete this.acks[t.id],s.withError&&t.data.unshift(null),s.apply(this,t.data))}onconnect(t,s){this.id=t,this.recovered=s&&this._pid===s,this._pid=s,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:pe.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const s=this._anyListeners;for(let a=0;a<s.length;a++)if(t===s[a])return s.splice(a,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const s=this._anyOutgoingListeners;for(let a=0;a<s.length;a++)if(t===s[a])return s.splice(a,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const s=this._anyOutgoingListeners.slice();for(const a of s)a.apply(this,t.data)}}}function es(r){r=r||{},this.ms=r.min||100,this.max=r.max||1e4,this.factor=r.factor||2,this.jitter=r.jitter>0&&r.jitter<=1?r.jitter:0,this.attempts=0}es.prototype.duration=function(){var r=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),s=Math.floor(t*this.jitter*r);r=Math.floor(t*10)&1?r+s:r-s}return Math.min(r,this.max)|0};es.prototype.reset=function(){this.attempts=0};es.prototype.setMin=function(r){this.ms=r};es.prototype.setMax=function(r){this.max=r};es.prototype.setJitter=function(r){this.jitter=r};class Or extends Le{constructor(t,s){var a;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(s=t,t=void 0),s=s||{},s.path=s.path||"/socket.io",this.opts=s,nr(this,s),this.reconnection(s.reconnection!==!1),this.reconnectionAttempts(s.reconnectionAttempts||1/0),this.reconnectionDelay(s.reconnectionDelay||1e3),this.reconnectionDelayMax(s.reconnectionDelayMax||5e3),this.randomizationFactor((a=s.randomizationFactor)!==null&&a!==void 0?a:.5),this.backoff=new es({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(s.timeout==null?2e4:s.timeout),this._readyState="closed",this.uri=t;const n=s.parser||Yc;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=s.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var s;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(s=this.backoff)===null||s===void 0||s.setMin(t),this)}randomizationFactor(t){var s;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(s=this.backoff)===null||s===void 0||s.setJitter(t),this)}reconnectionDelayMax(t){var s;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(s=this.backoff)===null||s===void 0||s.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new $c(this.uri,this.opts);const s=this.engine,a=this;this._readyState="opening",this.skipReconnect=!1;const n=mt(s,"open",function(){a.onopen(),t&&t()}),i=o=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",o),t?t(o):this.maybeReconnectOnOpen()},c=mt(s,"error",i);if(this._timeout!==!1){const o=this._timeout,l=this.setTimeoutFn(()=>{n(),i(new Error("timeout")),s.close()},o);this.opts.autoUnref&&l.unref(),this.subs.push(()=>{this.clearTimeoutFn(l)})}return this.subs.push(n),this.subs.push(c),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(mt(t,"ping",this.onping.bind(this)),mt(t,"data",this.ondata.bind(this)),mt(t,"error",this.onerror.bind(this)),mt(t,"close",this.onclose.bind(this)),mt(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(s){this.onclose("parse error",s)}}ondecoded(t){ar(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,s){let a=this.nsps[t];return a?this._autoConnect&&!a.active&&a.connect():(a=new Gn(this,t,s),this.nsps[t]=a),a}_destroy(t){const s=Object.keys(this.nsps);for(const a of s)if(this.nsps[a].active)return;this._close()}_packet(t){const s=this.encoder.encode(t);for(let a=0;a<s.length;a++)this.engine.write(s[a],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,s){var a;this.cleanup(),(a=this.engine)===null||a===void 0||a.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,s),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const s=this.backoff.duration();this._reconnecting=!0;const a=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(n=>{n?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",n)):t.onreconnect()}))},s);this.opts.autoUnref&&a.unref(),this.subs.push(()=>{this.clearTimeoutFn(a)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const ts={};function Ms(r,t){typeof r=="object"&&(t=r,r=void 0),t=t||{};const s=Lc(r,t.path||"/socket.io"),a=s.source,n=s.id,i=s.path,c=ts[n]&&i in ts[n].nsps,o=t.forceNew||t["force new connection"]||t.multiplex===!1||c;let l;return o?l=new Or(a,t):(ts[n]||(ts[n]=new Or(a,t)),l=ts[n]),s.query&&!t.query&&(t.query=s.queryKey),l.socket(s.path,t)}Object.assign(Ms,{Manager:Or,Socket:Gn,io:Ms,connect:Ms});function Jc(){const r=window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1",t="http://localhost:8001";return{wsUrl:(t&&t.trim()!==""?t:r?"http://localhost:8001":window.location.origin).replace(/\/$/,""),isLocal:r}}var en,tn;const Xc=((tn=(en=import.meta)==null?void 0:en.env)==null?void 0:tn.DEV)??!1;function Es(r,...t){var a;if(!Xc&&r==="debug")return;const s=`[WS:${r.toUpperCase()}]`;(a=console[r==="debug"?"log":r])==null||a.call(console,s,...t)}const Ie={debug:(...r)=>Es("debug",...r),info:(...r)=>Es("info",...r),warn:(...r)=>Es("warn",...r),error:(...r)=>Es("error",...r)};class Zc{constructor(){_e(this,"socket",null);_e(this,"connectionStatus","disconnected");_e(this,"reconnectAttempts",0);_e(this,"maxReconnectAttempts",5);_e(this,"listeners",new Map);_e(this,"statusListeners",new Set);_e(this,"desiredRestaurantId",null);_e(this,"joinedRestaurantId",null);_e(this,"reconnectTimer",null);_e(this,"lastWsUrl",null);_e(this,"lastAuthToken",null);_e(this,"storageListenerAttached",!1);_e(this,"reconnecting",!1);_e(this,"eventAliases",{newSuggestion:"new-suggestion",voteUpdate:"vote-update",queueUpdate:"queue-update",queue_updated:"queue-update","playback-state-changed":"playback-state-update",track_ended:"song-ended",superVote_received:"superVoteReceived","super-vote-received":"superVoteReceived",super_vote_received:"superVoteReceived"});try{this.desiredRestaurantId=typeof localStorage<"u"?localStorage.getItem("currentRestaurantId"):null}catch{}this.connect()}getWsConfig(){return Jc()}connect(){const{wsUrl:t,isLocal:s}=this.getWsConfig();this.setConnectionStatus("connecting");try{const a=this.getAuthToken();this.lastWsUrl=t,this.lastAuthToken=a,this.socket=Ms(t,{transports:["websocket"],timeout:15e3,reconnection:!0,reconnectionAttempts:1/0,reconnectionDelay:1500,reconnectionDelayMax:6e3,randomizationFactor:.25,autoConnect:!0,auth:a?{token:a,restaurantId:this.desiredRestaurantId||void 0}:void 0,withCredentials:!0}),this.setupEventListeners()}catch(a){console.warn("WebSocket não disponível:",a),this.setConnectionStatus("disconnected")}}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{Ie.info("✅ WebSocket conectado"),this.setConnectionStatus("connected"),this.reconnectAttempts=0,this.ensureJoinedRestaurant()}),this.socket.on("disconnect",t=>{var s;Ie.warn("❌ WebSocket desconectado:",t),this.setConnectionStatus("disconnected"),t==="io server disconnect"&&((s=this.socket)==null||s.connect()),this.joinedRestaurantId=null}),this.socket.on("connect_error",t=>{Ie.warn("WebSocket connection failed:",t.message),this.setConnectionStatus("error"),this.reconnectAttempts++}),this.socket.on("reconnect",t=>{Ie.info(`🔄 WebSocket reconectado após ${t} tentativas`),this.setConnectionStatus("connected")}),this.socket.on("reconnect_attempt",t=>{Ie.debug(`🔄 Tentativa de reconexão ${t}`),this.setConnectionStatus("connecting")}),this.socket.on("reconnect_error",t=>{Ie.error("❌ Erro na reconexão:",t),this.setConnectionStatus("error")}),this.socket.on("reconnect_failed",()=>{Ie.error("❌ Falha na reconexão após máximo de tentativas"),this.setConnectionStatus("error")}),this.setupApplicationEvents(),this.storageListenerAttached||(window.addEventListener("storage",t=>{t.key==="authToken"&&this.reconnect(),t.key==="currentRestaurantId"&&(this.desiredRestaurantId=t.newValue,this.connectionStatus==="connected"&&this.ensureJoinedRestaurant())}),this.storageListenerAttached=!0))}setupApplicationEvents(){if(this.socket){this.socket.on("notification",t=>{this.emit("notification",t)}),this.socket.on("joined-restaurant",t=>{t&&(this.joinedRestaurantId=t)}),this.socket.on("left-restaurant",t=>{this.joinedRestaurantId===t&&(this.joinedRestaurantId=null)});for(const[t,s]of Object.entries(this.eventAliases))this.socket.on(t,a=>{try{s==="superVoteReceived"?this.handleSuperVoteSocketEvent(a):this.emit(s,a)}catch(n){Ie.error(`Falha ao tratar alias de evento ${t} -> ${String(s)}`,n)}});this.socket.on("new-suggestion",t=>{!t||!("title"in t)||(Ie.info("🎵 Nova sugestão recebida:",t),this.emit("new-suggestion",t),v.success(`Nova música sugerida: ${t.title}`,{duration:4e3}))}),this.socket.on("vote-update",t=>{t&&(Ie.debug("👍 Atualização de votos:",t),this.emit("vote-update",t))}),this.socket.on("queue-update",t=>{t&&(Ie.debug("📋 Fila atualizada:",t),this.emit("queue-update",t))}),this.socket.on("playback-state-update",t=>{Ie.debug("🎚️ Estado do player atualizado:",t),this.emit("playback-state-update",t)}),this.socket.on("now-playing",t=>{Ie.info("🎵 Tocando agora:",t),this.emit("now-playing",t),v(`Tocando agora: ${t.suggestion.title}`,{icon:"🎵",duration:6e3})}),this.socket.on("playbackStart",t=>{Ie.info("▶️ (camelCase) Início de reprodução:",t),this.emit("now-playing",{suggestion:t.track})}),this.socket.on("playlistReordered",t=>{console.log("🔄 Playlist reordenada:",t),this.emit("playlistReordered",t)}),this.socket.on("playback_started",t=>{var n;Ie.info("▶️ (snake) Início de reprodução:",t);const s=(t==null?void 0:t.track)||((n=t==null?void 0:t.state)==null?void 0:n.currentTrack)||t,a={id:s==null?void 0:s.id,title:s==null?void 0:s.title,artist:s==null?void 0:s.artist,youtubeId:(s==null?void 0:s.youtubeVideoId)||(s==null?void 0:s.youtubeId),thumbnailUrl:s==null?void 0:s.thumbnailUrl,duration:s==null?void 0:s.duration};this.emit("now-playing",a)}),this.socket.on("playlistReorderedAdmin",t=>{console.log("🔄 (admin) Playlist reordenada (admin):",t),this.emit("playlistReordered",t),this.emit("playlistReorderedAdmin",t)}),this.socket.on("ranking-snapshot",t=>{console.log("📸 Snapshot de ranking:",t),this.emit("ranking-snapshot",t)}),this.socket.on("reorderSelected",t=>{try{Ie.info("🎯 Próxima selecionada (server):",t),this.emit("reorderSelected",t)}catch(s){console.warn("Falha ao tratar reorderSelected:",s)}}),this.socket.on("reorder-selected",t=>{try{Ie.info("🎯 (kebab) Próxima selecionada (server):",t),this.emit("reorderSelected",t)}catch{}}),this.socket.on("song-ended",t=>{Ie.info("⏭️ Música finalizada:",t),this.emit("song-ended",t)}),this.socket.on("superVoteReceived",t=>{this.handleSuperVoteSocketEvent(t)})}}handleSuperVoteSocketEvent(t){try{const a=t&&typeof t=="object"&&("data"in t||"event"in t)?t.data??t:t,n=a&&(a.payment||a.suggestion);Ie.info("⭐ SuperVoto recebido:",t),n||Ie.error("Formato inesperado para superVoteReceived",{raw:t}),this.emit("superVoteReceived",t)}catch(s){Ie.error("Erro ao processar evento superVoteReceived:",s)}}getAuthToken(){try{const t=typeof localStorage<"u"?localStorage.getItem("authToken"):null;return t||Ie.warn("Nenhum authToken presente no localStorage (WS seguirá sem auth)"),t}catch(t){return Ie.error("Erro ao ler authToken do localStorage:",t),null}}setConnectionStatus(t){this.connectionStatus=t,this.statusListeners.forEach(s=>s(t))}offConnectionStatusChange(t){this.statusListeners.delete(t)}joinRestaurant(t){this.desiredRestaurantId=t,this.ensureJoinedRestaurant()}leaveRestaurant(t){this.socket&&this.connectionStatus==="connected"&&(this.socket.emit("leave-restaurant",t),console.log(`🚪 Saiu da sala do restaurante: ${t}`)),this.desiredRestaurantId===t&&(this.desiredRestaurantId=null),this.joinedRestaurantId===t&&(this.joinedRestaurantId=null)}on(t,s){this.listeners.has(t)||this.listeners.set(t,new Set);const a=this.listeners.get(t);if(a.has(s)){Ie.warn(`Callback já registrado para o evento ${String(t)}`);return}a.size>100&&Ie.warn("Muitos listeners no evento",String(t),a.size),a.add(s)}off(t,s){const a=this.listeners.get(t);a&&a.delete(s)}emit(t,s){const a=this.listeners.get(t);a&&a.forEach(n=>{try{n(s)}catch(i){console.warn("Erro em listener de evento WS",t,i)}})}publicEmit(t,s){if(!(!this.socket||this.connectionStatus!=="connected"))try{this.socket.emit(t,s)}catch(a){console.warn("Falha ao emitir evento via WS:",t,a)}}onConnectionStatusChange(t){return this.statusListeners.add(t),()=>{this.statusListeners.delete(t)}}subscribe(t,s){return this.on(t,s),()=>this.off(t,s)}getConnectionStatus(){return this.connectionStatus}isConnected(){return this.connectionStatus==="connected"}onConnectionChange(t){this.onConnectionStatusChange(s=>{t(s==="connected")})}reconnect(){this.reconnecting||(this.reconnecting=!0,this.reconnectTimer&&(window.clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.reconnectTimer=window.setTimeout(()=>{this.reconnectTimer=null;const{wsUrl:t}=this.getWsConfig(),s=this.getAuthToken(),a=this.lastWsUrl===t,n=this.lastAuthToken===s;if(this.socket&&a&&n)if(this.socket.connected)this.ensureJoinedRestaurant();else try{this.socket.connect()}catch{}else this.safeTeardown(),this.connect();this.reconnecting=!1},300))}disconnect(){this.safeTeardown(),this.setConnectionStatus("disconnected")}removeAllListeners(){this.listeners.clear(),this.statusListeners.clear()}safeTeardown(){var t,s;if(this.socket){try{(s=(t=this.socket).removeAllListeners)==null||s.call(t)}catch{}try{this.socket.disconnect()}catch{}this.socket=null}this.joinedRestaurantId=null}ensureJoinedRestaurant(){if(!this.socket||this.connectionStatus!=="connected")return;const t=this.desiredRestaurantId;if(!(!t||this.joinedRestaurantId===t))try{const s=this.getAuthToken();s&&this.socket.emit("authenticate",{token:s,restaurantId:t});try{this.socket.emit("joinRoom",{restaurantId:t})}catch{}this.socket.emit("join-restaurant",t,a=>{if(a===!1){this.joinedRestaurantId=t;return}this.joinedRestaurantId=t}),Ie.info(`🏪 Entrou na sala do restaurante: ${t}`)}catch(s){Ie.error("Falha ao entrar na sala do restaurante via WS:",s)}}getDebugInfo(){var t,s,a,n,i;return{connected:this.isConnected(),status:this.connectionStatus,reconnectAttempts:this.reconnectAttempts,socketId:(t=this.socket)==null?void 0:t.id,transport:(i=(n=(a=(s=this.socket)==null?void 0:s.io)==null?void 0:a.engine)==null?void 0:n.transport)==null?void 0:i.name,listenersCount:Array.from(this.listeners.entries()).reduce((c,[o,l])=>({...c,[o]:l.size}),{})}}}const De=new Zc,qt=r=>{const t=d.useSyncExternalStore(a=>De.onConnectionStatusChange(a),()=>De.getConnectionStatus(),()=>"disconnected"),s=t==="connected";return d.useEffect(()=>{if(r)return De.joinRestaurant(r),()=>{De.leaveRestaurant(r)}},[r]),d.useMemo(()=>({service:De,isConnected:s,status:t,joinRestaurant:De.joinRestaurant.bind(De),leaveRestaurant:De.leaveRestaurant.bind(De),on:De.on.bind(De),off:De.off.bind(De),reconnect:De.reconnect.bind(De),emit:De.publicEmit.bind(De),onConnectionStatusChange:De.onConnectionStatusChange.bind(De)}),[t,s,r])},ed=Object.freeze(Object.defineProperty({__proto__:null,default:De,useWebSocket:qt,wsService:De},Symbol.toStringTag,{value:"Module"})),td=d.lazy(()=>pt(()=>import("./PlaylistManager-ffb01299.js"),["assets/PlaylistManager-ffb01299.js","assets/vendor-66b0ef43.js","assets/utils-08f61814.js","assets/ui-d6218fb3.js","assets/router-f729e475.js"])),sd=d.lazy(()=>pt(()=>import("./MusicPlayer-5fcee38a.js"),["assets/MusicPlayer-5fcee38a.js","assets/vendor-66b0ef43.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),rd=d.lazy(()=>pt(()=>Promise.resolve().then(()=>jd),void 0)),ad=d.lazy(()=>pt(()=>import("./UnifiedAnalytics-164ae950.js"),["assets/UnifiedAnalytics-164ae950.js","assets/vendor-66b0ef43.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),nd=d.lazy(()=>pt(()=>import("./GenreManager-8dcf03fa.js"),["assets/GenreManager-8dcf03fa.js","assets/vendor-66b0ef43.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Wa=d.lazy(()=>pt(()=>import("./RestaurantProfile-8ad714fb.js"),["assets/RestaurantProfile-8ad714fb.js","assets/vendor-66b0ef43.js","assets/YouTubeAuthManager-18e1f41f.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),id=d.lazy(()=>pt(()=>import("./QRCodeManager-9e14fa3f.js"),["assets/QRCodeManager-9e14fa3f.js","assets/vendor-66b0ef43.js","assets/router-f729e475.js","assets/ui-d6218fb3.js","assets/utils-08f61814.js"])),od=d.lazy(()=>pt(()=>import("./ProblematicTracksAlert-c0b02153.js"),["assets/ProblematicTracksAlert-c0b02153.js","assets/vendor-66b0ef43.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"]));d.lazy(()=>pt(()=>import("./RestaurantSettings-7f0630a1.js"),["assets/RestaurantSettings-7f0630a1.js","assets/vendor-66b0ef43.js","assets/YouTubeAuthManager-18e1f41f.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"]));const ld=d.lazy(()=>pt(()=>import("./EnhancedRestaurantProfile-7329a68b.js"),["assets/EnhancedRestaurantProfile-7329a68b.js","assets/vendor-66b0ef43.js","assets/ui-d6218fb3.js","assets/router-f729e475.js","assets/utils-08f61814.js"])),Jn=d.createContext({restaurantId:"",isConnected:!1,lastUpdate:new Date}),Xn=()=>d.useContext(Jn);class Ct{static setCache(t,s,a=5*60*1e3){const n={data:s,timestamp:Date.now(),ttl:a};localStorage.setItem(this.CACHE_PREFIX+t,JSON.stringify(n))}static getCache(t){try{const s=localStorage.getItem(this.CACHE_PREFIX+t);if(!s)return null;const{data:a,timestamp:n,ttl:i}=JSON.parse(s);return Date.now()-n>i?(localStorage.removeItem(this.CACHE_PREFIX+t),null):a}catch{return null}}static clearCache(){Object.keys(localStorage).filter(t=>t.startsWith(this.CACHE_PREFIX)).forEach(t=>localStorage.removeItem(t))}}_e(Ct,"CACHE_PREFIX","restaurant_dashboard_");const Vr=()=>e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 animate-pulse",children:[e.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"}),e.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]}),cd=ce.memo(({title:r,value:t,change:s,icon:a,color:n,bgColor:i,description:c})=>e.jsx(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow",children:e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:r}),e.jsx("div",{className:`w-10 h-10 rounded-lg ${i} flex items-center justify-center`,children:e.jsx(a,{className:`w-5 h-5 ${n}`})})]}),e.jsx("p",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-1",children:t}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:c}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:`text-sm font-medium ${s.startsWith("+")?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400"}`,children:s}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400 ml-1",children:"hoje"})]})]})})})),dd=()=>{const{restaurantId:r,isConnected:t}=Xn(),s=na(),{on:a,off:n,emit:i,joinRestaurant:c,leaveRestaurant:o}=qt(),[l,h]=d.useState({}),[u,p]=d.useState([]),[f,m]=d.useState([]),[x,g]=d.useState(new Date),{data:w,isLoading:b,error:k}=Vs(["analytics",r],async()=>{try{const A=await Ge.getAnalytics(r);return Ct.setCache(`stats_${r}`,A),A}catch(A){const z=Ct.getCache(`stats_${r}`);if(z)return v("Usando dados em cache devido à falha de conexão",{icon:"ℹ️"}),z;throw A}},{staleTime:2*60*1e3,retry:3,retryDelay:A=>Math.min(1e3*2**A,3e4)}),{data:S,isLoading:J}=Vs(["suggestions",r],async()=>{try{const A=await Ge.getSuggestions(r,{limit:10});return Ct.setCache(`suggestions_${r}`,A),A}catch(A){const z=Ct.getCache(`suggestions_${r}`);if(z)return z;throw A}},{staleTime:30*1e3,retry:2}),{data:Z,isLoading:se}=Vs(["queue",r],async()=>{try{const A=await Ge.getPlayQueue(r);return Ct.setCache(`queue_${r}`,A),A}catch(A){const z=Ct.getCache(`queue_${r}`);if(z)return z;throw A}},{staleTime:15*1e3,retry:2}),N=d.useCallback(A=>A.reduce((z,le,X)=>X===0?0:z+(le.duration||180),0),[]);d.useEffect(()=>{if(!r||!t)return;const A=U=>{p(I=>[{...U,type:"suggestion"},...I.slice(0,9)]),h(I=>{var ee,Y;return{...I,totalSuggestions:(I.totalSuggestions||0)+1,dailyStats:{...I.dailyStats,suggestions:(((ee=I.dailyStats)==null?void 0:ee.suggestions)||0)+1,votes:((Y=I.dailyStats)==null?void 0:Y.votes)||0}}}),v.success(`🎵 Nova sugestão: ${U.title}`,{duration:3e3,position:"bottom-right"})},z=U=>{m(U);const I=N(U);h(ee=>({...ee,estimatedWaitTime:I})),s.setQueryData(["queue",r],{queue:U})},le=U=>{h(I=>{var ee,Y;return{...I,totalVotes:(I.totalVotes||0)+1,dailyStats:{suggestions:((ee=I.dailyStats)==null?void 0:ee.suggestions)||0,votes:(((Y=I.dailyStats)==null?void 0:Y.votes)||0)+1}}})},X=U=>{h(I=>({...I,currentlyPlaying:U?{title:U.title,artist:U.artist,remainingTime:U.remainingTime}:void 0}))};return a("new-suggestion",A),a("queue-update",z),a("vote-update",le),a("now-playing",X),c(r),()=>{n("new-suggestion",A),n("queue-update",z),n("vote-update",le),n("now-playing",X),o(r)}},[r,t,a,n,i,s,N]);const $=ce.useMemo(()=>{var z,le;const A=(w==null?void 0:w.summary)||{};return{totalSuggestions:l.totalSuggestions??A.totalSuggestions??0,totalVotes:l.totalVotes??A.totalVotes??0,pendingSuggestions:l.pendingSuggestions??A.pendingSuggestions??0,dailyStats:{suggestions:((z=l.dailyStats)==null?void 0:z.suggestions)??A.dailySuggestions??0,votes:((le=l.dailyStats)==null?void 0:le.votes)??A.dailyVotes??0},totalPlays:l.totalPlays??A.totalPlays??0,activeUsers:l.activeUsers??A.activeUsers??0,averageRating:l.averageRating??A.averageRating??0,growthRate:l.growthRate??A.growthRate??0,peakHour:l.peakHour??A.peakHour??"0:00",topGenre:l.topGenre??A.topGenre??"N/A",estimatedWaitTime:l.estimatedWaitTime,currentlyPlaying:l.currentlyPlaying}},[w,l]),W=ce.useMemo(()=>{const A=(S==null?void 0:S.suggestions)||[];return[...u,...A.map(X=>({id:X.id||`suggestion-${Date.now()}-${Math.random()}`,title:X.title||"Título não disponível",artist:X.artist||"Artista desconhecido",createdAt:X.createdAt||new Date().toISOString(),upvotes:X.upvotes||0,type:"suggestion"}))].filter((X,U,I)=>I.findIndex(ee=>ee.id===X.id)===U).slice(0,10)},[S,u]),P=ce.useMemo(()=>{const A=(Z==null?void 0:Z.queue)||[];return(f.length>0?f:A).slice(0,10).map((le,X)=>{const U=new Date;return U.setSeconds(U.getSeconds()+X*180),{id:le.id||`queue-${Date.now()}-${Math.random()}`,title:le.title||"Título não disponível",artist:le.artist||"Artista desconhecido",upvotes:le.upvotes||0,downvotes:le.downvotes||0,duration:le.duration||180,priority:le.priority||"normal",estimatedPlayTime:U}})},[Z,f]),H=ce.useMemo(()=>[{title:"Sugestões Hoje",value:$.dailyStats.suggestions.toString(),change:$.growthRate>0?`+${$.growthRate.toFixed(1)}%`:"0%",icon:Ce,color:"text-blue-600 dark:text-blue-400",bgColor:"bg-blue-100 dark:bg-blue-900/20",description:"Novas sugestões recebidas hoje",trend:$.growthRate>0?"up":"stable"},{title:"Total de Votos",value:$.totalVotes.toString(),change:`+${$.dailyStats.votes}`,icon:As,color:"text-green-600 dark:text-green-400",bgColor:"bg-green-100 dark:bg-green-900/20",description:"Votos acumulados de clientes",trend:$.dailyStats.votes>0?"up":"stable"},{title:"Fila de Espera",value:P.length.toString(),change:$.estimatedWaitTime?`${Math.round($.estimatedWaitTime/60)}min`:"0min",icon:Pt,color:"text-yellow-600 dark:text-yellow-400",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",description:$.estimatedWaitTime?"Tempo estimado de espera":"Fila vazia",trend:P.length>5?"up":P.length>0?"stable":"down"},{title:"Usuários Ativos",value:$.activeUsers.toString(),change:"+0.2",icon:yt,color:"text-purple-600 dark:text-purple-400",bgColor:"bg-purple-100 dark:bg-purple-900/20",description:"Clientes interagindo agora",trend:$.activeUsers>0?"up":"stable"}],[$,P.length]),re=b||J||se;return k&&!w?e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(ls,{className:"w-8 h-8 text-red-500"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-red-900 dark:text-red-100",children:"Erro ao carregar dados"}),e.jsx("p",{className:"text-red-700 dark:text-red-300 mt-1",children:"Não foi possível conectar ao servidor. Tentando usar dados em cache..."}),e.jsxs("button",{onClick:()=>{Ct.clearCache(),window.location.reload()},className:"mt-3 flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[e.jsx(Xe,{className:"w-4 h-4"}),e.jsx("span",{children:"Tentar Novamente"})]})]})]})})}):re?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg animate-pulse",children:[e.jsx("div",{className:"h-8 bg-white/20 rounded w-1/2 mb-2"}),e.jsx("div",{className:"h-4 bg-white/20 rounded w-3/4"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:Array(4).fill(0).map((A,z)=>e.jsx(Vr,{},z))})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white shadow-lg",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:"Dashboard do Restaurante"}),e.jsx("p",{className:"text-blue-100 text-lg",children:"Visão geral das atividades e estatísticas em tempo real"}),e.jsxs("div",{className:"flex items-center space-x-6 mt-4",children:[e.jsx("div",{className:"flex items-center space-x-2",children:t?e.jsxs(e.Fragment,{children:[e.jsx(vi,{className:"w-4 h-4 text-green-300"}),e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-sm text-green-100 font-medium",children:"Sistema Online"})]}):e.jsxs(e.Fragment,{children:[e.jsx(wi,{className:"w-4 h-4 text-red-300"}),e.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),e.jsx("span",{className:"text-sm text-red-100",children:"Modo Offline"})]})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Pt,{className:"w-4 h-4 text-blue-200"}),e.jsxs("span",{className:"text-sm text-blue-100",children:["Atualizado:"," ",x.toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit",second:"2-digit"})]})]}),$.currentlyPlaying&&e.jsxs("div",{className:"flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-1",children:[e.jsx(ot,{className:"w-4 h-4 text-green-300"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-green-100 font-medium",children:"Tocando:"}),e.jsxs("span",{className:"text-blue-100 ml-2",children:[$.currentlyPlaying.title," -"," ",$.currentlyPlaying.artist]})]})]})]})]}),e.jsx("div",{className:"hidden md:block",children:e.jsxs("div",{className:"w-24 h-24 bg-white/10 rounded-full flex items-center justify-center backdrop-blur-sm relative",children:[e.jsx(Nt,{className:"w-12 h-12 text-white"}),!t&&e.jsx("div",{className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center",children:e.jsx(ls,{className:"w-4 h-4 text-white"})})]})})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:H.map((A,z)=>e.jsx(cd,{...A},A.title))}),e.jsx(d.Suspense,{fallback:e.jsx(Vr,{}),children:e.jsx(od,{})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Atividade Recente"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Ao vivo"})]})]}),e.jsx("div",{className:"space-y-3",children:W.length>0?W.map(A=>e.jsxs(B.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-sm",children:e.jsx(Ce,{className:"w-5 h-5 text-white"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:['Nova sugestão: "',A.title,'"']}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:A.artist}),e.jsx("span",{className:"text-xs text-gray-400",children:"•"}),e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(A.createdAt).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(As,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-xs text-gray-500",children:A.upvotes||0})]})]},A.id)):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Ce,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Nenhuma sugestão recente"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"As sugestões mais recentes dos clientes aparecerão aqui"}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:"🎵 Compartilhe o QR Code para clientes sugerirem músicas"}),e.jsx("p",{className:"text-xs text-gray-500",children:'Acesse "QR Code" no menu principal'})]})]})})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Fila Atual"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(ot,{className:"w-4 h-4 text-green-500"}),e.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[P.length," músicas"]})]})]}),e.jsx("div",{className:"space-y-3",children:P.length>0?P.map((A,z)=>e.jsxs(B.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:`flex items-center space-x-3 p-3 rounded-lg transition-colors ${z===0?"bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-700":"bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600"}`,children:[e.jsx("div",{className:`w-8 h-8 rounded-full text-xs flex items-center justify-center font-bold ${z===0?"bg-gradient-to-r from-green-500 to-blue-500 text-white shadow-sm":"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}`,children:z===0?e.jsx(ot,{className:"w-3 h-3"}):z+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("p",{className:`text-sm font-medium truncate ${z===0?"text-gray-900 dark:text-white font-semibold":"text-gray-900 dark:text-white"}`,children:A.title}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400 truncate",children:A.artist}),e.jsx("span",{className:"text-xs text-gray-400",children:"•"}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(As,{className:"w-3 h-3 text-green-500"}),e.jsx("span",{className:"text-xs text-gray-500",children:A.upvotes-A.downvotes})]})]})]}),z===0&&e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:"Tocando"})]})]},A.id)):e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ot,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:"Fila de reprodução vazia"}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Aguardando músicas serem aprovadas e adicionadas à fila"}),e.jsx("div",{className:"mt-4 space-y-2",children:e.jsx("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:'💡 Dica: Use "Controle de Reprodução" para acompanhar fila e votação'})})]})})]})]})]})},ud=()=>{var f,m;const r=ps(),t=ci(),{restaurantId:s}=At(),{user:a,setUser:n,setAuthToken:i}=ys(),{status:c}=qt(),{data:o}=Vs(["restaurant",s],()=>Ge.getRestaurant(s),{enabled:!!s,staleTime:5*60*1e3,retry:2}),l=(o==null?void 0:o.name)||(o==null?void 0:o.displayName)||((f=a==null?void 0:a.restaurant)==null?void 0:f.name)||(s?`Restaurante ${s}`:"Restaurante"),h=(o==null?void 0:o.logo)||(o==null?void 0:o.logoUrl)||(o==null?void 0:o.brandLogo)||((m=a==null?void 0:a.restaurant)==null?void 0:m.logo);if(!s)return e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-red-600",children:"Erro de Rota"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"ID do restaurante não fornecido na URL"})]})});const u=()=>{n(null),i(null),localStorage.removeItem("authToken"),v.success("Logout realizado com sucesso!"),r("/")},p=ce.useMemo(()=>[{name:"Player",icon:bi,path:`/restaurant/${s}/dashboard/player`},{name:"Controle de Reprodução",icon:Yt,path:`/restaurant/${s}/dashboard/playback-control`},{name:"Playlists",icon:Ce,path:`/restaurant/${s}/dashboard/playlists`},{name:"Gêneros",icon:Zs,path:`/restaurant/${s}/dashboard/genres`},{name:"Analytics",icon:Nt,path:`/restaurant/${s}/dashboard/analytics`},{name:"QR Code",icon:os,path:`/restaurant/${s}/dashboard/qrcode`}],[s]);return e.jsx(Jn.Provider,{value:{restaurantId:s,isConnected:c==="connected",lastUpdate:new Date},children:e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[e.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(Bt,{to:`/restaurant/${s}/dashboard`,className:"flex items-center space-x-3 hover:opacity-80 transition-all duration-200 hover:scale-105 cursor-pointer",title:"Voltar ao Dashboard Principal",children:[h?e.jsx("div",{className:"w-10 h-10 rounded-lg overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700 bg-white",children:e.jsx("img",{src:h,alt:`${l} logo`,className:"w-full h-full object-cover",onError:x=>{x.currentTarget.style.display="none"}})}):e.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center shadow-lg",children:e.jsx(Ce,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:l}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Dashboard de Gerenciamento"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:l}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Bt,{to:"profile",className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center hover:from-blue-600 hover:to-purple-700 transition-all duration-200 cursor-pointer",title:"Perfil do Restaurante",children:e.jsx(Wt,{className:"w-4 h-4 text-white"})}),e.jsxs("button",{onClick:u,className:"flex items-center space-x-1 px-3 py-2 text-sm text-white bg-red-500 hover:bg-red-600 rounded-lg transition-colors font-medium",title:"Sair",children:[e.jsx(ln,{className:"w-4 h-4"}),e.jsx("span",{children:"Sair"})]})]})]})]})})}),e.jsx("nav",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"flex space-x-8 overflow-x-auto",children:p.map(x=>e.jsxs(Bt,{to:x.path,className:`flex items-center space-x-2 py-4 px-1 border-b-2 text-sm font-medium transition-colors whitespace-nowrap ${t.pathname===x.path?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"}`,children:[e.jsx(x.icon,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"truncate",children:x.name})]},x.name))})})}),e.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsx(d.Suspense,{fallback:e.jsx(Vr,{}),children:e.jsxs(Hr,{children:[e.jsx(be,{index:!0,element:e.jsx(dd,{})}),e.jsx(be,{path:"player",element:e.jsx(sd,{})}),e.jsx(be,{path:"playback-control",element:e.jsx(rd,{})}),e.jsx(be,{path:"playlists",element:e.jsx(td,{})}),e.jsx(be,{path:"genres",element:e.jsx(nd,{})}),e.jsx(be,{path:"qrcode",element:e.jsx(id,{})}),e.jsx(be,{path:"analytics",element:e.jsx(ad,{})}),e.jsx(be,{path:"settings",element:e.jsx(Wa,{})}),e.jsx(be,{path:"profile",element:e.jsx(Wa,{})}),e.jsx(be,{path:"enhanced-profile",element:e.jsx(ld,{})}),e.jsx(be,{path:"suggestions",element:e.jsx(Ft,{to:"playback-control",replace:!0})}),e.jsx(be,{path:"*",element:e.jsx(Ft,{to:".",replace:!0})})]})})})]})})};class ht{static setCache(t,s,a=2*60*1e3){const n={data:s,timestamp:Date.now(),ttl:a};localStorage.setItem(this.CACHE_PREFIX+t,JSON.stringify(n))}static getCache(t){try{const s=localStorage.getItem(this.CACHE_PREFIX+t);if(!s)return null;const{data:a,timestamp:n,ttl:i}=JSON.parse(s);return Date.now()-n>i?(localStorage.removeItem(this.CACHE_PREFIX+t),null):a}catch{return null}}}_e(ht,"CACHE_PREFIX","playback_controller_");const hd=()=>{const{restaurantId:r}=At(),{restaurantId:t,isConnected:s}=Xn(),a=r||t,{on:n,off:i,emit:c,onConnectionStatusChange:o}=qt(),[l,h]=d.useState(null),[u,p]=d.useState(!0),[f,m]=d.useState(null),[x,g]=d.useState([]),[w,b]=d.useState([]),[k,S]=d.useState({totalItems:0,paidItems:0,freeItems:0,estimatedWaitTime:0}),[J,Z]=d.useState(new Date),[se,N]=d.useState(0),[$,W]=d.useState([]),[P,H]=d.useState([]),[re,A]=d.useState(null),[z,le]=d.useState(0),[X,U]=d.useState(!1),[I,ee]=d.useState([]),[Y,ye]=d.useState(0),[te,Ee]=d.useState([]),[xe,Ze]=d.useState(null),[Ve,Qe]=d.useState({totalSuperVotes:0,totalNormalVotes:0,paidItems:0,freeItems:0}),[Ue,K]=d.useState(0),ie=d.useMemo(()=>{const j={};for(const O of[...$,...P])O!=null&&O.youtubeVideoId&&(j[O.youtubeVideoId]=O);return j},[$,P]),Re=d.useMemo(()=>[...x||[],...w||[]],[x,w]),fe=d.useMemo(()=>{const j=O=>{const M=ie[O.youtubeVideoId],y=Number((M==null?void 0:M.isPaid)??O.isPaid??!1),E=Number((M==null?void 0:M.paymentAmount)??0),R=Number((M==null?void 0:M.voteCount)??0);return{isPaid:y,payment:E,votes:R}};return[...Re].sort((O,M)=>{const y=j(O),E=j(M);return y.isPaid!==E.isPaid?E.isPaid-y.isPaid:y.payment!==E.payment?E.payment-y.payment:E.votes-y.votes})},[Re,ie]),[Fe,lt]=d.useState({shuffle:!1,repeat:!1,autoPlay:!1,crossfade:0,lockVoting:!1}),[ze,$e]=d.useState("");d.useState(!1);const qe=d.useRef(null),ke=d.useRef(null),tt=d.useRef(null),Be=d.useRef(null),je=d.useCallback(async(j,O,M=3)=>{for(let y=0;y<M;y++)try{const E={"Content-Type":"application/json",...(O==null?void 0:O.headers)||{}},R=localStorage.getItem("authToken");R&&(E.Authorization=`Bearer ${R}`);const q=await fetch(j,{...O,headers:E});if(!q.ok)throw new Error(`HTTP ${q.status}: ${q.statusText}`);const ne=await q.json();return N(0),ne}catch(E){if(console.error(`API call failed (attempt ${y+1}):`,E),y===M-1)throw N(R=>R+1),v.error(`Falha na conexão. Tentativa ${se+1}`),E;await new Promise(R=>setTimeout(R,Math.pow(2,y)*1e3))}},[se]),Pe=d.useCallback(async()=>{if(a)try{console.log(`[PlaybackController] Loading playback state for restaurant: ${a}`);const j=await je(ge(`/playback/${a}/state`));console.log("[PlaybackController] Playback state response:",j),j!=null&&j.state?(j.state.currentTrack&&j.state.currentTrack.createdAt&&(j.state.currentTrack.createdAt=new Date(j.state.currentTrack.createdAt)),h(j.state),ht.setCache(`playback_${a}`,j.state),console.log("[PlaybackController] Playback state loaded successfully:",j.state)):(console.log("[PlaybackController] No playback state found, setting default state"),h({isPlaying:!1,currentTrack:null,volume:50,currentTime:0,queue:[],priorityQueue:[],normalQueue:[],history:[],connectionStatus:s?"connected":"disconnected"}))}catch(j){console.error("Erro ao carregar estado de reprodução:",j);const O=ht.getCache(`playback_${a}`);O&&(h(O),v("Usando dados em cache devido à falha de conexão",{icon:"ℹ️"}))}finally{p(!1),Z(new Date)}},[a,s,je]),ct=d.useCallback(j=>j.reduce((O,M)=>O+(M.duration||180),0),[]),Ae=d.useCallback(async()=>{if(a)try{const j=await je(ge(`/playback/${a}/state`)),O=(j==null?void 0:j.state)||{},M=(O.priorityQueue||[]).map(R=>({...R,createdAt:R.createdAt?new Date(R.createdAt):new Date,isPaid:!0})),y=(O.normalQueue||[]).map(R=>({...R,createdAt:R.createdAt?new Date(R.createdAt):new Date,isPaid:!1}));g(M),b(y);const E=ct([...M||[],...y||[]]);S({totalItems:((M==null?void 0:M.length)||0)+((y==null?void 0:y.length)||0),paidItems:(M==null?void 0:M.length)||0,freeItems:(y==null?void 0:y.length)||0,estimatedWaitTime:E}),ht.setCache(`queue_${a}`,{priorityQueue:M,normalQueue:y})}catch(j){console.error("Erro ao carregar filas pelo playback/state:",j);const O=ht.getCache(`queue_${a}`);O&&(g(O.priorityQueue||[]),b(O.normalQueue||[]),v("Usando dados de fila em cache",{icon:"ℹ️"}))}},[a,je,ct]),Se=d.useCallback(async()=>{if(a)try{const j=ge(`/collaborative-playlist/${a}/ranking`,{limit:"50"}),O=await fetch(j,{headers:Kt()});if(!O.ok)throw new Error(`Falha ao carregar ranking (${O.status})`);const M=await O.json(),y=(M==null?void 0:M.data)||[],E=L=>({...L,paymentAmount:Number(L.paymentAmount)||0,voteCount:Number(L.voteCount)||0,superVoteCount:Number(L.superVoteCount)||0,normalVoteCount:Number(L.normalVoteCount)||0,totalRevenue:Number(L.totalRevenue)||0,isPaid:!!L.isPaid}),R=y.map(E),q=R.filter(L=>L.isPaid).sort((L,oe)=>{const ve=oe.paymentAmount-L.paymentAmount;return ve!==0?ve:oe.voteCount-L.voteCount}),ne=R.filter(L=>!L.isPaid).sort((L,oe)=>oe.voteCount-L.voteCount);W(q),H(ne)}catch(j){console.warn("Não foi possível obter ranking colaborativo:",j)}},[a]),Ye=d.useCallback(async()=>{if(a)try{const j=ge(`/collaborative-playlist/${a}/stats`),O=await fetch(j,{headers:Kt()});if(!O.ok)throw new Error(`Falha ao carregar stats (${O.status})`);const M=await O.json();Ze((M==null?void 0:M.data)??M??null)}catch(j){console.warn("Não foi possível obter stats colaborativas:",j)}},[a]);d.useEffect(()=>{const j=[...$,...P].sort((E,R)=>{const q=Number(R.isPaid)-Number(E.isPaid);if(q!==0)return q;const ne=R.paymentAmount-E.paymentAmount;return ne!==0?ne:R.voteCount-E.voteCount});ee(j),ye(j.reduce((E,R)=>E+R.voteCount,0));const O=j.reduce((E,R)=>E+R.superVoteCount,0),M=j.reduce((E,R)=>E+R.normalVoteCount,0);Qe({totalSuperVotes:O,totalNormalVotes:M,paidItems:$.length,freeItems:P.length});const y=$.reduce((E,R)=>E+R.paymentAmount,0);K(y)},[$,P]);const dt=d.useCallback(j=>{Be.current&&clearInterval(Be.current);const O=()=>{const M=Math.max(0,Math.floor((j.getTime()-Date.now())/1e3));le(M),M<=0&&(Be.current&&clearInterval(Be.current),Be.current=null,X&&Me())};O(),Be.current=setInterval(O,1e3)},[X]),Me=d.useCallback(async()=>{if(a)try{const j=await je(ge(`/collaborative-playlist/${a}/reorder`),{method:"POST"});v.success("Playlist reordenada por votos"),await Promise.all([Ae(),Se(),Pe()])}catch(j){console.error("Erro ao reordenar por votos:",j),v.error("Erro ao reordenar por votos")}finally{const j=new Date(Date.now()+3e5);A(j),dt(j)}},[a,je,Ae,Se,Pe,dt]);d.useEffect(()=>{if(!a||!s)return;const j=E=>{h(R=>R&&{...R,...E}),Z(new Date),ht.setCache(`playback_${a}`,E)},O=E=>{let R=[],q=[];if(E.priorityQueue||E.normalQueue)R=E.priorityQueue||[],q=E.normalQueue||[];else if(E.queue){const L=E.queue||[];R=L.filter(oe=>oe==null?void 0:oe.isPaid),q=L.filter(oe=>!(oe!=null&&oe.isPaid))}g(R),b(q);const ne=ct([...R||[],...q||[]]);S(L=>({...L,estimatedWaitTime:ne})),ht.setCache(`queue_${a}`,{priorityQueue:R,normalQueue:q})},M=o(E=>{h(R=>R&&{...R,connectionStatus:E})});n("playback-state-update",j),n("queue-update",O);const y=E=>{v.success((E==null?void 0:E.message)||"Playlist reordenada por votos"),Ee(q=>{var ne,L;return[{time:new Date().toISOString(),playlistName:((ne=E==null?void 0:E.playlist)==null?void 0:ne.name)||(E==null?void 0:E.playlistName),count:((L=E==null?void 0:E.playlist)==null?void 0:L.tracksReordered)||(E==null?void 0:E.tracksReordered)||0,details:((E==null?void 0:E.topTracks)||[]).slice(0,5)},...q.slice(0,19)]}),Ae(),Pe(),Se(),Ye();const R=new Date(Date.now()+5*60*1e3);A(R),dt(R)};return n("playlistReordered",y),c("join-restaurant-playback",{restaurantId:a}),()=>{i("playback-state-update",j),i("queue-update",O),M==null||M(),i("playlistReordered",y),c("leave-restaurant-playback",{restaurantId:a})}},[a,s,n,i,c,Ae,Pe,Se,Ye,ct,dt]),d.useEffect(()=>{const j=async()=>{const M=ht.getCache(`playback_${a}`),y=ht.getCache(`queue_${a}`);M&&(h(M),v("Carregando dados em cache...",{duration:2e3,icon:"ℹ️"})),y&&(g(y.priorityQueue||[]),b(y.normalQueue||[])),await Promise.all([Pe(),Ae()])},O=async()=>{await j(),Se(),Ye()};if(O(),setTimeout(()=>{O()},1e3),s||(qe.current=setInterval(()=>{Pe(),Ae()},1e4)),tt.current=setInterval(()=>{Se(),Ye()},15e3),!re){const M=new Date(Date.now()+3e5);A(M),dt(M)}return()=>{qe.current&&clearInterval(qe.current),ke.current&&clearTimeout(ke.current),tt.current&&clearInterval(tt.current),Be.current&&clearInterval(Be.current)}},[a,s,Pe,Ae,Se,Ye,re,dt]);const st=d.useCallback(async j=>{if(a)try{await je(ge(`/collaborative-playlist/${a}/vote`),{method:"POST",body:JSON.stringify({youtubeVideoId:j,clientSessionId:"admin_panel"})}),v.success("Voto registrado (normal)"),await Promise.all([Se(),Ae()])}catch(O){console.error(O),v.error("Falha ao registrar voto (normal)")}},[a,je,Se,Ae]),vt=d.useCallback(async(j,O,M,y)=>{if(a)try{await je(ge(`/collaborative-playlist/${a}/supervote`),{method:"POST",body:JSON.stringify({youtubeVideoId:j,paymentAmount:O,paymentId:`admin_test_${Date.now()}`,clientSessionId:"admin_panel",clientMessage:M||void 0,clientName:y||"Admin"})}),v.success(`Supervoto R$ ${O} registrado`),await Promise.all([Se(),Ae()])}catch(E){console.error(E),v.error("Falha ao registrar supervoto")}},[a,je,Se,Ae]),ut=d.useCallback(j=>{if(!j)return null;if(/^[a-zA-Z0-9_-]{11}$/.test(j))return j;try{const O=new URL(j);return O.hostname.includes("youtu.be")?O.pathname.slice(1):O.searchParams.get("v")}catch{return null}},[]),xt=d.useCallback(j=>{const O=ut(ze.trim());if(!O){v.error("Informe um link ou ID válido do YouTube");return}const M={id:`local_${Date.now()}`,youtubeVideoId:O,title:`Música (${O})`,artist:"Artista Desconhecido",duration:180,thumbnailUrl:`https://img.youtube.com/vi/${O}/mqdefault.jpg`,upvotes:0,downvotes:0,score:0,createdAt:new Date,isPaid:j};j?(g(y=>[M,...y]),v.success("Adicionada à fila prioritária (somente UI)")):(b(y=>[M,...y]),v.success("Adicionada à fila normal (somente UI)")),$e("")},[ze,ut]),Te=d.useCallback(()=>{g([]),b([]),S({totalItems:0,paidItems:0,freeItems:0,estimatedWaitTime:0}),v.success("Filas limpas (somente UI)")},[]),ft=d.useCallback((j,O,M)=>{(j==="priority"?g:b)(E=>{const R=[...E],q=R.findIndex(L=>L.id===O);if(q<0)return E;const ne=M==="up"?q-1:q+1;return ne<0||ne>=R.length?E:([R[q],R[ne]]=[R[ne],R[q]],R)})},[]),at=d.useCallback(j=>{g(O=>{const M=O.findIndex(R=>R.id===j);if(M<0)return O;const y={...O[M],isPaid:!1},E=O.filter((R,q)=>q!==M);return b(R=>[y,...R]),v("Movida para fila normal (somente UI)"),E})},[]),rt=d.useCallback(j=>{lt(O=>({...O,[j]:!O[j]}))},[]),Tt=d.useCallback(async()=>{var j;if(!(!l||!a)){m("playpause");try{const O=l.isPlaying?"pause":"resume";await je(ge(`/playback/${a}/${O}`),{method:"POST"});const M={...l,isPlaying:!l.isPlaying};h(M),ht.setCache(`playback_${a}`,M),v.success(l.isPlaying?"Reprodução pausada":"Reprodução retomada"),me("playback_toggle",{action:O,trackId:(j=l.currentTrack)==null?void 0:j.id})}catch{v.error("Erro ao controlar reprodução")}finally{m(null)}}},[l,a,je]),C=d.useCallback(async()=>{var j;if(a){m("skip");try{await je(ge(`/playback/${a}/skip`),{method:"POST"}),v.success("Música pulada"),me("track_skip",{trackId:(j=l==null?void 0:l.currentTrack)==null?void 0:j.id,skipTime:l==null?void 0:l.currentTime}),await Promise.all([Pe(),Ae()])}catch{v.error("Erro ao pular música")}finally{m(null)}}},[a,l,je,Pe,Ae]),T=d.useCallback(async()=>{if(!a)return;m("start");let j=null,O="";if(x.length>0?(j=x[0],O="priority",v.success(`Tocando da fila prioritária: ${j.title}`)):w.length>0&&(j=[...w].sort((y,E)=>E.upvotes-E.downvotes-(y.upvotes-y.downvotes))[0],O="normal",v.success(`Tocando da fila normal: ${j.title}`)),!j){v.error("Nenhuma música na fila"),m(null);return}try{await je(ge(`/playback/${a}/play`),{method:"POST",body:JSON.stringify({songId:j.id})}),v.success(`Tocando: ${j.title}`),me("track_start",{trackId:j.id,queueType:O,trackTitle:j.title}),await Promise.all([Pe(),Ae()])}catch(M){console.error("Erro ao iniciar próxima música:",M),v.error("Erro ao iniciar próxima música")}finally{m(null)}},[a,x,w,je,Pe,Ae]),_=d.useCallback(async j=>{if(!(!a||!l))try{await je(ge(`/playback/${a}/volume`),{method:"POST",body:JSON.stringify({volume:j})});const O={...l,volume:j};h(O),ht.setCache(`playback_${a}`,O),me("volume_change",{previousVolume:l.volume,newVolume:j})}catch{v.error("Erro ao ajustar volume")}},[a,l,je]),Q=d.useCallback(()=>{if(!l)return;const j=l.volume===0?50:0;_(j),me("volume_mute_toggle",{action:j===0?"mute":"unmute"})},[l,_]),G=d.useCallback(async j=>{if(a){m(`remove-${j}`);try{await je(ge(`/playback-queue/${a}/remove`),{method:"POST",body:JSON.stringify({trackId:j})}),v.success("Música removida da fila"),me("track_remove",{trackId:j}),await Ae()}catch{v.error("Erro ao remover música da fila")}finally{m(null)}}},[a,je,Ae]),ue=d.useCallback(async j=>{if(a){m(`promote-${j}`);try{await je(ge(`/playback-queue/${a}/promote`),{method:"POST",body:JSON.stringify({trackId:j})}),v.success("Música promovida para fila prioritária"),me("track_promote",{trackId:j}),await Ae()}catch{v.error("Erro ao promover música")}finally{m(null)}}},[a,je,Ae]),me=d.useCallback((j,O)=>{try{const M={timestamp:new Date().toISOString(),restaurantId:a,action:j,data:O};let y=JSON.parse(localStorage.getItem("playback_analytics")||"[]");y.push(M),y.length>100&&(y=y.slice(-100)),localStorage.setItem("playback_analytics",JSON.stringify(y))}catch(M){console.error("Erro ao registrar analytics:",M)}},[a]),we=j=>{const O=Math.floor(j/60),M=Math.floor(j%60);return`${O}:${M.toString().padStart(2,"0")}`},V=j=>j<60?`${j}s`:j<3600?`${Math.floor(j/60)}min`:`${Math.floor(j/3600)}h`,ae=j=>j.toLocaleString("pt-BR",{style:"currency",currency:"BRL"}).slice(3),de=()=>{var j;return(j=l==null?void 0:l.currentTrack)!=null&&j.duration?l.currentTime/l.currentTrack.duration*100:0};return u?e.jsx("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm",children:e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(Xe,{className:"w-8 h-8 animate-spin text-blue-600"})})}):e.jsx("div",{className:"min-h-screen bg-white text-gray-900",children:e.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-6 space-y-6",children:[e.jsx(In,{position:"top-left"}),e.jsx(md,{autoReorder:X,setAutoReorder:U,handleReorderByVotes:Me,onRefresh:()=>{Pe(),Ae()},onStartNext:T,onClearQueues:Te,actionLoading:f,localSettings:Fe,toggleLocal:rt}),e.jsx(pd,{countdown:z,totalVotes:(xe==null?void 0:xe.totalVotes)??Y,computedRevenue:(xe==null?void 0:xe.totalRevenue)??Ue,voteAggregates:Ve,formatEstimatedTime:V}),e.jsx(xd,{playbackState:l,actionLoading:f,handlePlayPause:Tt,handleSkip:C,handleVolumeChange:_,toggleMute:Q,getProgressPercentage:de,formatTime:we,localSettings:Fe,toggleLocal:rt,handleClearQueues:Te,handleReorderByVotes:Me,handleStartNext:T,manualVideoInput:ze,setManualVideoInput:$e,handleManualAdd:xt,loadInitialData:()=>{Pe(),Ae()},rankingMap:ie}),e.jsx(fd,{queueStats:k,formatEstimatedTime:V}),e.jsx(gd,{reorderHistory:te}),e.jsx(yd,{priorityQueue:x,normalQueue:w,actionLoading:f,handleMoveInQueue:ft,handleDemoteToNormal:at,handlePromoteTrack:ue,handleRemoveFromQueue:G,formatTime:we,rankingMap:ie,predictedReorder:fe}),e.jsx(bd,{rankingPaid:$,rankingFree:P,voteNormalFromController:st,superVoteFromController:vt,formatBRL:ae}),e.jsx(vd,{autoPreview:I,formatBRL:ae}),e.jsx(wd,{lastUpdateTime:J})]})})},md=ce.memo(({autoReorder:r,setAutoReorder:t,handleReorderByVotes:s,onRefresh:a,onStartNext:n,onClearQueues:i,actionLoading:c,localSettings:o,toggleLocal:l})=>e.jsx("header",{className:"bg-white/70 backdrop-blur-md border border-gray-200 p-4 sm:p-6 rounded-xl",children:e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3",children:[e.jsxs("div",{className:"text-gray-900",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Controle da Playlist (Colaborativa)"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Gerencie a reprodução e acompanhe votos, supervotos e reordenações automáticas."})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm text-gray-800 bg-white/70 border border-gray-300 px-3 py-1.5 rounded-lg",children:[e.jsx("input",{type:"checkbox",checked:r,onChange:h=>t(h.target.checked)}),"Auto-reordenar"]}),e.jsx("button",{onClick:s,className:"px-3 py-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg hover:from-indigo-700 hover:to-blue-700 text-sm",children:"Reordenar agora"}),e.jsxs("button",{onClick:a,disabled:c==="refresh",className:"flex items-center gap-2 px-3 py-2 bg-white/70 border border-gray-300 rounded-lg hover:bg-white text-sm disabled:opacity-50","aria-label":"Atualizar dados de reprodução",children:[c==="refresh"?e.jsx(Qt,{className:"w-4 h-4 animate-spin"}):e.jsx(Xe,{className:"w-4 h-4"}),"Atualizar"]})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsxs("button",{onClick:n,className:"px-3 py-1.5 bg-white border border-gray-300 text-gray-800 rounded hover:bg-gray-50 text-sm flex items-center gap-2",children:[e.jsx(dn,{className:"w-4 h-4"})," Tocar próxima da fila"]}),e.jsxs("button",{onClick:i,className:"px-3 py-1.5 bg-white border border-red-300 text-red-700 rounded hover:bg-red-50 text-sm flex items-center gap-2",children:[e.jsx(rn,{className:"w-4 h-4"})," Limpar Filas"]}),e.jsx("div",{className:"h-5 w-px bg-gray-300"}),e.jsxs("button",{onClick:()=>l("shuffle"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${o.shuffle?"bg-indigo-50 border-indigo-200 text-indigo-700":"bg-white border-gray-300 text-gray-700"}`,children:[e.jsx(ji,{className:"w-4 h-4"})," Shuffle"]}),e.jsxs("button",{onClick:()=>l("repeat"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${o.repeat?"bg-indigo-50 border-indigo-200 text-indigo-700":"bg-white border-gray-300 text-gray-700"}`,children:[e.jsx(Ni,{className:"w-4 h-4"})," Repeat"]}),e.jsxs("button",{onClick:()=>l("lockVoting"),className:`px-3 py-1.5 rounded text-sm flex items-center gap-1 border ${o.lockVoting?"bg-yellow-50 border-yellow-300 text-yellow-700":"bg-white border-gray-300 text-gray-700"}`,children:[o.lockVoting?e.jsx(Xs,{className:"w-4 h-4"}):e.jsx(ki,{className:"w-4 h-4"})," ",o.lockVoting?"Votação Travada":"Votação Liberada"]})]})]})})),pd=ce.memo(({countdown:r,totalVotes:t,computedRevenue:s,voteAggregates:a,formatEstimatedTime:n})=>e.jsx("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-4 border border-gray-200",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Próxima reordenação"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:n(r)})]}),e.jsx(Pt,{className:"w-7 h-7 text-indigo-500"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total de votos"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t})]}),e.jsx(Je,{className:"w-7 h-7 text-green-500"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-yellow-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-yellow-600",children:"Fila Prioritária"}),e.jsx("p",{className:"text-2xl font-bold text-yellow-700",children:a.paidItems})]}),e.jsx(yt,{className:"w-7 h-7 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-green-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-green-600",children:"Fila Normal"}),e.jsx("p",{className:"text-2xl font-bold text-green-700",children:a.freeItems})]}),e.jsx(yt,{className:"w-7 h-7 text-green-500"})]})})]})})),xd=ce.memo(({playbackState:r,actionLoading:t,handlePlayPause:s,handleSkip:a,handleVolumeChange:n,toggleMute:i,getProgressPercentage:c,formatTime:o,localSettings:l,toggleLocal:h,handleClearQueues:u,handleReorderByVotes:p,handleStartNext:f,manualVideoInput:m,setManualVideoInput:x,handleManualAdd:g,loadInitialData:w,rankingMap:b})=>e.jsx("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:r!=null&&r.currentTrack?e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("img",{src:r.currentTrack.thumbnailUrl,alt:r.currentTrack.title,className:"w-16 h-12 object-cover rounded-lg shadow-sm"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:r.currentTrack.title}),e.jsx("p",{className:"text-gray-600",children:r.currentTrack.artist}),e.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-green-600",children:[e.jsx(Je,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"text-sm",children:r.currentTrack.upvotes})]}),e.jsxs("div",{className:"flex items-center space-x-1 text-red-600",children:[e.jsx(ta,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"text-sm",children:r.currentTrack.downvotes})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Score: ",r.currentTrack.score]})]}),e.jsx("div",{className:"flex items-center space-x-3 mt-1 text-xs text-gray-600",children:(()=>{const k=r.currentTrack.youtubeVideoId,S=k?b==null?void 0:b[k]:void 0;return S?e.jsxs(e.Fragment,{children:[e.jsxs("span",{className:"px-1.5 py-0.5 rounded bg-white/70 border border-gray-300 text-gray-800",children:["Votos: ",S.voteCount]}),S.isPaid&&e.jsxs("span",{className:"px-1.5 py-0.5 rounded bg-yellow-100 text-yellow-800 border border-yellow-300",children:["R$ ",Number(S.paymentAmount??0).toFixed(2)]})]}):null})()})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm text-gray-500",children:[e.jsx("span",{children:o(r.currentTime)}),e.jsx("span",{children:o(r.currentTrack.duration)})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:e.jsx(B.div,{className:"bg-blue-600 h-2 rounded-full",initial:{width:0},animate:{width:`${c()}%`},transition:{duration:1},role:"progressbar","aria-valuenow":c(),"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Progresso da música"})})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-4",children:[e.jsx("button",{onClick:s,disabled:t==="playpause",className:"flex items-center justify-center w-12 h-12 bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":r.isPlaying?"Pausar":"Reproduzir",children:t==="playpause"?e.jsx(Qt,{className:"w-6 h-6 animate-spin"}):r.isPlaying?e.jsx(Zr,{className:"w-6 h-6"}):e.jsx(ot,{className:"w-6 h-6 ml-1"})}),e.jsx("button",{onClick:a,disabled:t==="skip",className:"flex items-center justify-center w-10 h-10 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Pular música",children:t==="skip"?e.jsx(Qt,{className:"w-5 h-5 animate-spin"}):e.jsx(dn,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:i,"aria-label":r.volume===0?"Ativar som":"Silenciar",children:r.volume===0?e.jsx(un,{className:"w-5 h-5 text-gray-600"}):e.jsx(er,{className:"w-5 h-5 text-gray-600"})}),e.jsx("input",{type:"range",min:"0",max:"100",value:r.volume,onChange:k=>n(parseInt(k.target.value)),className:"w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer","aria-label":"Controle de volume"}),e.jsx("span",{className:"text-sm text-gray-500 w-8",children:r.volume})]})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-16 h-12 bg-white/10 rounded-lg"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Sem reprodução ativa no momento"}),e.jsx("p",{className:"text-gray-600 text-sm",children:"A playlist do restaurante toca normalmente até que a votação reordene a fila."})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900/40 rounded-lg p-4 border border-gray-200 dark:border-gray-700",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-800 dark:text-gray-200 mb-3",children:[e.jsx(Xr,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium text-sm",children:"Adicionar música manualmente"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2",children:[e.jsx("input",{value:m,onChange:k=>x(k.target.value),placeholder:"Cole o link ou ID do YouTube",className:"flex-1 px-3 py-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>g(!1),className:"px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 text-sm",children:"Adicionar na Fila Normal"}),e.jsx("button",{onClick:()=>g(!0),className:"px-3 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm",children:"Adicionar na Fila Prioritária"})]})]})]})]})})),fd=ce.memo(({queueStats:r,formatEstimatedTime:t})=>e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsx("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Total na Fila"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.totalItems})]}),e.jsx(Ce,{className:"w-8 h-8 text-blue-500"})]})}),e.jsx("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Fila Prioritária"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.paidItems})]}),e.jsx(Je,{className:"w-8 h-8 text-yellow-500"})]})}),e.jsx("div",{className:"bg-white rounded-xl p-4 border border-gray-200 shadow-sm",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-600",children:"Fila Normal"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:r.freeItems})]}),e.jsx(yt,{className:"w-8 h-8 text-green-500"})]})})]})),gd=ce.memo(({reorderHistory:r})=>e.jsxs("section",{className:"bg-white rounded-xl p-6 border border-gray-200 shadow-sm mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Histórico de Reordenação"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-gray-100 text-gray-700 border border-gray-200",children:[r.length," eventos"]})]}),r.length===0?e.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Nenhum evento de reordenação ainda"}):e.jsx("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:r.map((t,s)=>e.jsxs("div",{className:"p-3 rounded border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"font-medium text-gray-800 dark:text-gray-200",children:[new Date(t.time).toLocaleString("pt-BR")," • ",t.playlistName||"Playlist"]}),e.jsxs("div",{className:"text-xs text-indigo-700 dark:text-indigo-300",children:[t.count," músicas impactadas"]})]}),e.jsxs("div",{className:"mt-2 text-xs text-gray-700 dark:text-gray-300",children:["Top 5:",e.jsx("ul",{className:"list-disc list-inside",children:(t.details||[]).map((a,n)=>e.jsxs("li",{className:"truncate",children:[a.title||a.videoId," ",a.isPaid?"(Paga)":""," — votos: ",a.voteCount??"?"]},n))})]})]},s))})]})),yd=ce.memo(({priorityQueue:r,normalQueue:t,actionLoading:s,handleMoveInQueue:a,handleDemoteToNormal:n,handlePromoteTrack:i,handleRemoveFromQueue:c,formatTime:o,rankingMap:l,predictedReorder:h})=>e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center text-gray-900",children:["🔥 Fila Prioritária (",r.length,")"]}),e.jsx("div",{className:"bg-yellow-100 text-yellow-800 border border-yellow-300 px-2 py-1 rounded text-xs font-medium",children:"SuperVoto (R$ 5, 20, 50)"})]}),e.jsx("div",{className:"text-xs text-gray-600 mb-2",children:(()=>{const u=r.reduce((f,m)=>{var x;return f+(((x=l==null?void 0:l[m.youtubeVideoId])==null?void 0:x.voteCount)??0)},0),p=r.length;return e.jsxs("span",{children:["Itens pagos: ",p," • Votos somados: ",u]})})()}),e.jsxs("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:[r.length>0?e.jsx(et,{children:r.map((u,p)=>{var f,m,x;return e.jsxs(B.div,{layout:!0,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 text-white rounded-full text-xs flex items-center justify-center font-bold",children:p+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 truncate",children:u.title}),e.jsx("p",{className:"text-xs text-gray-600 truncate",children:u.artist})]}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Je,{className:"w-3 h-3 text-yellow-600","aria-hidden":"true"}),e.jsxs("span",{className:"text-xs text-yellow-700",children:[((f=l==null?void 0:l[u.youtubeVideoId])==null?void 0:f.voteCount)??0," votos"]})]}),((m=l==null?void 0:l[u.youtubeVideoId])==null?void 0:m.isPaid)&&e.jsxs("div",{className:"text-xs text-green-700",children:["R$ ",Number(((x=l==null?void 0:l[u.youtubeVideoId])==null?void 0:x.paymentAmount)??0).toFixed(2)]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:o(u.duration)}),e.jsx("button",{onClick:()=>a("priority",u.id,"up"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para cima","aria-label":"Mover para cima",children:e.jsx(Na,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>a("priority",u.id,"down"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para baixo","aria-label":"Mover para baixo",children:e.jsx(ka,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>n(u.id),className:"p-1 text-blue-600 hover:text-blue-800",title:"Mover para fila normal","aria-label":"Mover para fila normal",children:e.jsx(as,{className:"w-3 h-3 rotate-180"})}),e.jsx("button",{onClick:()=>c(u.id),disabled:s===`remove-${u.id}`,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Remover da fila prioritária",title:"Remover",children:s===`remove-${u.id}`?e.jsx(Qt,{className:"w-3 h-3 animate-spin"}):e.jsx(jt,{className:"w-3 h-3"})})]})]},u.id)})}):e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(Je,{className:"w-6 h-6 text-yellow-600"})}),e.jsx("p",{className:"text-gray-600 text-sm",children:"Nenhuma música na fila prioritária"})]}),h&&h.length>0&&e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-indigo-200 dark:border-indigo-700 lg:col-span-2",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"text-md font-semibold text-indigo-700 dark:text-indigo-300",children:"Prévia de Reordenação (por votos)"}),e.jsx("span",{className:"text-xs text-gray-500",children:"Pagas primeiro, depois por valor e votos"})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:h.map((u,p)=>{var f,m,x;return e.jsxs("div",{className:"px-2 py-1 rounded text-xs border border-indigo-200 dark:border-indigo-700 bg-indigo-50/60 dark:bg-indigo-900/20",children:[e.jsxs("span",{className:"font-medium mr-1",children:[p+1,"."]}),e.jsx("span",{className:"mr-1",children:u.title}),e.jsxs("span",{className:"text-gray-500",children:["(",((f=l==null?void 0:l[u.youtubeVideoId])==null?void 0:f.voteCount)??0," votos",(m=l==null?void 0:l[u.youtubeVideoId])!=null&&m.isPaid?` · R$ ${Number(((x=l==null?void 0:l[u.youtubeVideoId])==null?void 0:x.paymentAmount)??0).toFixed(2)}`:"",")"]})]},`${u.id}_${p}`)})})]})]})]}),e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-semibold flex items-center text-gray-900",children:["📋 Fila Normal (",t.length,")"]}),e.jsx("div",{className:"bg-green-100 text-green-800 border border-green-300 px-2 py-1 rounded text-xs font-medium",children:"Gratuito"})]}),e.jsx("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:t.length>0?e.jsx(et,{children:t.slice().sort((u,p)=>p.upvotes-p.downvotes-(u.upvotes-u.downvotes)).map((u,p)=>{var f;return e.jsxs(B.div,{layout:!0,initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[e.jsx("div",{className:"w-6 h-6 bg-green-600 text-white rounded text-xs flex items-center justify-center font-medium",children:p+1}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900 truncate",children:u.title}),e.jsx("p",{className:"text-xs text-gray-600 truncate",children:u.artist}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Je,{className:"w-3 h-3 text-green-600","aria-hidden":"true"}),e.jsx("span",{className:"text-xs text-green-600",children:u.upvotes})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(ta,{className:"w-3 h-3 text-red-600","aria-hidden":"true"}),e.jsx("span",{className:"text-xs text-red-600",children:u.downvotes})]}),e.jsx("div",{className:"flex items-center space-x-3 mt-1",children:e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx(Je,{className:"w-3 h-3 text-green-600","aria-hidden":"true"}),e.jsxs("span",{className:"text-xs text-green-700",children:[((f=l==null?void 0:l[u.youtubeVideoId])==null?void 0:f.voteCount)??0," votos"]})]})}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Score: ",u.score||u.upvotes-u.downvotes]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:o(u.duration)}),e.jsx("button",{onClick:()=>a("normal",u.id,"up"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para cima","aria-label":"Mover para cima",children:e.jsx(Na,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>a("normal",u.id,"down"),className:"p-1 text-gray-600 hover:text-gray-800",title:"Mover para baixo","aria-label":"Mover para baixo",children:e.jsx(ka,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>i(u.id),disabled:s===`promote-${u.id}`,className:"p-1 text-blue-600 hover:text-blue-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Promover para fila prioritária",title:"Promover",children:s===`promote-${u.id}`?e.jsx(Qt,{className:"w-3 h-3 animate-spin"}):e.jsx(as,{className:"w-3 h-3"})}),e.jsx("button",{onClick:()=>c(u.id),disabled:s===`remove-${u.id}`,className:"p-1 text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors","aria-label":"Remover da fila normal",title:"Remover",children:s===`remove-${u.id}`?e.jsx(Qt,{className:"w-3 h-3 animate-spin"}):e.jsx(jt,{className:"w-3 h-3"})})]})]},u.id)})}):e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(yt,{className:"w-6 h-6 text-green-600"})}),e.jsx("p",{className:"text-gray-600 text-sm",children:"Nenhuma música na fila normal"})]})})]})]})),bd=ce.memo(({rankingPaid:r,rankingFree:t,voteNormalFromController:s,superVoteFromController:a,formatBRL:n})=>e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"SuperVotos (Pagas)"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800 border border-yellow-300",children:[r.length," itens"]})]}),e.jsx("div",{className:"space-y-2 max-h-72 overflow-y-auto",children:r.length===0?e.jsx("div",{className:"text-sm text-gray-600",children:"Nenhum supervoto no momento"}):r.map((i,c)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-gray-200 bg-white/50",children:[e.jsx("div",{className:"w-7 h-7 bg-yellow-500 text-white rounded flex items-center justify-center text-xs font-bold",children:c+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${i.youtubeVideoId}/mqdefault.jpg`,alt:i.title||i.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium truncate text-gray-900",children:i.title||i.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 truncate",children:i.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-purple-200 mr-1",children:["Votos: ",i.voteCount]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>s(i.youtubeVideoId),className:"px-2 py-0.5 border border-white/20 rounded text-xs hover:bg-white/10",title:"Voto normal",children:"+1"}),e.jsx("button",{onClick:()=>{const o=window.prompt("Nome do cliente (opcional)","Admin")||void 0,l=window.prompt("Mensagem/recado (opcional)","Recado de teste pelo painel")||void 0;a(i.youtubeVideoId,5,l,o)},className:"px-2 py-0.5 border border-yellow-500/40 rounded text-xs hover:bg-yellow-500/10",title:"Supervoto R$5",children:"5"}),e.jsx("button",{onClick:()=>{const o=window.prompt("Nome do cliente (opcional)","Admin")||void 0,l=window.prompt("Mensagem/recado (opcional)","Recado de teste pelo painel")||void 0;a(i.youtubeVideoId,20,l,o)},className:"px-2 py-0.5 border border-yellow-500/50 rounded text-xs hover:bg-yellow-500/10",title:"Supervoto R$20",children:"20"}),e.jsx("button",{onClick:()=>{const o=window.prompt("Nome do cliente (opcional)","Admin")||void 0,l=window.prompt("Mensagem/recado (opcional)","Recado de teste pelo painel")||void 0;a(i.youtubeVideoId,50,l,o)},className:"px-2 py-0.5 border border-yellow-500/60 rounded text-xs hover:bg-yellow-500/10",title:"Supervoto R$50",children:"50"})]}),e.jsxs("div",{className:"text-xs text-green-200 ml-1",children:["R$ ",n(i.paymentAmount)]})]})]},i.youtubeVideoId))})]}),e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Votos (Grátis)"}),e.jsxs("span",{className:"text-xs px-2 py-1 rounded bg-green-100 text-green-800 border border-green-300",children:[t.length," itens"]})]}),e.jsx("div",{className:"space-y-2 max-h-72 overflow-y-auto",children:t.length===0?e.jsx("div",{className:"text-sm text-gray-600",children:"Nenhum voto gratuito no momento"}):t.map((i,c)=>e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded border border-gray-200 bg-white/50",children:[e.jsx("div",{className:"w-7 h-7 bg-green-600 text-white rounded flex items-center justify-center text-xs font-bold",children:c+1}),e.jsx("img",{src:`https://img.youtube.com/vi/${i.youtubeVideoId}/mqdefault.jpg`,alt:i.title||i.youtubeVideoId,className:"w-10 h-8 object-cover rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium truncate text-gray-900",children:i.title||i.youtubeVideoId}),e.jsx("div",{className:"text-xs text-gray-600 truncate",children:i.artist||"—"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"text-xs text-gray-600 mr-1",children:["Votos: ",i.voteCount]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>s(i.youtubeVideoId),className:"px-2 py-0.5 border border-gray-300 rounded text-xs hover:bg-gray-100",title:"Voto normal",children:"+1"}),e.jsx("button",{onClick:()=>{const o=window.prompt("Nome do cliente (opcional)","Admin")||void 0,l=window.prompt("Mensagem/recado (opcional)","Recado de teste pelo painel")||void 0;a(i.youtubeVideoId,5,l,o)},className:"px-2 py-0.5 border border-yellow-400 rounded text-xs hover:bg-yellow-100",title:"Supervoto R$5",children:"5"}),e.jsx("button",{onClick:()=>{const o=window.prompt("Nome do cliente (opcional)","Admin")||void 0,l=window.prompt("Mensagem/recado (opcional)","Recado de teste pelo painel")||void 0;a(i.youtubeVideoId,20,l,o)},className:"px-2 py-0.5 border border-yellow-500 rounded text-xs hover:bg-yellow-100",title:"Supervoto R$20",children:"20"}),e.jsx("button",{onClick:()=>{const o=window.prompt("Nome do cliente (opcional)","Admin")||void 0,l=window.prompt("Mensagem/recado (opcional)","Recado de teste pelo painel")||void 0;a(i.youtubeVideoId,50,l,o)},className:"px-2 py-0.5 border border-yellow-600 rounded text-xs hover:bg-yellow-100",title:"Supervoto R$50",children:"50"})]}),e.jsxs("div",{className:"text-xs text-gray-600 ml-1",children:["Mesa: ",i.tableNumber??"—"]})]})]},i.youtubeVideoId))})]})]})),vd=ce.memo(({autoPreview:r,formatBRL:t})=>e.jsxs("section",{className:"bg-white/70 backdrop-blur-md rounded-xl p-6 border border-gray-200",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:"Fila Automática (Prévia)"}),e.jsx("div",{className:"text-xs text-gray-600",children:"Baseada em votos e pagamentos"})]}),r.length>0?e.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:r.map((s,a)=>e.jsxs("div",{className:"flex items-center justify-between p-2 rounded bg-white/50 border border-gray-200",children:[e.jsxs("div",{className:"truncate mr-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:s.title}),e.jsxs("span",{className:"text-xs text-gray-600 ml-2",children:[s.voteCount," votos"]})]}),e.jsx("div",{className:"text-xs",children:s.isPaid?e.jsxs("span",{className:"text-green-600",children:["R$ ",t(s.paymentAmount??0)]}):e.jsx("span",{className:"text-gray-600",children:"Grátis"})})]},a))}):e.jsx("div",{className:"text-sm text-gray-600",children:"Nenhuma prévia disponível."})]})),wd=ce.memo(({lastUpdateTime:r})=>e.jsx(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},className:"mt-6",children:e.jsx("div",{className:"bg-white rounded-lg p-4 shadow-sm border border-gray-200",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(nn,{className:"w-4 h-4 text-blue-500"}),e.jsxs("span",{className:"text-sm text-gray-600",children:["Sistema ativo - Última atualização: ",r.toLocaleTimeString("pt-BR")]})]}),e.jsxs("div",{className:"flex items-center space-x-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-green-600",children:"Online"})]})]})})})),jd=Object.freeze(Object.defineProperty({__proto__:null,default:hd},Symbol.toStringTag,{value:"Module"})),Nd=()=>{const{restaurantId:r}=At(),[t,s]=d.useState(null),[a,n]=d.useState([]),[i,c]=d.useState(null),[o,l]=d.useState("7d"),[h,u]=d.useState(!0),[p,f]=d.useState(null),m=r||"demo-restaurant";d.useEffect(()=>{x()},[o,m]);const x=async()=>{u(!0),f(null);try{const b=await(await fetch(`http://localhost:8001/api/v1/analytics/dashboard/${m}?period=${o}`)).json();s(b);const S=await(await fetch(`http://localhost:8001/api/v1/analytics/popular-songs/${m}?limit=5`)).json();n(S.songs);const Z=await(await fetch(`http://localhost:8001/api/v1/analytics/engagement/${m}?period=${o}`)).json();c(Z)}catch(w){f("Erro ao carregar dados de analytics"),console.error("Analytics error:",w)}finally{u(!1)}},g=async()=>{try{await fetch(`http://localhost:8001/api/v1/analytics/generate-test-data/${m}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({days:7})}),x()}catch(w){console.error("Error generating test data:",w)}};return h?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-600",children:"Carregando analytics..."})]})}):p?e.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-red-600 mb-4",children:p}),e.jsx("button",{onClick:x,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",children:"Tentar Novamente"})]})}):e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Analytics Dashboard"}),e.jsx("p",{className:"text-gray-600",children:"Restaurante Demo - Métricas e Relatórios"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("select",{value:o,onChange:w=>l(w.target.value),className:"border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[e.jsx("option",{value:"24h",children:"Últimas 24h"}),e.jsx("option",{value:"7d",children:"Últimos 7 dias"}),e.jsx("option",{value:"30d",children:"Últimos 30 dias"}),e.jsx("option",{value:"90d",children:"Últimos 90 dias"})]}),e.jsxs("button",{onClick:g,className:"bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2",children:[e.jsx(Nt,{className:"w-4 h-4"}),e.jsx("span",{children:"Gerar Dados Teste"})]})]})]})})}),e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Ce,{className:"w-8 h-8 text-blue-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Plays"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalPlays})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Je,{className:"w-8 h-8 text-green-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Total de Votos"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalVotes})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(yt,{className:"w-8 h-8 text-purple-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sugestões"}),e.jsx("p",{className:"text-2xl font-bold text-gray-900",children:t.summary.totalSuggestions})]})]})}),e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(Nt,{className:"w-8 h-8 text-orange-600"}),e.jsxs("div",{className:"ml-4",children:[e.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Engajamento"}),e.jsxs("p",{className:"text-2xl font-bold text-gray-900",children:[t.summary.engagementRate,"%"]})]})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Músicas Mais Populares"})}),e.jsx("div",{className:"p-6",children:a.length>0?e.jsx("div",{className:"space-y-4",children:a.map((w,b)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:b+1}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:w.title}),e.jsx("p",{className:"text-sm text-gray-600",children:w.artist})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:["Score: ",w.stats.popularityScore]}),e.jsxs("p",{className:"text-xs text-gray-600",children:[w.stats.plays," plays • ",w.stats.upvotes," ","upvotes"]})]})]},w.id))}):e.jsx("p",{className:"text-gray-500 text-center py-8",children:"Nenhum dado disponível"})})]}),i&&e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsx("div",{className:"px-6 py-4 border-b border-gray-200",children:e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Métricas de Engajamento"})}),e.jsx("div",{className:"p-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Taxa de Engajamento"}),e.jsxs("span",{className:"font-semibold text-green-600",children:[i.metrics.engagementRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Taxa de Positividade"}),e.jsxs("span",{className:"font-semibold text-blue-600",children:[i.metrics.positivityRate,"%"]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Votos por Sugestão"}),e.jsx("span",{className:"font-semibold text-purple-600",children:i.metrics.averageVotesPerSuggestion})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Upvotes"}),e.jsx("span",{className:"font-semibold text-green-600",children:i.metrics.upvotes})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Downvotes"}),e.jsx("span",{className:"font-semibold text-red-600",children:i.metrics.downvotes})]})]})})]})]}),e.jsx("div",{className:"mt-8 bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Exportar Dados"}),e.jsx("p",{className:"text-gray-600",children:"Baixe relatórios detalhados em formato JSON"})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsxs("a",{href:`http://localhost:8001/api/v1/analytics/export/${m}?type=all&format=json`,className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2",target:"_blank",rel:"noopener noreferrer",children:[e.jsx(ea,{className:"w-4 h-4"}),e.jsx("span",{children:"Exportar Tudo"})]})})]})})]})]})},kd=()=>{const r=ps();return e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center px-4",children:e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center max-w-md mx-auto",children:[e.jsx(B.div,{initial:{scale:.8},animate:{scale:1},transition:{delay:.2},className:"mb-8",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"text-8xl md:text-9xl font-bold text-gray-200 dark:text-gray-700 select-none",children:"404"}),e.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:e.jsx(B.div,{animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center shadow-lg",children:e.jsx(Ce,{className:"w-8 h-8 text-white"})})})]})}),e.jsxs(B.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"mb-8",children:[e.jsx("h1",{className:"text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Página Não Encontrada"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-2",children:"Ops! A página que você está procurando não existe ou foi movida."}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Que tal voltar e descobrir uma nova música?"})]}),e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[e.jsx(nt,{onClick:()=>r("/"),icon:e.jsx(cn,{className:"w-4 h-4"}),size:"lg",children:"Página Inicial"}),e.jsx(nt,{onClick:()=>r(-1),variant:"outline",icon:e.jsx(Si,{className:"w-4 h-4"}),size:"lg",children:"Voltar"})]}),e.jsx("div",{className:"pt-4",children:e.jsxs("button",{onClick:()=>r("/"),className:"inline-flex items-center space-x-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors",children:[e.jsx(qs,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Procurar restaurantes"})]})})]}),e.jsxs(B.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},className:"mt-12 p-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-200 dark:border-gray-700",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"Você pode estar procurando por:"}),e.jsxs("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Página inicial do sistema"}),e.jsx("li",{children:"• Painel administrativo (/admin)"}),e.jsx("li",{children:"• Página de um restaurante específico"})]})]}),e.jsxs("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[e.jsx(B.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-32 h-32 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"}),e.jsx(B.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-32 h-32 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-10"})]})]})})};class Sd{constructor(){_e(this,"sessionToken",null);_e(this,"session",null);_e(this,"apiUrl",$n.BASE_URL+"/api/v1");this.restoreSession()}async createSession(t,s,a){try{const n=dr(),i=this.getDeviceInfo(),c=await fetch(`${this.apiUrl}/client/session`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionToken:n,restaurantId:t,tableNumber:s,clientName:a,deviceInfo:i})});if(c.ok){const o=await c.json();return this.session=o,this.sessionToken=o.sessionToken,this.saveSession(),o}else return this.createLocalSession(t,s,a)}catch(n){return console.error("Error creating session:",n),this.createLocalSession(t,s,a)}}createLocalSession(t,s,a){const n=dr(),i=new Date().toISOString(),c={id:dr(),sessionToken:n,restaurantId:t,tableNumber:s,clientName:a,deviceInfo:this.getDeviceInfo(),lastActivity:i,suggestionsCount:0,votesCount:0,pageViews:1,sessionDuration:0,formattedDuration:"0m",isActive:!0,isSessionActive:!0,isNewSession:!0,engagementLevel:"low",createdAt:i,updatedAt:i,points:0,level:1,badges:[],streak:0};return this.session=c,this.sessionToken=n,this.saveSession(),c}getSession(){return this.session}getSessionToken(){return this.sessionToken}updateStats(t){this.session&&(this.session={...this.session,...t},this.saveSession())}incrementSuggestions(){this.session&&(this.session.suggestionsCount++,this.session.points+=20,this.updateLevel(),this.saveSession())}incrementVotes(){this.session&&(this.session.votesCount++,this.session.points+=10,this.updateLevel(),this.saveSession())}updateLevel(){if(this.session){const t=Math.floor(this.session.points/100)+1;t>this.session.level&&(this.session.level=t)}}awardBadge(t){return this.session&&!this.session.badges.includes(t)?(this.session.badges.push(t),this.saveSession(),!0):!1}getDeviceInfo(){const t=navigator.userAgent,s=`${screen.width}x${screen.height}`,a=navigator.language,n=Intl.DateTimeFormat().resolvedOptions().timeZone;let i="desktop";return/Mobile|Android|iPhone|iPad/.test(t)&&(i=/iPad/.test(t)?"tablet":"mobile"),{type:i,screenResolution:s,language:a,timezone:n}}saveSession(){this.session&&this.sessionToken&&(localStorage.setItem("clientSession",JSON.stringify(this.session)),localStorage.setItem("sessionToken",this.sessionToken))}restoreSession(){try{const t=localStorage.getItem("clientSession"),s=localStorage.getItem("sessionToken");t&&s&&(this.session=JSON.parse(t),this.sessionToken=s)}catch(t){console.error("Error restoring session:",t),this.clearSession()}}clearSession(){this.session=null,this.sessionToken=null,localStorage.removeItem("clientSession"),localStorage.removeItem("sessionToken")}isSessionValid(){if(!this.session||!this.sessionToken)return!1;const t=Date.now()-new Date(this.session.createdAt).getTime(),s=24*60*60*1e3;return t<s}async forceNewSession(t,s,a){return this.clearSession(),this.createSession(t,s,a)}}const gr=new Sd;class Cd{constructor(){_e(this,"cooldownCache",new Map);_e(this,"cacheExpiry",3e4);_e(this,"intervalId",null)}async checkSongCooldown(t,s){const a=`${t}:${s}`,n=this.cooldownCache.get(a);if(n&&Date.now()<n.expiresAt){if(!n.isInCooldown)return{isInCooldown:!1};const i=Math.floor((Date.now()-n.cachedAt)/1e3),c=Math.max(0,n.ttlAtCache-i);return n.lastTimeLeft=c,this.cooldownCache.set(a,n),{isInCooldown:c>0,cooldownTimeLeft:c>0?c:void 0}}try{const i=await fetch(ge(`/collaborative-playlist/${t}/cooldown/${s}`),{method:"GET",headers:{"Content-Type":"application/json"}});if(i.ok){const c=await i.json(),o=!!c.isInCooldown,l=Math.max(0,Number(c.cooldownTimeLeft||0));return this.cooldownCache.set(a,{isInCooldown:o,cachedAt:Date.now(),ttlAtCache:l,expiresAt:Date.now()+this.cacheExpiry,lastTimeLeft:l}),{isInCooldown:o,cooldownTimeLeft:o?l:void 0}}}catch(i){console.warn("Erro ao verificar cooldown:",i)}return{isInCooldown:!1}}async checkMultipleSongsCooldown(t,s){const a=s.map(async n=>{const i=await this.checkSongCooldown(t,n.youtubeVideoId);return{...n,isInCooldown:i.isInCooldown,cooldownTimeLeft:i.cooldownTimeLeft}});return Promise.all(a)}markSongInCooldown(t,s,a=10){const n=`${t}:${s}`,i=a*60;this.cooldownCache.set(n,{isInCooldown:!0,cachedAt:Date.now(),ttlAtCache:i,expiresAt:Date.now()+this.cacheExpiry,lastTimeLeft:i})}removeSongFromCooldown(t,s){const a=`${t}:${s}`;this.cooldownCache.delete(a)}clearCache(){this.cooldownCache.clear()}updateCooldownTimes(){const t=Date.now();for(const[s,a]of this.cooldownCache.entries()){if(!a.isInCooldown)continue;const n=Math.floor((t-a.cachedAt)/1e3),i=Math.max(0,a.ttlAtCache-n);i<=0?this.cooldownCache.set(s,{...a,isInCooldown:!1,lastTimeLeft:0}):this.cooldownCache.set(s,{...a,lastTimeLeft:i})}}startCooldownTimer(){this.intervalId||(this.intervalId=setInterval(()=>{this.updateCooldownTimes()},1e3))}}const Dt=new Cd,Ed=[{label:"R$ 5,00",amount:500},{label:"R$ 20,00",amount:2e3},{label:"R$ 50,00",amount:5e3}],Rd=({isOpen:r,onClose:t,suggestion:s,sessionId:a,onPaymentSuccess:n,restaurantId:i})=>{const{restaurantId:c}=At(),o=i||c,[l]=Kr(),h=l.get("table"),[u,p]=d.useState("Cliente"),[f,m]=d.useState(""),[x,g]=d.useState("confirm"),[w,b]=d.useState(null),[k,S]=d.useState(!1),[J,Z]=d.useState(1800),[se,N]=d.useState(!1),[$,W]=d.useState(!1),[P,H]=d.useState(null),[re,A]=d.useState(null);if(!o)return console.error("❌ RestaurantId não encontrado na URL"),null;d.useEffect(()=>{if(x==="payment"&&J>0){const Y=setInterval(()=>{Z(ye=>ye<=1?(g("error"),v.error("O tempo para pagamento expirou"),0):ye-1)},1e3);return()=>clearInterval(Y)}},[x,J]),d.useEffect(()=>{if(x==="payment"&&w&&!se){const Y=setInterval(async()=>{await X()},5e3);return()=>clearInterval(Y)}},[x,w,se]);const z=Y=>{const ye=Math.floor(Y/60),te=Y%60;return`${ye.toString().padStart(2,"0")}:${te.toString().padStart(2,"0")}`},le=async Y=>{try{S(!0),A(Y);const ye=await fetch(ge("/payments/pix/suggestion"),{method:"POST",headers:Kt("application/json"),body:JSON.stringify({restaurantId:o,youtubeId:s.youtubeVideoId||s.id,title:s.title,artist:s.artist,clientName:u,clientMessage:f,tableNumber:h?parseInt(h):1,sessionId:a,amount:Y})});if(ye.ok){const te=await ye.json();console.log("💳 DEBUG: Dados do pagamento recebidos:",te);const Ee=te.payment.qrCodeData==="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";if(!te.payment.qrCodeData.startsWith("data:image/")||Ee){console.warn("⚠️ QR Code placeholder ou inválido, gerando fallback..."),W(!0);try{const xe=`https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(te.payment.pixCode)}&size=300x300&margin=10`;H(xe)}catch(xe){console.warn("Não foi possível gerar URL de fallback do QR Code",xe)}}b({paymentId:te.payment.id,qrCode:te.payment.pixCode,qrCodeBase64:te.payment.qrCodeData,ticketUrl:te.payment.ticketUrl||"",amount:te.payment.amount}),g("payment"),v.success("Pagamento Pix gerado com sucesso!")}else{const te=await ye.json();console.error("❌ Erro na resposta do servidor:",te),v.error(te.error||"Erro ao gerar pagamento"),g("error")}}catch(ye){console.error("Erro ao criar pagamento:",ye),v.error("Erro ao conectar com o servidor de pagamento"),g("error")}finally{S(!1)}},X=async()=>{var Y,ye;if(!(!w||se))try{N(!0);const te=await fetch(ge(`/payments/${w.paymentId}/status`),{headers:Kt()});if(te.ok){const Ee=await te.json(),xe=((Y=Ee.payment)==null?void 0:Y.status)||((ye=Ee.status)==null?void 0:ye.status);if(xe==="approved"||xe==="paid"){g("success"),v.success("Pagamento aprovado! Sua música foi adicionada à fila.");const Ze=re??Math.round((w.amount||0)*100);n==null||n({paymentId:w.paymentId,amountCents:Ze,clientMessage:f,clientName:u})}else(xe==="rejected"||xe==="cancelled")&&(g("error"),v.error("Pagamento rejeitado ou cancelado"))}else console.error("❌ Erro ao verificar status do pagamento:",te.status)}catch(te){console.error("Erro ao verificar pagamento:",te)}finally{N(!1)}},U=()=>{w!=null&&w.qrCode&&(navigator.clipboard.writeText(w.qrCode),v.success("Código Pix copiado!"))},I=()=>{w!=null&&w.ticketUrl&&window.open(w.ticketUrl,"_blank")},ee=()=>{g("confirm"),b(null),Z(1800),W(!1),t()};return r?e.jsx(et,{children:e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4 overflow-y-auto",children:e.jsxs(B.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"bg-white dark:bg-gray-800 rounded-lg max-w-md w-full my-4",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[e.jsxs("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[x==="confirm"&&"Escolha seu SuperVoto",x==="payment"&&"Pagamento Pix",x==="success"&&"Pagamento Aprovado!",x==="error"&&"Erro no Pagamento"]}),e.jsx("button",{onClick:ee,className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors","aria-label":"Fechar modal",children:e.jsx(jt,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center space-x-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[s.thumbnailUrl&&e.jsx("img",{src:s.thumbnailUrl,alt:`Capa de ${s.title} por ${s.artist}`,className:"w-16 h-16 rounded-lg object-cover"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"font-medium text-gray-900 dark:text-white truncate",children:s.title}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 truncate",children:s.artist}),s.duration&&e.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:[Math.floor(s.duration/60),":",(s.duration%60).toString().padStart(2,"0")]})]})]}),x==="confirm"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(zt,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Escolha seu SuperVoto"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Seu SuperVoto prioriza a música na fila. Selecione um valor:"})]}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:Ed.map(Y=>e.jsxs("button",{onClick:()=>le(Y.amount),disabled:k,className:"px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs","aria-label":`Gerar Pix de ${Y.label}`,children:[e.jsx(os,{className:"w-4 h-4"}),e.jsx("span",{children:Y.label})]},Y.amount))}),e.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(Ci,{className:"w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:"Como funciona:"}),e.jsxs("ul",{className:"text-blue-800 dark:text-blue-200 space-y-1",children:[e.jsx("li",{children:"• Pague via Pix"}),e.jsx("li",{children:"• Sua música vai para a fila prioritária"}),e.jsx("li",{children:'• Acompanhe as letras no "Cante Comigo"'}),e.jsx("li",{children:"• Outros clientes podem votar na sua performance"})]})]})]})})]}),x==="payment"&&w&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Mensagem para o músico (opcional)"}),e.jsx("textarea",{value:f,onChange:Y=>m(Y.target.value),rows:2,maxLength:200,placeholder:"Escreva uma mensagem para o músico acompanhar seu SuperVoto...",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"}),e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Máx. 200 caracteres"})]}),e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-orange-600 dark:text-orange-400",children:[e.jsx(Pt,{className:"w-5 h-5"}),e.jsx("span",{className:"font-mono text-lg",children:z(J)}),e.jsx("span",{className:"text-sm",children:"para pagar"})]}),e.jsxs("div",{className:"text-center",children:[$?P?e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block mb-2",children:e.jsx("img",{src:P,alt:`QR Code (fallback) para pagamento de ${s.title}`,className:"w-48 h-48 mx-auto",onError:()=>{console.warn("Falha ao carregar QR Code de fallback")}})}):e.jsxs("div",{className:"bg-yellow-100 dark:bg-yellow-900/20 p-4 rounded-lg mb-2",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(os,{className:"w-8 h-8 text-yellow-600 dark:text-yellow-400"})}),e.jsx("p",{className:"text-sm text-yellow-800 dark:text-yellow-200 font-medium mb-1",children:"QR Code temporariamente indisponível"}),e.jsx("p",{className:"text-xs text-yellow-700 dark:text-yellow-300",children:"Use o código PIX abaixo para pagar manualmente"})]}):e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block mb-2",children:e.jsx("img",{src:w.qrCodeBase64,alt:`QR Code para pagamento de ${s.title}`,className:"w-48 h-48 mx-auto",onError:Y=>{console.error("❌ Erro ao carregar QR Code base64"),W(!0);try{const ye=`https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(w.qrCode)}&size=300x300&margin=10`;H(ye)}catch{}Y.currentTarget.style.display="none"},onLoad:()=>{console.log("✅ QR Code carregado com sucesso!")}})}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Escaneie o QR Code com seu app do banco"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Ou copie o código Pix:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"text",value:w.qrCode,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm font-mono text-gray-900 dark:text-gray-100","aria-label":"Código Pix"}),e.jsx("button",{onClick:U,className:"px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Copiar código Pix",children:e.jsx(Ei,{className:"w-4 h-4"})})]})]}),w.ticketUrl&&e.jsxs("button",{onClick:I,className:"w-full flex items-center justify-center space-x-2 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors","aria-label":"Abrir pagamento no Mercado Pago",children:[e.jsx(Xr,{className:"w-4 h-4"}),e.jsx("span",{children:"Abrir no Mercado Pago"})]}),e.jsx("div",{className:"text-center",children:e.jsxs("div",{className:"flex items-center justify-center space-x-2 text-blue-600 dark:text-blue-400",children:[e.jsx("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"text-sm",children:"Aguardando pagamento..."})]})})]}),x==="success"&&e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(hn,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Pagamento Aprovado!"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Sua música foi adicionada à fila prioritária e tocará em breve."})]}),e.jsx("button",{onClick:ee,className:"w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors","aria-label":"Continuar após pagamento aprovado",children:"Continuar"})]}),x==="error"&&e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(ls,{className:"w-8 h-8 text-red-600 dark:text-red-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Erro no Pagamento"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Não foi possível processar o pagamento. Tente novamente."})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:()=>{g("confirm"),b(null),Z(1800),W(!1)},className:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors","aria-label":"Tentar pagamento novamente",children:"Tentar Novamente"}),e.jsx("button",{onClick:ee,className:"w-full py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors","aria-label":"Cancelar pagamento",children:"Cancelar"})]})]})]})]})})}):null};var Zn={exports:{}},Pd="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",Id=Pd,Ad=Id;function ei(){}function ti(){}ti.resetWarningCache=ei;var _d=function(){function r(a,n,i,c,o,l){if(l!==Ad){var h=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw h.name="Invariant Violation",h}}r.isRequired=r;function t(){return r}var s={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:t,element:r,elementType:r,instanceOf:t,node:r,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:ti,resetWarningCache:ei};return s.PropTypes=s,s};Zn.exports=_d();var Td=Zn.exports;const He=Js(Td);var Od=function r(t,s){if(t===s)return!0;if(t&&s&&typeof t=="object"&&typeof s=="object"){if(t.constructor!==s.constructor)return!1;var a,n,i;if(Array.isArray(t)){if(a=t.length,a!=s.length)return!1;for(n=a;n--!==0;)if(!r(t[n],s[n]))return!1;return!0}if(t.constructor===RegExp)return t.source===s.source&&t.flags===s.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===s.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===s.toString();if(i=Object.keys(t),a=i.length,a!==Object.keys(s).length)return!1;for(n=a;n--!==0;)if(!Object.prototype.hasOwnProperty.call(s,i[n]))return!1;for(n=a;n--!==0;){var c=i[n];if(!r(t[c],s[c]))return!1}return!0}return t!==t&&s!==s};const Vd=Js(Od);var Dr={exports:{}},si;/**
* @link https://github.com/gajus/sister for the canonical source repository
* @license https://github.com/gajus/sister/blob/master/LICENSE BSD 3-Clause
*/si=function(){var r={},t={};return r.on=function(s,a){var n={name:s,handler:a};return t[s]=t[s]||[],t[s].unshift(n),n},r.off=function(s){var a=t[s.name].indexOf(s);a!==-1&&t[s.name].splice(a,1)},r.trigger=function(s,a){var n=t[s],i;if(n)for(i=n.length;i--;)n[i].handler(a)},r};var Dd=si,$r={exports:{}},$d=function(t,s,a){var n=document.head||document.getElementsByTagName("head")[0],i=document.createElement("script");typeof s=="function"&&(a=s,s={}),s=s||{},a=a||function(){},i.type=s.type||"text/javascript",i.charset=s.charset||"utf8",i.async="async"in s?!!s.async:!0,i.src=t,s.attrs&&Ld(i,s.attrs),s.text&&(i.text=""+s.text);var c="onload"in i?Ha:Fd;c(i,a),i.onload||Ha(i,a),n.appendChild(i)};function Ld(r,t){for(var s in t)r.setAttribute(s,t[s])}function Ha(r,t){r.onload=function(){this.onerror=this.onload=null,t(null,r)},r.onerror=function(){this.onerror=this.onload=null,t(new Error("Failed to load "+this.src),r)}}function Fd(r,t){r.onreadystatechange=function(){this.readyState!="complete"&&this.readyState!="loaded"||(this.onreadystatechange=null,t(null,r))}}(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=$d,a=n(s);function n(i){return i&&i.__esModule?i:{default:i}}t.default=function(i){var c=new Promise(function(o){if(window.YT&&window.YT.Player&&window.YT.Player instanceof Function){o(window.YT);return}else{var l=window.location.protocol==="http:"?"http:":"https:";(0,a.default)(l+"//www.youtube.com/iframe_api",function(u){u&&i.trigger("error",u)})}var h=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=function(){h&&h(),o(window.YT)}});return c},r.exports=t.default})($r,$r.exports);var Md=$r.exports,Lr={exports:{}},Fr={exports:{}},Mr={exports:{}},ds=1e3,us=ds*60,hs=us*60,ms=hs*24,Ud=ms*365.25,qd=function(r,t){t=t||{};var s=typeof r;if(s==="string"&&r.length>0)return Bd(r);if(s==="number"&&isNaN(r)===!1)return t.long?zd(r):Qd(r);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(r))};function Bd(r){if(r=String(r),!(r.length>100)){var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(r);if(t){var s=parseFloat(t[1]),a=(t[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return s*Ud;case"days":case"day":case"d":return s*ms;case"hours":case"hour":case"hrs":case"hr":case"h":return s*hs;case"minutes":case"minute":case"mins":case"min":case"m":return s*us;case"seconds":case"second":case"secs":case"sec":case"s":return s*ds;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return}}}}function Qd(r){return r>=ms?Math.round(r/ms)+"d":r>=hs?Math.round(r/hs)+"h":r>=us?Math.round(r/us)+"m":r>=ds?Math.round(r/ds)+"s":r+"ms"}function zd(r){return Rs(r,ms,"day")||Rs(r,hs,"hour")||Rs(r,us,"minute")||Rs(r,ds,"second")||r+" ms"}function Rs(r,t,s){if(!(r<t))return r<t*1.5?Math.floor(r/t)+" "+s:Math.ceil(r/t)+" "+s+"s"}(function(r,t){t=r.exports=n.debug=n.default=n,t.coerce=l,t.disable=c,t.enable=i,t.enabled=o,t.humanize=qd,t.names=[],t.skips=[],t.formatters={};var s;function a(h){var u=0,p;for(p in h)u=(u<<5)-u+h.charCodeAt(p),u|=0;return t.colors[Math.abs(u)%t.colors.length]}function n(h){function u(){if(u.enabled){var p=u,f=+new Date,m=f-(s||f);p.diff=m,p.prev=s,p.curr=f,s=f;for(var x=new Array(arguments.length),g=0;g<x.length;g++)x[g]=arguments[g];x[0]=t.coerce(x[0]),typeof x[0]!="string"&&x.unshift("%O");var w=0;x[0]=x[0].replace(/%([a-zA-Z%])/g,function(k,S){if(k==="%%")return k;w++;var J=t.formatters[S];if(typeof J=="function"){var Z=x[w];k=J.call(p,Z),x.splice(w,1),w--}return k}),t.formatArgs.call(p,x);var b=u.log||t.log||console.log.bind(console);b.apply(p,x)}}return u.namespace=h,u.enabled=t.enabled(h),u.useColors=t.useColors(),u.color=a(h),typeof t.init=="function"&&t.init(u),u}function i(h){t.save(h),t.names=[],t.skips=[];for(var u=(typeof h=="string"?h:"").split(/[\s,]+/),p=u.length,f=0;f<p;f++)u[f]&&(h=u[f].replace(/\*/g,".*?"),h[0]==="-"?t.skips.push(new RegExp("^"+h.substr(1)+"$")):t.names.push(new RegExp("^"+h+"$")))}function c(){t.enable("")}function o(h){var u,p;for(u=0,p=t.skips.length;u<p;u++)if(t.skips[u].test(h))return!1;for(u=0,p=t.names.length;u<p;u++)if(t.names[u].test(h))return!0;return!1}function l(h){return h instanceof Error?h.stack||h.message:h}})(Mr,Mr.exports);var Wd=Mr.exports;(function(r,t){t=r.exports=Wd,t.log=n,t.formatArgs=a,t.save=i,t.load=c,t.useColors=s,t.storage=typeof chrome<"u"&&typeof chrome.storage<"u"?chrome.storage.local:o(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"];function s(){return typeof window<"u"&&window.process&&window.process.type==="renderer"?!0:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}t.formatters.j=function(l){try{return JSON.stringify(l)}catch(h){return"[UnexpectedJSONParseError]: "+h.message}};function a(l){var h=this.useColors;if(l[0]=(h?"%c":"")+this.namespace+(h?" %c":" ")+l[0]+(h?"%c ":" ")+"+"+t.humanize(this.diff),!!h){var u="color: "+this.color;l.splice(1,0,u,"color: inherit");var p=0,f=0;l[0].replace(/%[a-zA-Z%]/g,function(m){m!=="%%"&&(p++,m==="%c"&&(f=p))}),l.splice(f,0,u)}}function n(){return typeof console=="object"&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function i(l){try{l==null?t.storage.removeItem("debug"):t.storage.debug=l}catch{}}function c(){var l;try{l=t.storage.debug}catch{}return!l&&typeof process<"u"&&"env"in process&&(l={}.DEBUG),l}t.enable(c());function o(){try{return window.localStorage}catch{}}})(Fr,Fr.exports);var Hd=Fr.exports,Ur={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=["cueVideoById","loadVideoById","cueVideoByUrl","loadVideoByUrl","playVideo","pauseVideo","stopVideo","getVideoLoadedFraction","cuePlaylist","loadPlaylist","nextVideo","previousVideo","playVideoAt","setShuffle","setLoop","getPlaylist","getPlaylistIndex","setOption","mute","unMute","isMuted","setVolume","getVolume","seekTo","getPlayerState","getPlaybackRate","setPlaybackRate","getAvailablePlaybackRates","getPlaybackQuality","setPlaybackQuality","getAvailableQualityLevels","getCurrentTime","getDuration","removeEventListener","getVideoUrl","getVideoEmbedCode","getOptions","getOption","addEventListener","destroy","setSize","getIframe"],r.exports=t.default})(Ur,Ur.exports);var Kd=Ur.exports,qr={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=["ready","stateChange","playbackQualityChange","playbackRateChange","error","apiChange","volumeChange"],r.exports=t.default})(qr,qr.exports);var Yd=qr.exports,Br={exports:{}},Qr={exports:{}};(function(r,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default={BUFFERING:3,ENDED:0,PAUSED:2,PLAYING:1,UNSTARTED:-1,VIDEO_CUED:5},r.exports=t.default})(Qr,Qr.exports);var Gd=Qr.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=Gd,a=n(s);function n(i){return i&&i.__esModule?i:{default:i}}t.default={pauseVideo:{acceptableStates:[a.default.ENDED,a.default.PAUSED],stateChangeRequired:!1},playVideo:{acceptableStates:[a.default.ENDED,a.default.PLAYING],stateChangeRequired:!1},seekTo:{acceptableStates:[a.default.ENDED,a.default.PLAYING,a.default.PAUSED],stateChangeRequired:!0,timeout:3e3}},r.exports=t.default})(Br,Br.exports);var Jd=Br.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=Hd,a=u(s),n=Kd,i=u(n),c=Yd,o=u(c),l=Jd,h=u(l);function u(m){return m&&m.__esModule?m:{default:m}}var p=(0,a.default)("youtube-player"),f={};f.proxyEvents=function(m){var x={},g=function(N){var $="on"+N.slice(0,1).toUpperCase()+N.slice(1);x[$]=function(W){p('event "%s"',$,W),m.trigger(N,W)}},w=!0,b=!1,k=void 0;try{for(var S=o.default[Symbol.iterator](),J;!(w=(J=S.next()).done);w=!0){var Z=J.value;g(Z)}}catch(se){b=!0,k=se}finally{try{!w&&S.return&&S.return()}finally{if(b)throw k}}return x},f.promisifyPlayer=function(m){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,g={},w=function($){x&&h.default[$]?g[$]=function(){for(var W=arguments.length,P=Array(W),H=0;H<W;H++)P[H]=arguments[H];return m.then(function(re){var A=h.default[$],z=re.getPlayerState(),le=re[$].apply(re,P);return A.stateChangeRequired||Array.isArray(A.acceptableStates)&&A.acceptableStates.indexOf(z)===-1?new Promise(function(X){var U=function I(){var ee=re.getPlayerState(),Y=void 0;typeof A.timeout=="number"&&(Y=setTimeout(function(){re.removeEventListener("onStateChange",I),X()},A.timeout)),Array.isArray(A.acceptableStates)&&A.acceptableStates.indexOf(ee)!==-1&&(re.removeEventListener("onStateChange",I),clearTimeout(Y),X())};re.addEventListener("onStateChange",U)}).then(function(){return le}):le})}:g[$]=function(){for(var W=arguments.length,P=Array(W),H=0;H<W;H++)P[H]=arguments[H];return m.then(function(re){return re[$].apply(re,P)})}},b=!0,k=!1,S=void 0;try{for(var J=i.default[Symbol.iterator](),Z;!(b=(Z=J.next()).done);b=!0){var se=Z.value;w(se)}}catch(N){k=!0,S=N}finally{try{!b&&J.return&&J.return()}finally{if(k)throw S}}return g},t.default=f,r.exports=t.default})(Lr,Lr.exports);var Xd=Lr.exports;(function(r,t){Object.defineProperty(t,"__esModule",{value:!0});var s=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(p){return typeof p}:function(p){return p&&typeof Symbol=="function"&&p.constructor===Symbol&&p!==Symbol.prototype?"symbol":typeof p},a=Dd,n=h(a),i=Md,c=h(i),o=Xd,l=h(o);function h(p){return p&&p.__esModule?p:{default:p}}var u=void 0;t.default=function(p){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},m=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,x=(0,n.default)();if(u||(u=(0,c.default)(x)),f.events)throw new Error("Event handlers cannot be overwritten.");if(typeof p=="string"&&!document.getElementById(p))throw new Error('Element "'+p+'" does not exist.');f.events=l.default.proxyEvents(x);var g=new Promise(function(b){if((typeof p>"u"?"undefined":s(p))==="object"&&p.playVideo instanceof Function){var k=p;b(k)}else u.then(function(S){var J=new S.Player(p,f);return x.on("ready",function(){b(J)}),null})}),w=l.default.promisifyPlayer(g,m);return w.on=x.on,w.off=x.off,w},r.exports=t.default})(Dr,Dr.exports);var Zd=Dr.exports;const eu=Js(Zd);var tu=Object.defineProperty,su=Object.defineProperties,ru=Object.getOwnPropertyDescriptors,Ka=Object.getOwnPropertySymbols,au=Object.prototype.hasOwnProperty,nu=Object.prototype.propertyIsEnumerable,Ya=(r,t,s)=>t in r?tu(r,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):r[t]=s,zr=(r,t)=>{for(var s in t||(t={}))au.call(t,s)&&Ya(r,s,t[s]);if(Ka)for(var s of Ka(t))nu.call(t,s)&&Ya(r,s,t[s]);return r},Wr=(r,t)=>su(r,ru(t)),iu=(r,t,s)=>new Promise((a,n)=>{var i=l=>{try{o(s.next(l))}catch(h){n(h)}},c=l=>{try{o(s.throw(l))}catch(h){n(h)}},o=l=>l.done?a(l.value):Promise.resolve(l.value).then(i,c);o((s=s.apply(r,t)).next())});function ou(r,t){var s,a;if(r.videoId!==t.videoId)return!0;const n=((s=r.opts)==null?void 0:s.playerVars)||{},i=((a=t.opts)==null?void 0:a.playerVars)||{};return n.start!==i.start||n.end!==i.end}function Ga(r={}){return Wr(zr({},r),{height:0,width:0,playerVars:Wr(zr({},r.playerVars),{autoplay:0,start:0,end:0})})}function lu(r,t){return r.videoId!==t.videoId||!Vd(Ga(r.opts),Ga(t.opts))}function cu(r,t){var s,a,n,i;return r.id!==t.id||r.className!==t.className||((s=r.opts)==null?void 0:s.width)!==((a=t.opts)==null?void 0:a.width)||((n=r.opts)==null?void 0:n.height)!==((i=t.opts)==null?void 0:i.height)||r.iframeClassName!==t.iframeClassName||r.title!==t.title}var du={videoId:"",id:"",className:"",iframeClassName:"",style:{},title:"",loading:void 0,opts:{},onReady:()=>{},onError:()=>{},onPlay:()=>{},onPause:()=>{},onEnd:()=>{},onStateChange:()=>{},onPlaybackRateChange:()=>{},onPlaybackQualityChange:()=>{}},uu={videoId:He.string,id:He.string,className:He.string,iframeClassName:He.string,style:He.object,title:He.string,loading:He.oneOf(["lazy","eager"]),opts:He.objectOf(He.any),onReady:He.func,onError:He.func,onPlay:He.func,onPause:He.func,onEnd:He.func,onStateChange:He.func,onPlaybackRateChange:He.func,onPlaybackQualityChange:He.func},Us=class extends ce.Component{constructor(r){super(r),this.destroyPlayerPromise=void 0,this.onPlayerReady=t=>{var s,a;return(a=(s=this.props).onReady)==null?void 0:a.call(s,t)},this.onPlayerError=t=>{var s,a;return(a=(s=this.props).onError)==null?void 0:a.call(s,t)},this.onPlayerStateChange=t=>{var s,a,n,i,c,o,l,h;switch((a=(s=this.props).onStateChange)==null||a.call(s,t),t.data){case Us.PlayerState.ENDED:(i=(n=this.props).onEnd)==null||i.call(n,t);break;case Us.PlayerState.PLAYING:(o=(c=this.props).onPlay)==null||o.call(c,t);break;case Us.PlayerState.PAUSED:(h=(l=this.props).onPause)==null||h.call(l,t);break}},this.onPlayerPlaybackRateChange=t=>{var s,a;return(a=(s=this.props).onPlaybackRateChange)==null?void 0:a.call(s,t)},this.onPlayerPlaybackQualityChange=t=>{var s,a;return(a=(s=this.props).onPlaybackQualityChange)==null?void 0:a.call(s,t)},this.destroyPlayer=()=>this.internalPlayer?(this.destroyPlayerPromise=this.internalPlayer.destroy().then(()=>this.destroyPlayerPromise=void 0),this.destroyPlayerPromise):Promise.resolve(),this.createPlayer=()=>{if(typeof document>"u")return;if(this.destroyPlayerPromise){this.destroyPlayerPromise.then(this.createPlayer);return}const t=Wr(zr({},this.props.opts),{videoId:this.props.videoId});this.internalPlayer=eu(this.container,t),this.internalPlayer.on("ready",this.onPlayerReady),this.internalPlayer.on("error",this.onPlayerError),this.internalPlayer.on("stateChange",this.onPlayerStateChange),this.internalPlayer.on("playbackRateChange",this.onPlayerPlaybackRateChange),this.internalPlayer.on("playbackQualityChange",this.onPlayerPlaybackQualityChange),(this.props.title||this.props.loading)&&this.internalPlayer.getIframe().then(s=>{this.props.title&&s.setAttribute("title",this.props.title),this.props.loading&&s.setAttribute("loading",this.props.loading)})},this.resetPlayer=()=>this.destroyPlayer().then(this.createPlayer),this.updatePlayer=()=>{var t;(t=this.internalPlayer)==null||t.getIframe().then(s=>{this.props.id?s.setAttribute("id",this.props.id):s.removeAttribute("id"),this.props.iframeClassName?s.setAttribute("class",this.props.iframeClassName):s.removeAttribute("class"),this.props.opts&&this.props.opts.width?s.setAttribute("width",this.props.opts.width.toString()):s.removeAttribute("width"),this.props.opts&&this.props.opts.height?s.setAttribute("height",this.props.opts.height.toString()):s.removeAttribute("height"),this.props.title?s.setAttribute("title",this.props.title):s.setAttribute("title","YouTube video player"),this.props.loading?s.setAttribute("loading",this.props.loading):s.removeAttribute("loading")})},this.getInternalPlayer=()=>this.internalPlayer,this.updateVideo=()=>{var t,s,a,n;if(typeof this.props.videoId>"u"||this.props.videoId===null){(t=this.internalPlayer)==null||t.stopVideo();return}let i=!1;const c={videoId:this.props.videoId};if((s=this.props.opts)!=null&&s.playerVars&&(i=this.props.opts.playerVars.autoplay===1,"start"in this.props.opts.playerVars&&(c.startSeconds=this.props.opts.playerVars.start),"end"in this.props.opts.playerVars&&(c.endSeconds=this.props.opts.playerVars.end)),i){(a=this.internalPlayer)==null||a.loadVideoById(c);return}(n=this.internalPlayer)==null||n.cueVideoById(c)},this.refContainer=t=>{this.container=t},this.container=null,this.internalPlayer=null}componentDidMount(){this.createPlayer()}componentDidUpdate(r){return iu(this,null,function*(){cu(r,this.props)&&this.updatePlayer(),lu(r,this.props)&&(yield this.resetPlayer()),ou(r,this.props)&&this.updateVideo()})}componentWillUnmount(){this.destroyPlayer()}render(){return ce.createElement("div",{className:this.props.className,style:this.props.style},ce.createElement("div",{id:this.props.id,className:this.props.iframeClassName,ref:this.refContainer}))}},ir=Us;ir.propTypes=uu;ir.defaultProps=du;ir.PlayerState={UNSTARTED:-1,ENDED:0,PLAYING:1,PAUSED:2,BUFFERING:3,CUED:5};var Ps=ir;const hu=({isOpen:r,onClose:t,suggestion:s,sessionId:a,onVoteRequest:n})=>{var Qe,Ue;const[i,c]=d.useState(null),[o,l]=d.useState(0),[h,u]=d.useState(!1),[p,f]=d.useState(!1),[m,x]=d.useState(!1),[g,w]=d.useState(!1),[b,k]=d.useState(28),[S,J]=d.useState("#3B82F6"),[Z,se]=d.useState(!1),[N,$]=d.useState(-1),[W,P]=d.useState(0),H=d.useRef(null),re=d.useRef(null),A=d.useRef(null),z=d.useCallback(async()=>{var K;if(!s.youtubeVideoId){v.error("ID do vídeo não fornecido");return}try{se(!0),console.log(`🎵 Carregando letras para: ${s.title} - ${s.artist}`);const ie=await fetch(ge(`/lyrics/search?title=${encodeURIComponent(s.title)}&artist=${encodeURIComponent(s.artist)}&youtubeVideoId=${s.youtubeVideoId}`));if(ie.ok){const fe=await ie.json();if(fe.success&&fe.lyrics){c(fe.lyrics),v.success("Letras sincronizadas carregadas! 🎤"),console.log(`✅ Letras carregadas com ${((K=fe.lyrics.lines)==null?void 0:K.length)||0} linhas`);return}}try{const fe=await fetch(ge(`/lyrics/youtube/${s.youtubeVideoId}`));if(fe.ok){const Fe=await fe.json();if(Fe.success&&Fe.lyrics){c(Fe.lyrics),v.success("Letras encontradas pelo YouTube ID! 🎤");return}}}catch(fe){console.warn("Fallback YouTube ID falhou:",fe)}const Re=await fetch(ge(`/lyrics/test?title=${encodeURIComponent(s.title)}&artist=${encodeURIComponent(s.artist)}`));if(Re.ok){const fe=await Re.json();if(fe.success&&fe.lyrics){c(fe.lyrics),v("Usando letras de demonstração 🎵",{icon:"ℹ️",duration:4e3}),console.log("📝 Usando letras de teste");return}}throw new Error("Nenhuma fonte de letras disponível")}catch(ie){console.error("Erro ao carregar letras:",ie),v.error("Não foi possível carregar as letras. Modo karaokê sem sincronização."),c({id:`fallback_${s.youtubeVideoId}`,title:s.title,artist:s.artist,duration:s.duration||180,language:"pt",lines:[{time:0,text:"🎤 Modo Karaokê Ativo!"},{time:5,text:"Cante junto com a música!"},{time:10,text:"Letras sincronizadas não disponíveis"},{time:15,text:"Mas você pode cantar do mesmo jeito! 🎵"}],source:"fallback",isExplicit:!1,hasTimestamps:!1})}finally{se(!1)}},[s.title,s.artist,s.youtubeVideoId,s.duration]),le=d.useCallback(K=>{if(!i)return;let ie=-1,Re=0;for(let fe=0;fe<i.lines.length&&K>=i.lines[fe].time;fe++)ie=fe;if(ie>=0&&ie<i.lines.length-1){const fe=i.lines[ie],lt=i.lines[ie+1].time-fe.time,ze=K-fe.time;Re=Math.min(1,Math.max(0,ze/lt))}$(ie),P(Re)},[i]),X=d.useCallback(()=>{if(!H.current||!i)return;const K=H.current.getInternalPlayer();K&&typeof K.getCurrentTime=="function"&&K.getCurrentTime().then(ie=>{l(ie),le(ie)}).catch(ie=>{console.warn("Erro ao obter tempo atual do player:",ie)})},[i,le]),U=d.useCallback(()=>{var ie;const K=(ie=H.current)==null?void 0:ie.getInternalPlayer();K&&(h?K.pauseVideo():K.playVideo(),u(!h))},[h]),I=d.useCallback(()=>{var ie;const K=(ie=H.current)==null?void 0:ie.getInternalPlayer();K&&(p?(K.unMute(),K.setVolume(50)):K.mute(),f(!p))},[p]),ee=d.useCallback(()=>{var ie;const K=(ie=H.current)==null?void 0:ie.getInternalPlayer();K&&(K.seekTo(0),h||(K.playVideo(),u(!0))),l(0),$(-1),P(0)},[h]),Y=d.useCallback(()=>{var K,ie,Re;re.current&&(m?(Re=document.exitFullscreen)==null||Re.call(document):(ie=(K=re.current).requestFullscreen)==null||ie.call(K),x(!m))},[m]),ye=d.useCallback(K=>{K.target.setVolume(p?0:50)},[p]),te=d.useCallback(K=>{K.data===Ps.PlayerState.PLAYING?u(!0):K.data===Ps.PlayerState.PAUSED?u(!1):K.data===Ps.PlayerState.ENDED&&(u(!1),l(0),$(-1),P(0))},[]),Ee=d.useCallback(K=>{const ie=Math.floor(K/60),Re=Math.floor(K%60);return`${ie}:${Re.toString().padStart(2,"0")}`},[]),xe=d.useCallback(()=>!i||N<0?null:i.lines[N]||null,[i,N]),Ze=d.useCallback(()=>!i||N<0||N>=i.lines.length-1?null:i.lines[N+1]||null,[i,N]),Ve=d.useCallback(()=>{if(!i||N<0)return[];const K=Math.max(0,N-2),ie=Math.min(i.lines.length,N+3);return i.lines.slice(K,ie)},[i,N]);return d.useEffect(()=>(r&&s.youtubeVideoId&&z(),()=>{c(null),l(0),$(-1),P(0),u(!1),A.current&&(clearInterval(A.current),A.current=null)}),[r,s.youtubeVideoId,z]),d.useEffect(()=>(h&&i?A.current=setInterval(X,100):A.current&&(clearInterval(A.current),A.current=null),()=>{A.current&&(clearInterval(A.current),A.current=null)}),[h,i,X]),d.useEffect(()=>{const K=()=>{x(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",K),()=>document.removeEventListener("fullscreenchange",K)},[]),r?e.jsx(et,{children:e.jsxs(B.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex flex-col z-50 overflow-hidden",ref:re,role:"dialog","aria-modal":"true","aria-labelledby":"karaoke-title",children:[e.jsxs("header",{className:"flex items-center justify-between p-3 sm:p-4 bg-black/40 backdrop-blur-md border-b border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 overflow-hidden flex-1 min-w-0",children:[s.thumbnailUrl&&e.jsx("img",{src:s.thumbnailUrl,alt:`Capa de ${s.title}`,className:"w-8 h-8 sm:w-10 sm:h-10 rounded-lg object-cover flex-shrink-0 shadow-lg","aria-hidden":"true"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("h2",{id:"karaoke-title",className:"text-sm sm:text-lg font-bold text-white truncate leading-tight",children:s.title}),e.jsx("p",{className:"text-xs sm:text-sm text-purple-200 truncate",children:s.artist})]})]}),e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2 flex-shrink-0",children:[e.jsx("button",{onClick:()=>w(!g),className:"p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95","aria-label":g?"Fechar configurações":"Abrir configurações",children:e.jsx(Yt,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:Y,className:"p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95","aria-label":m?"Sair da tela cheia":"Entrar na tela cheia",children:m?e.jsx(mn,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"}):e.jsx(sa,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"})}),e.jsx("button",{onClick:t,className:"p-2 text-white hover:bg-white/20 rounded-full transition-colors active:scale-95","aria-label":"Fechar player de karaokê",children:e.jsx(jt,{className:"w-4 h-4 sm:w-5 sm:h-5","aria-hidden":"true"})})]})]}),g&&e.jsx(B.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"bg-black/60 backdrop-blur-md p-3 sm:p-4 mx-3 sm:mx-4 rounded-xl border border-white/20 shadow-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[e.jsx("span",{className:"text-white text-sm font-medium",children:"Tamanho da fonte:"}),e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx("input",{type:"range",min:"16",max:"40",value:b,onChange:K=>k(Number(K.target.value)),className:"flex-1 h-2 bg-white/20 rounded-lg appearance-none cursor-pointer","aria-label":"Ajustar tamanho da fonte"}),e.jsxs("span",{className:"text-white text-sm font-mono bg-white/10 px-2 py-1 rounded min-w-[50px] text-center",children:[b,"px"]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3",children:[e.jsx("span",{className:"text-white text-sm font-medium",children:"Cor de destaque:"}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"color",value:S,onChange:K=>J(K.target.value),className:"w-10 h-10 rounded-lg border-2 border-white/20 cursor-pointer","aria-label":"Escolher cor de destaque"}),e.jsx("span",{className:"text-white text-sm font-mono bg-white/10 px-2 py-1 rounded",children:S})]})]})]})}),e.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[s.youtubeVideoId&&e.jsx("div",{className:"hidden sm:block w-full px-3 sm:px-4 pt-2",children:e.jsx("div",{className:"w-full max-w-md mx-auto aspect-video",children:e.jsx(Ps,{videoId:s.youtubeVideoId,opts:{playerVars:{autoplay:0,controls:0,rel:0,showinfo:0,modestbranding:1}},onReady:ye,onStateChange:te,ref:H,className:"w-full h-full rounded-xl overflow-hidden shadow-2xl border border-white/20"})})}),e.jsx("div",{className:"flex-1 flex flex-col justify-center px-4 sm:px-6 py-4 overflow-hidden",children:Z?e.jsxs("div",{className:"text-center flex flex-col items-center justify-center h-full",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full animate-spin mb-6"}),e.jsx("div",{className:"absolute inset-0 w-16 h-16 border-4 border-blue-400 border-b-transparent rounded-full animate-spin animate-reverse"})]}),e.jsx("p",{className:"text-white text-lg sm:text-xl font-medium",children:"Carregando letras..."}),e.jsx("p",{className:"text-purple-200 text-sm mt-2",children:"Preparando experiência de karaokê"})]}):i?e.jsxs("div",{className:"h-full flex flex-col justify-center text-center space-y-4 sm:space-y-6",children:[e.jsx(B.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{duration:.5,ease:"easeOut"},className:"flex-1 flex items-center justify-center px-2","aria-live":"polite",children:e.jsx("div",{className:"max-w-full",children:e.jsx("div",{className:"font-bold leading-tight text-center break-words",style:{fontSize:`${Math.max(b*.8,18)}px`,color:xe()?S:"white",textShadow:"2px 2px 8px rgba(0,0,0,0.9)",filter:xe()?`drop-shadow(0 0 20px ${S}40)`:"none",lineHeight:"1.2"},children:((Qe=xe())==null?void 0:Qe.text)||"🎵 Aguardando início..."})})},`current-${N}`),xe()&&i.hasTimestamps&&e.jsxs(B.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.3},className:"px-6 sm:px-12",children:[e.jsx("div",{className:"w-full bg-white/20 rounded-full h-2 shadow-inner",children:e.jsx(B.div,{className:"h-2 rounded-full bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 shadow-lg",style:{width:`${W*100}%`},transition:{duration:.1}})}),e.jsxs("div",{className:"flex justify-between text-xs text-white/60 mt-1",children:[e.jsx("span",{children:"Linha atual"}),e.jsxs("span",{children:[Math.round(W*100),"%"]})]})]}),Ze()&&e.jsx(B.div,{initial:{opacity:0,y:20},animate:{opacity:.8,y:0},transition:{duration:.4,delay:.2},className:"px-4",children:e.jsxs("div",{className:"bg-white/5 rounded-xl p-3 sm:p-4 border border-white/10",children:[e.jsx("div",{className:"text-xs text-blue-300 font-medium mb-1",children:"Próxima linha:"}),e.jsx("div",{className:"text-white/80 leading-relaxed break-words",style:{fontSize:`${Math.max(b*.6,14)}px`,textShadow:"1px 1px 4px rgba(0,0,0,0.8)"},children:(Ue=Ze())==null?void 0:Ue.text})]})},`next-${N+1}`),e.jsx("div",{className:"hidden sm:block space-y-1 opacity-30 max-h-24 overflow-y-auto",children:Ve().slice(0,3).map((K,ie)=>{const Re=i.lines.indexOf(K),fe=Re===N,Fe=Re<N;return fe||Re===N+1?null:e.jsx(B.div,{initial:{opacity:0},animate:{opacity:Fe?.4:.6},transition:{duration:.3},className:"text-white/50 text-sm px-4 truncate",children:K.text},`context-${Re}`)})})]}):e.jsxs("div",{className:"h-full flex flex-col items-center justify-center text-center px-4",children:[e.jsx(B.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.6,ease:"easeOut"},className:"mb-6",children:e.jsxs("div",{className:"relative",children:[e.jsx(Bs,{className:"w-16 h-16 sm:w-20 sm:h-20 text-purple-400 mx-auto mb-4"}),e.jsx("div",{className:"absolute inset-0 w-16 h-16 sm:w-20 sm:h-20 border-4 border-purple-400/30 rounded-full animate-ping"})]})}),e.jsxs(B.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},className:"space-y-3",children:[e.jsx("h3",{className:"text-white text-xl sm:text-2xl font-bold",children:"🎤 Modo Karaokê Ativo!"}),e.jsx("p",{className:"text-purple-200 text-base sm:text-lg",children:"Letras sincronizadas não disponíveis"}),e.jsx("p",{className:"text-white/80 text-sm sm:text-base",children:"Mas você pode cantar junto com o vídeo! 🎵"}),e.jsx("div",{className:"mt-6 p-4 bg-white/5 rounded-xl border border-white/10",children:e.jsxs("p",{className:"text-white/70 text-sm",children:["💡 ",e.jsx("strong",{children:"Dica:"})," Use os controles abaixo para ajustar o volume e pausar a música"]})})]})]})})]}),e.jsxs("footer",{className:"bg-black/50 backdrop-blur-md border-t border-white/10",children:[i&&e.jsxs("div",{className:"px-4 pt-3",children:[e.jsxs("div",{className:"flex items-center justify-between text-white text-xs sm:text-sm mb-2",children:[e.jsx("span",{className:"font-mono",children:Ee(o)}),e.jsx("span",{className:"font-mono",children:Ee(s.duration||i.duration)})]}),e.jsx("div",{className:"w-full bg-white/20 rounded-full h-2 shadow-inner",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-400 to-blue-400 h-2 rounded-full transition-all duration-200 shadow-lg",style:{width:`${o/(s.duration||i.duration)*100}%`},role:"progressbar","aria-valuenow":o/(s.duration||i.duration)*100,"aria-valuemin":0,"aria-valuemax":100})})]}),e.jsxs("div",{className:"p-4",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 sm:gap-6",children:[e.jsx("button",{onClick:ee,className:"p-3 bg-white/10 text-white rounded-full hover:bg-white/20 active:scale-95 transition-all border border-white/20","aria-label":"Reiniciar música",children:e.jsx(Ri,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"})}),e.jsx("button",{onClick:U,className:"p-4 sm:p-5 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full hover:from-purple-700 hover:to-blue-700 active:scale-95 transition-all shadow-lg border border-white/20","aria-label":h?"Pausar música":"Tocar música",children:h?e.jsx(Zr,{className:"w-6 h-6 sm:w-8 sm:h-8","aria-hidden":"true"}):e.jsx(ot,{className:"w-6 h-6 sm:w-8 sm:h-8","aria-hidden":"true"})}),e.jsx("button",{onClick:I,className:"p-3 bg-white/10 text-white rounded-full hover:bg-white/20 active:scale-95 transition-all border border-white/20","aria-label":p?"Ativar som":"Silenciar som",children:p?e.jsx(un,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"}):e.jsx(er,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"})}),n&&e.jsx("button",{onClick:n,className:"p-3 bg-pink-600/80 text-white rounded-full hover:bg-pink-600 active:scale-95 transition-all border border-white/20","aria-label":"Solicitar votação da performance",children:e.jsx(Jr,{className:"w-5 h-5 sm:w-6 sm:h-6","aria-hidden":"true"})})]}),e.jsxs("div",{className:"flex items-center justify-center gap-4 sm:gap-6 mt-4 text-white/70 text-xs sm:text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(Bs,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"hidden sm:inline",children:"Cante Comigo"}),e.jsx("span",{className:"sm:hidden",children:"Karaokê"})]}),(i==null?void 0:i.hasTimestamps)&&e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(Zs,{className:"w-4 h-4 text-yellow-400","aria-hidden":"true"}),e.jsx("span",{className:"hidden sm:inline",children:"Sincronizado"}),e.jsx("span",{className:"sm:hidden",children:"Sync"})]}),e.jsxs("div",{className:"flex items-center gap-1.5",children:[e.jsx(yt,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{className:"hidden sm:inline",children:"Interativo"}),e.jsx("span",{className:"sm:hidden",children:"Live"})]})]})]})]})]})}):null},mu=({isOpen:r,onClose:t,restaurantId:s,sessionId:a})=>{const[n,i]=d.useState(!1),[c,o]=d.useState(!1),[l,h]=d.useState({name:"",avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}}),[u,p]=d.useState([]),f=d.useCallback(()=>{o(!0);try{const b=localStorage.getItem(`clientProfile_${a}`);if(b)h(JSON.parse(b));else{const k={name:`Cliente ${a.slice(0,8)}`,avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}};h(k),localStorage.setItem(`clientProfile_${a}`,JSON.stringify(k))}}catch(b){console.error("Error loading profile:",b)}finally{o(!1)}},[a]),m=d.useCallback(async()=>{o(!0);try{const b=await fetch(ge(`/collaborative-playlist/${s}/ranking`));if(b.ok){const{ranking:k}=await b.json();p(k)}else throw new Error("Falha ao buscar ranking")}catch(b){console.error("Erro ao carregar ranking:",b),p([{title:"Música Exemplo",artist:"Artista",points:61,voteCount:12,superVote5:3,superVote20:1,superVote40:1},{title:"Outra Música",artist:"Outro",points:40,voteCount:5,superVote5:2,superVote20:1,superVote40:0}]),v.error("Erro ao carregar ranking, usando dados de exemplo")}finally{o(!1)}},[s]),x=d.useCallback(()=>{try{localStorage.setItem(`clientProfile_${a}`,JSON.stringify(l)),i(!1),v.success("Perfil salvo com sucesso!")}catch(b){console.error("Error saving profile:",b),v.error("Erro ao salvar perfil")}},[l,a]),g=d.useCallback(b=>{var S;const k=(S=b.target.files)==null?void 0:S[0];if(k&&k.type.startsWith("image/")){const J=new FileReader;J.onload=()=>{h(Z=>({...Z,avatar:J.result}))},J.readAsDataURL(k)}else v.error("Por favor, selecione uma imagem válida")},[]),w=d.useCallback(()=>Math.min(l.experience/l.nextLevelExp*100,100),[l.experience,l.nextLevelExp]);return d.useCallback(b=>{const k={Iniciante:"text-gray-600 bg-gray-100 dark:bg-gray-700",Ativo:"text-blue-600 bg-blue-100 dark:bg-blue-900/30",Engajado:"text-green-600 bg-green-100 dark:bg-green-900/30",Expert:"text-purple-600 bg-purple-100 dark:bg-purple-900/30"};return k[b]||k.Iniciante},[]),d.useEffect(()=>{r&&a&&(f(),m())},[r,a,f,m]),r?e.jsx(et,{children:e.jsx(B.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:t,role:"dialog","aria-modal":"true","aria-labelledby":"profile-modal-title",children:e.jsxs(B.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},onClick:b=>b.stopPropagation(),className:"bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[e.jsx("header",{className:"bg-gradient-to-r from-purple-600 to-blue-600 p-4 sm:p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center",children:l.avatar?e.jsx("img",{src:l.avatar,alt:`Avatar de ${l.name}`,className:"w-full h-full rounded-full object-cover"}):e.jsx(Wt,{className:"w-8 h-8","aria-hidden":"true"})}),e.jsxs("div",{children:[e.jsx("h2",{id:"profile-modal-title",className:"text-xl sm:text-2xl font-bold",children:l.name}),e.jsx("p",{className:"text-blue-100 text-sm",children:l.title}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs("span",{className:"text-sm",children:["Nível ",l.level]}),e.jsx("div",{className:"w-24 h-2 bg-white/20 rounded-full",children:e.jsx("div",{className:"h-full bg-white rounded-full transition-all duration-300",style:{width:`${w()}%`},role:"progressbar","aria-valuenow":Math.round(w()),"aria-valuemin":0,"aria-valuemax":100})})]})]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("button",{onClick:t,className:"p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors","aria-label":"Fechar perfil",children:e.jsx(jt,{className:"w-5 h-5","aria-hidden":"true"})})})]})}),e.jsx("div",{className:"p-4 sm:p-6 max-h-[60vh] overflow-y-auto",children:c?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"Carregando perfil..."})]}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Nome"}),n?e.jsx("input",{type:"text",value:l.name,onChange:b=>h({...l,name:b.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500","aria-label":"Editar nome do perfil"}):e.jsx("p",{className:"text-gray-900 dark:text-white",children:l.name})]}),n&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Avatar"}),e.jsx("input",{type:"file",accept:"image/*",onChange:g,className:"w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-blue-100 file:text-blue-700 hover:file:bg-blue-200","aria-label":"Fazer upload de avatar"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Membro desde"}),e.jsx("p",{className:"text-gray-900 dark:text-white",children:new Date(l.joinedAt).toLocaleDateString("pt-BR",{day:"2-digit",month:"long",year:"numeric"})})]})]}),e.jsx("div",{className:"flex justify-end items-center",children:n?e.jsxs("button",{onClick:x,className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors","aria-label":"Salvar alterações do perfil",children:[e.jsx(on,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Salvar"})]}):e.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Editar perfil",children:[e.jsx(Wt,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Editar"})]})})]})})})]})})}):null},pu=({restaurantId:r,currentTableNumber:t,isVisible:s,onClose:a})=>{const[n,i]=d.useState([]),[c,o]=d.useState(!1),[l,h]=d.useState("points");d.useEffect(()=>{if(s){u();const x=setInterval(u,3e4);return()=>clearInterval(x)}},[s,r]);const u=async()=>{o(!0);try{const x=await fetch(`http://localhost:8001/api/v1/analytics/tables/${r}/leaderboard`);if(x.ok){const w=(await x.json()).tables.map((b,k)=>({tableNumber:b.tableNumber||`Mesa ${k+1}`,totalSuggestions:b.totalSuggestions||0,totalVotes:b.totalVotes||0,averageScore:b.averageScore||0,paidSuggestions:b.paidSuggestions||0,points:b.points||0,rank:k+1,isCurrentTable:b.tableNumber===t}));i(w)}}catch(x){console.error("Erro ao carregar estatísticas das mesas:",x),i([{tableNumber:"Mesa 5",totalSuggestions:8,totalVotes:45,averageScore:4.2,paidSuggestions:3,points:850,rank:1,isCurrentTable:t==="5"},{tableNumber:"Mesa 10",totalSuggestions:6,totalVotes:38,averageScore:3.8,paidSuggestions:2,points:720,rank:2,isCurrentTable:t==="10"},{tableNumber:"Mesa 3",totalSuggestions:5,totalVotes:32,averageScore:3.5,paidSuggestions:1,points:650,rank:3,isCurrentTable:t==="3"},{tableNumber:"Mesa 7",totalSuggestions:4,totalVotes:28,averageScore:3.2,paidSuggestions:1,points:580,rank:4,isCurrentTable:t==="7"},{tableNumber:"Mesa 12",totalSuggestions:3,totalVotes:22,averageScore:2.9,paidSuggestions:0,points:450,rank:5,isCurrentTable:t==="12"}])}finally{o(!1)}},p=[...n].sort((x,g)=>{switch(l){case"suggestions":return g.totalSuggestions-x.totalSuggestions;case"votes":return g.totalVotes-x.totalVotes;default:return g.points-x.points}}),f=x=>{switch(x){case 1:return e.jsx(Ai,{className:"w-5 h-5 text-yellow-500"});case 2:return e.jsx(Ii,{className:"w-5 h-5 text-gray-400"});case 3:return e.jsx(Pi,{className:"w-5 h-5 text-orange-600"});default:return e.jsx(vr,{className:"w-5 h-5 text-gray-500"})}},m=x=>{switch(x){case 1:return"from-yellow-500 to-yellow-600";case 2:return"from-gray-400 to-gray-500";case 3:return"from-orange-500 to-orange-600";default:return"from-gray-600 to-gray-700"}};return s?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs(B.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md max-h-[80vh] overflow-hidden",children:[e.jsx("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(vr,{className:"w-6 h-6"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold",children:"Ranking das Mesas"}),e.jsx("p",{className:"text-purple-100 text-sm",children:"Competição em tempo real"})]})]}),e.jsx("button",{onClick:a,className:"text-white/80 hover:text-white transition-colors",children:e.jsx(jt,{className:"w-6 h-6"})})]})}),e.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700",children:e.jsx("div",{className:"flex space-x-2",children:[{key:"points",label:"Pontos"},{key:"suggestions",label:"Sugestões"},{key:"votes",label:"Votos"}].map(x=>e.jsx("button",{onClick:()=>h(x.key),className:`px-3 py-1 rounded-full text-sm font-medium transition-colors ${l===x.key?"bg-purple-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-purple-100 dark:hover:bg-gray-600"}`,children:x.label},x.key))})}),e.jsx("div",{className:"p-4 max-h-96 overflow-y-auto",children:c?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"}),e.jsx("span",{className:"ml-3 text-gray-600 dark:text-gray-400",children:"Carregando ranking..."})]}):e.jsx("div",{className:"space-y-3",children:e.jsx(et,{children:p.map((x,g)=>e.jsxs(B.div,{layout:!0,initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,delay:g*.05},className:`p-4 rounded-lg border-2 transition-all ${x.isCurrentTable?"border-purple-500 bg-purple-50 dark:bg-purple-900/20":"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"}`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`p-2 rounded-full bg-gradient-to-r ${m(g+1)}`,children:f(g+1)}),e.jsxs("div",{children:[e.jsxs("h3",{className:`font-bold ${x.isCurrentTable?"text-purple-700 dark:text-purple-300":"text-gray-900 dark:text-white"}`,children:[x.tableNumber,x.isCurrentTable&&e.jsx("span",{className:"ml-2 text-xs bg-purple-600 text-white px-2 py-1 rounded-full",children:"Você"})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[e.jsxs("span",{children:[x.points," pts"]}),e.jsxs("span",{children:[x.totalSuggestions," sugestões"]}),e.jsxs("span",{children:[x.totalVotes," votos"]})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center space-x-1 text-yellow-500",children:[e.jsx(Zs,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:x.averageScore.toFixed(1)})]}),x.paidSuggestions>0&&e.jsxs("div",{className:"text-xs text-green-600 dark:text-green-400",children:[x.paidSuggestions," pagas"]})]})]}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full bg-gradient-to-r ${m(g+1)} transition-all duration-500`,style:{width:`${Math.min(x.points/Math.max(...p.map(w=>w.points))*100,100)}%`}})})})]},x.tableNumber))})})}),e.jsx("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 text-center",children:e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"🎵 Continue sugerindo e votando para subir no ranking!"})})]})}):null},xu=r=>{const t=Math.floor(r/60),s=r%60;return`${t}:${String(s).padStart(2,"0")}`},fu=()=>d.useCallback(async(r,t={},s=3,a=1e3)=>{let n;for(let i=0;i<s;i++)try{const c=await fetch(r,t);if(c.ok)return c;let o=`Erro na requisição: ${c.status} ${c.statusText}`;try{const l=await c.json();l.error?o=l.error:l.message&&(o=l.message)}catch{}if(n=new Error(o),c.status===401||c.status===403||c.status===409)throw n}catch(c){if(n=c instanceof Error?c:new Error(String(c)),i===s-1)break;console.warn(`Tentativa ${i+1} falhou, tentando novamente em ${a}ms...`),await new Promise(o=>setTimeout(o,a))}throw n},[]),gu=()=>({getBadges:d.useCallback(t=>{const s=[];return t.suggestionsCount>=1&&s.push("🎵 Primeira Sugestão"),t.suggestionsCount>=5&&s.push("🎶 Melomaníaco"),t.suggestionsCount>=10&&s.push("🎸 DJ Amador"),t.votesCount>=10&&s.push("👍 Crítico Musical"),t.votesCount>=25&&s.push("⭐ Especialista"),t.streak>=3&&s.push("🔥 Em Chamas"),t.points>=500&&s.push("🏆 Lenda"),s},[])}),Ja=()=>{const{restaurantId:r}=At(),[t]=Kr(),s=t.get("table"),a=fu(),{getBadges:n}=gu(),[i,c]=d.useState(null),[o,l]=d.useState(""),[h,u]=d.useState([]),[p,f]=d.useState(!1),[m,x]=d.useState([]),[g,w]=d.useState(1),[b,k]=d.useState(!1),[S,J]=d.useState(!0),[Z,se]=d.useState(0),[N,$]=d.useState(null),[W,P]=d.useState(!0),[H,re]=d.useState(!1),[A,z]=d.useState([]),[le,X]=d.useState([]),[U,I]=d.useState([]),[ee,Y]=d.useState(null),ye=2*60*1e3,[te,Ee]=d.useState(null),[xe,Ze]=d.useState({points:0,level:1,badges:[],suggestionsCount:0,votesCount:0,streak:0}),[Ve,Qe]=d.useState(""),[Ue,K]=d.useState(!0),[ie,Re]=d.useState(!1),[fe,Fe]=d.useState(null),[lt,ze]=d.useState(!1),[$e,qe]=d.useState(!1),[ke,tt]=d.useState(null),[Be,je]=d.useState(!1),[Pe,ct]=d.useState(null),[Ae,Se]=d.useState(!1),[Ye,dt]=d.useState(!1),[Me,st]=d.useState(!1);d.useState(!1);const[vt,ut]=d.useState(!1);d.useState(new Map);const xt=d.useRef(0),{isConnected:Te,joinRestaurant:ft,on:at,off:rt,reconnect:Tt}=qt();d.useMemo(()=>[],[]),d.useEffect(()=>{if(r)try{localStorage.setItem("currentRestaurantId",r),Tt()}catch{}},[r,Tt]),d.useEffect(()=>{Te&&r&&ft(r)},[Te,r,ft]),d.useEffect(()=>{r&&Dt.startCooldownTimer()},[r]);const C=d.useCallback(async()=>{var y;if(!r){v.error("ID do restaurante não encontrado");return}try{const R=await(await a(ge(`/restaurants/${r}`),{headers:{"Content-Type":"application/json"}})).json();c(R.restaurant||{id:r,name:"Restaurante Demo",description:"Ambiente acolhedor com música interativa",isActive:!0,isOpen:!0}),console.log(`🏪 Entrou na sala do restaurante: ${((y=R.restaurant)==null?void 0:y.name)||"Restaurante Demo"}`)}catch(E){console.error("Erro ao carregar restaurante:",E),c({id:r,name:"Restaurante Demo",description:"Ambiente acolhedor com música interativa",isActive:!0,isOpen:!0}),v.error("Erro ao carregar dados do restaurante",{duration:4e3,icon:"⚠️"})}},[r,a]),T=d.useCallback(async()=>{var y;if(!r){v.error("ID do restaurante não encontrado");return}try{const R=await(await a(ge(`/playback/${r}/state`),{headers:{"Content-Type":"application/json"}})).json();if(R.success===!1)throw new Error(R.message||"Erro ao carregar música atual");$(((y=R.state)==null?void 0:y.currentTrack)||null)}catch(E){console.error("Erro ao carregar música atual:",E),$(null)}},[r,a]),_=d.useCallback(async()=>{if(r)try{const E=await(await a(ge(`/playback-queue/${r}`),{headers:{"Content-Type":"application/json"}})).json();if(E.success===!1)throw new Error(E.message||"Erro ao carregar fila");if(E!=null&&E.queue){const R=Array.isArray(E.queue)?[...E.queue].sort((q,ne)=>{const L=typeof q.position=="number"&&q.position>0,oe=typeof ne.position=="number"&&ne.position>0;if(L&&oe)return q.position-ne.position;if(L)return-1;if(oe)return 1;const ve=q.createdAt?new Date(q.createdAt).getTime():0,D=ne.createdAt?new Date(ne.createdAt).getTime():0;return ve-D}):[];z(R)}E!=null&&E.currentlyPlaying&&$(E.currentlyPlaying)}catch(y){console.error("Erro ao carregar fila de reprodução:",y)}},[r,a]),Q=d.useCallback(async(y=1,E=!1)=>{if(!r){v.error("ID do restaurante não encontrado");return}k(!0);try{const q=new URLSearchParams({page:y.toString(),limit:24 .toString()});if(m.length){const oe=m.filter(D=>["rock","pop","sertanejo","mpb","eletronica","funk"].includes(D));oe.length&&q.append("genres",oe.join(","));const ve=m.filter(D=>["happy","sad","energetic","calm"].includes(D));ve.length&&q.append("moods",ve.join(","))}const L=await(await a(ge(`/restaurants/${r}/playlist?${q.toString()}`),{headers:{"Content-Type":"application/json"}})).json();if(L.success&&L.results){const oe=L.results.map(D=>({id:D.youtubeVideoId||D.id,title:D.title,artist:D.artist,duration:D.duration||0,formattedDuration:D.formattedDuration||"0:00",thumbnailUrl:D.thumbnailUrl||`https://img.youtube.com/vi/${D.youtubeVideoId||D.id}/mqdefault.jpg`,youtubeVideoId:D.youtubeVideoId||D.id,channelName:D.artist,viewCount:D.viewCount||0,publishedAt:D.addedAt||new Date().toISOString(),genre:Array.isArray(D.genres)&&D.genres.length?D.genres[0]:D.genre}));let ve=oe;try{const D=await Dt.checkMultipleSongsCooldown(r,oe.map(Ne=>({youtubeVideoId:Ne.youtubeVideoId}))),F=new Map;D.forEach(Ne=>F.set(Ne.youtubeVideoId,{isInCooldown:Ne.isInCooldown,cooldownTimeLeft:Ne.cooldownTimeLeft})),ve=oe.map(Ne=>{var We,St;return{...Ne,isInCooldown:(We=F.get(Ne.youtubeVideoId))==null?void 0:We.isInCooldown,cooldownTimeLeft:(St=F.get(Ne.youtubeVideoId))==null?void 0:St.cooldownTimeLeft}})}catch(D){console.warn("Falha ao enriquecer playlist com cooldown:",D)}u(D=>E?[...D,...ve]:ve),se(L.total||ve.length),J(ve.length===24),w(y)}else E||(u([]),v("Nenhuma música encontrada na playlist",{icon:"ℹ️"}))}catch(R){console.error("Erro ao carregar playlist:",R),v.error("Erro ao carregar playlist do restaurante"),E||u([])}finally{k(!1)}},[r,m,a]),G=d.useCallback(async()=>{if(!r)return;k(!0),Y(null);let y=0;const E=3;for(;y<E;)try{const R=await fetch(ge(`/restaurants/${r}/playlist`),{headers:{"Content-Type":"application/json"}});if(!R.ok){if((R.status===502||R.status===500)&&y<E-1){y++,await new Promise(L=>setTimeout(L,500*y));continue}throw new Error(`HTTP ${R.status}`)}const q=await R.json(),ne=Array.isArray(q==null?void 0:q.results)?q.results:[];ne.sort((L,oe)=>{if(L.playlistId!==oe.playlistId)return(L.playlistId||"").localeCompare(oe.playlistId||"");const ve=L.position&&L.position>0?L.position:Number.MAX_SAFE_INTEGER,D=oe.position&&oe.position>0?oe.position:Number.MAX_SAFE_INTEGER;return ve-D}),I(ne),k(!1);return}catch(R){if(y<E-1){y++,await new Promise(q=>setTimeout(q,500*y));continue}Y((R==null?void 0:R.message)||"Falha ao carregar playlist"),k(!1);return}},[r]),ue=d.useCallback(async()=>{if(!o.trim()){v.error("Digite uma busca válida");return}if(!r){v.error("ID do restaurante não encontrado");return}f(!0);try{const y=new URLSearchParams({q:encodeURIComponent(o.trim())});if(m.length){const q=m.filter(L=>["rock","pop","sertanejo","mpb","eletronica","funk"].includes(L));q.length&&y.append("genres",q.join(","));const ne=m.filter(L=>["happy","sad","energetic","calm"].includes(L));ne.length&&y.append("moods",ne.join(","))}const R=await(await a(ge(`/search/music?${y.toString()}`),{headers:{"Content-Type":"application/json"}})).json();if(R.success&&R.results){const q=R.results.map(L=>({id:L.youtubeVideoId||L.id,title:L.title,artist:L.artist,duration:L.duration||0,formattedDuration:L.formattedDuration||"0:00",thumbnailUrl:L.thumbnailUrl||`https://img.youtube.com/vi/${L.youtubeVideoId||L.id}/mqdefault.jpg`,youtubeVideoId:L.youtubeVideoId||L.id,channelName:L.artist,viewCount:L.viewCount||0,publishedAt:L.addedAt||new Date().toISOString(),genre:Array.isArray(L.genres)&&L.genres.length?L.genres[0]:L.genre}));let ne=q;try{const L=await Dt.checkMultipleSongsCooldown(r,q.map(ve=>({youtubeVideoId:ve.youtubeVideoId}))),oe=new Map;L.forEach(ve=>oe.set(ve.youtubeVideoId,{isInCooldown:ve.isInCooldown,cooldownTimeLeft:ve.cooldownTimeLeft})),ne=q.map(ve=>{var D,F;return{...ve,isInCooldown:(D=oe.get(ve.youtubeVideoId))==null?void 0:D.isInCooldown,cooldownTimeLeft:(F=oe.get(ve.youtubeVideoId))==null?void 0:F.cooldownTimeLeft}})}catch(L){console.warn("Falha ao enriquecer busca com cooldown:",L)}u(ne),se(R.total||ne.length),J(!1),w(1),ne.length>0?v.success(`${ne.length} música(s) encontrada(s)`,{icon:"🔍"}):v(`Nenhuma música encontrada para "${o}"`,{icon:"🔍"})}else u([]),v(`Nenhuma música encontrada para "${o}"`,{icon:"🔍"})}catch(y){console.error("Erro ao buscar músicas:",y),v.error("Erro ao buscar músicas"),u([])}finally{f(!1)}},[o,r,m,a]),me=d.useCallback(async()=>{if(!r){v.error("ID do restaurante não encontrado");return}K(!0);try{const y=await gr.forceNewSession(r,s||void 0,Ve||void 0);Ee(y),Ze({points:y.points||0,level:y.level||1,badges:y.badges||[],suggestionsCount:y.suggestionsCount||0,votesCount:y.votesCount||0,streak:y.streak||0}),!y.clientName&&!Ve&&ze(!0)}catch(y){console.error("Erro ao inicializar sessão:",y),v.error("Erro ao inicializar sessão",{duration:4e3,icon:"⚠️"})}finally{K(!1)}},[r,s,Ve]),we=d.useCallback((y,E=10)=>{Ze(R=>{const q={...R,points:R.points+E,suggestionsCount:y==="suggest"?R.suggestionsCount+1:R.suggestionsCount,votesCount:y==="vote"?R.votesCount+1:R.votesCount,streak:R.streak+1},ne=Math.floor(q.points/100)+1,L=n(q);ne>R.level&&(Re(!0),setTimeout(()=>Re(!1),3e3),v.success(`🎉 Level Up! Agora você é nível ${ne}!`,{duration:5e3,icon:"🏆"}));const oe=L.find(ve=>!R.badges.includes(ve));return oe&&(gr.awardBadge(oe),Fe(oe),setTimeout(()=>Fe(null),3e3),v.success(`🏆 Nova conquista: ${oe}!`,{duration:5e3,icon:"🎖️"})),{...q,level:ne,badges:L}})},[n]),V=d.useCallback(y=>{tt(y),qe(!0)},[]),ae=d.useCallback(async y=>{if(!te||!r){v.error("Sessão ou restaurante não encontrado");return}try{if((await Dt.checkSongCooldown(r,y.youtubeVideoId)).isInCooldown){v.error("Música em cooldown no momento. Tente outra ou aguarde.");return}const q=await(await a(ge(`/collaborative-playlist/${r}/vote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:y.youtubeVideoId,tableNumber:te.tableNumber,clientSessionId:te.id})})).json();if((q==null?void 0:q.success)===!1)throw new Error((q==null?void 0:q.message)||"Falha ao registrar voto");v.success(`Voto registrado para "${y.title}" ✅`),we("vote",10)}catch(E){console.error("Erro ao registrar voto:",E),v.error(E instanceof Error?E.message:"Erro ao registrar voto")}},[te,r,a,we]),de=d.useCallback(async y=>{if(!r){v.error("Restaurante não encontrado");return}try{const E=await fetch(ge(`/playback-queue/${r}/like`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:y.youtubeVideoId,action:"like"})}),R=await E.json();if(!E.ok||(R==null?void 0:R.success)===!1)throw new Error((R==null?void 0:R.message)||"Falha ao registrar curtida");v.success(`Curtida registrada para "${y.title}" ❤`)}catch(E){console.error("Erro ao registrar curtida:",E),v.error(E instanceof Error?E.message:"Erro ao registrar curtida")}},[r]),j=d.useCallback(async({paymentId:y,amountCents:E,clientMessage:R,clientName:q})=>{if(!ke||!te||!r){v.error("Dados insuficientes para processar pagamento");return}try{const L=await(await a(ge(`/collaborative-playlist/${r}/supervote`),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({youtubeVideoId:ke.youtubeVideoId,paymentAmount:(E||0)/100,paymentId:y,tableNumber:te.tableNumber,clientSessionId:te.id,clientMessage:R||void 0,clientName:q||Ve||void 0})})).json();if(L.success===!1)throw new Error(L.message||"Erro ao processar pagamento");v.success(`SuperVoto aplicado em "${ke.title}"! ⭐`),we("vote",25),l(""),o.trim()&&Q(1),ct({id:ke.id,title:ke.title,artist:ke.artist,thumbnailUrl:ke.thumbnailUrl,duration:ke.duration,youtubeVideoId:ke.youtubeVideoId,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString(),isPaid:!0,clientSessionId:te.id}),qe(!1),setTimeout(()=>{v(oe=>e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsx("span",{children:'🎤 Quer cantar junto? Ative o "Cante Comigo"!'}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{je(!0),v.dismiss(oe.id)},className:"px-3 py-1 bg-blue-600 text-white rounded text-sm",children:"Sim, vamos cantar!"}),e.jsx("button",{onClick:()=>v.dismiss(oe.id),className:"px-3 py-1 bg-gray-600 text-white rounded text-sm",children:"Não, obrigado"})]})]}),{duration:8e3})},2e3)}catch(ne){console.error("Erro após pagamento:",ne),v.error("Erro ao processar pagamento",{duration:4e3,icon:"❌"})}finally{tt(null)}},[ke,te,r,Q,o,a,we]),O=d.useCallback(async()=>{if(!Ve.trim()){v.error("Por favor, digite seu nome");return}if(!r){v.error("ID do restaurante não encontrado");return}try{const y=await gr.createSession(r,s||void 0,Ve);Ee(y),ze(!1);const E=`clientProfile_${y.id}`,R=localStorage.getItem(E);if(R){const q=JSON.parse(R);q.name=Ve,localStorage.setItem(E,JSON.stringify(q))}else localStorage.setItem(E,JSON.stringify({name:Ve,avatar:"",joinedAt:new Date().toISOString(),level:1,experience:0,nextLevelExp:100,title:"Novo Ouvinte",preferences:{favoriteGenres:[],notifications:!0,autoShare:!1}}));v.success(`Bem-vindo, ${Ve}! 🎵`,{duration:4e3,icon:"👋"})}catch(y){console.error("Erro ao salvar nome:",y),v.error("Erro ao salvar seu nome")}},[Ve,r,s]),M=d.useCallback(()=>{const y=()=>{T(),_(),v.success("Fila atualizada pela votação!",{icon:"🔄"})},E=D=>{var F;$(D.suggestion),v(`🎵 Tocando agora: ${D.suggestion.title}`,{duration:5e3,icon:"🎧"});try{r&&((F=D.suggestion)!=null&&F.youtubeVideoId)&&(Dt.markSongInCooldown(r,D.suggestion.youtubeVideoId),u(Ne=>Ne.map(We=>We.youtubeVideoId===D.suggestion.youtubeVideoId?{...We,isInCooldown:!0}:We)))}catch{}},R=D=>{try{const F=D==null?void 0:D.video;if(!(F!=null&&F.youtubeVideoId))return;$(Ne=>(Ne==null?void 0:Ne.youtubeVideoId)===F.youtubeVideoId?Ne:{id:F.suggestionId||F.youtubeVideoId,title:F.title,artist:F.artist,youtubeVideoId:F.youtubeVideoId,thumbnailUrl:F.thumbnailUrl,duration:F.duration,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString()}),v(`🎯 Próxima selecionada: ${F.title}`,{icon:"⏭️"})}catch{}},q=D=>{try{if(D!=null&&D.queue){const F=Array.isArray(D.queue)?[...D.queue].sort((Ne,We)=>{const St=typeof Ne.position=="number"&&Ne.position>0,Ot=typeof We.position=="number"&&We.position>0;if(St&&Ot)return Ne.position-We.position;if(St)return-1;if(Ot)return 1;if(Ne.isPaid!==We.isPaid)return Ne.isPaid?-1:1;const bs=Ne.addedAt?new Date(Ne.addedAt).getTime():0,vs=We.addedAt?new Date(We.addedAt).getTime():0;return bs-vs}):[];z(F)}D!=null&&D.currentlyPlaying&&$(D.currentlyPlaying)}catch(F){console.warn("Erro ao processar atualização da fila:",F)}T(),_()},ne=D=>{try{const F=D!=null&&D.timestamp?new Date(D.timestamp).getTime():Date.now();if(!Number.isFinite(F)||F<=xt.current)return;xt.current=F}catch{}},L=D=>{var bs,vs,ua,ha,ma,pa,xa,fa,ga,ya,ba,va;const F=D&&typeof D=="object"&&"data"in D?D.data:D,Ne=((bs=F==null?void 0:F.payment)==null?void 0:bs.message)??((vs=F==null?void 0:F.payment)==null?void 0:vs.clientMessage)??((ua=F==null?void 0:F.suggestion)==null?void 0:ua.clientMessage)??(F==null?void 0:F.message)??null,We=((ha=F==null?void 0:F.payment)==null?void 0:ha.clientName)??(F==null?void 0:F.clientName)??((ma=F==null?void 0:F.suggestion)==null?void 0:ma.clientName)??null,St=((pa=F==null?void 0:F.payment)==null?void 0:pa.tableNumber)??(F==null?void 0:F.tableNumber)??((xa=F==null?void 0:F.suggestion)==null?void 0:xa.tableNumber)??null,Ot={id:`${Date.now()}_${Math.random()}`,at:(F==null?void 0:F.timestamp)||(D==null?void 0:D.timestamp)||new Date().toISOString(),amount:Number((fa=F==null?void 0:F.payment)==null?void 0:fa.amount)||0,voteWeight:Number((ga=F==null?void 0:F.payment)==null?void 0:ga.voteWeight)||0,title:(ya=F==null?void 0:F.suggestion)==null?void 0:ya.title,artist:(ba=F==null?void 0:F.suggestion)==null?void 0:ba.artist,youtubeVideoId:(va=F==null?void 0:F.suggestion)==null?void 0:va.youtubeVideoId,clientName:We||void 0,tableNumber:typeof St=="number"?St:void 0,message:typeof Ne=="string"?Ne:void 0};X(or=>{const ws=Date.now(),lr=or.filter(js=>{const ai=js.at?new Date(js.at).getTime():ws;return ws-ai<ye});return[Ot,...lr].slice(0,50)});try{const or=Ot.at?new Date(Ot.at).getTime():Date.now(),ws=Math.max(0,ye-(Date.now()-or));setTimeout(()=>{X(lr=>lr.filter(js=>js.id!==Ot.id))},ws)}catch{}},oe=D=>{G()},ve=setInterval(()=>{const D=Date.now();X(F=>F.filter(Ne=>{const We=Ne.at?new Date(Ne.at).getTime():D;return D-We<ye}))},15e3);return at("now-playing",E),at("playlistReordered",y),at("reorderSelected",R),at("queue-update",q),at("ranking-snapshot",ne),at("superVoteReceived",L),at("playlistReordered",oe),()=>{rt("now-playing",E),rt("playlistReordered",y),rt("reorderSelected",R),rt("queue-update",q),rt("ranking-snapshot",ne),rt("superVoteReceived",L),rt("playlistReordered",oe),clearInterval(ve)}},[at,rt,T,_,r]);return d.useEffect(()=>{if(!r){v.error("ID do restaurante não encontrado");return}(async()=>{K(!0);try{await Promise.all([me(),C(),T(),_(),Q(),G()]),ft(r)}catch(R){console.error("Erro na inicialização:",R),v.error("Erro ao carregar dados iniciais")}finally{K(!1)}})();const E=M();return()=>{E()}},[r]),d.useEffect(()=>{r&&(w(1),Q(1))},[m,r]),Ue&&!i?e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-white",children:[e.jsx(Xe,{className:"w-12 h-12 animate-spin mx-auto mb-4","aria-hidden":"true"}),e.jsx("h2",{className:"text-xl font-semibold",children:"Carregando..."}),e.jsx("p",{className:"text-sm text-purple-300 mt-2",children:"Conectando ao restaurante"}),e.jsx("button",{onClick:()=>{C(),T(),_(),Q()},className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors","aria-label":"Tentar novamente",children:"Tentar Novamente"})]})}):e.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white",children:[e.jsx("header",{className:"bg-black/30 backdrop-blur-md border-b border-white/10 p-4 sm:p-6 sticky top-0 z-10",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsxs("div",{className:"flex items-center justify-center gap-3 mb-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center",children:e.jsx(Ce,{className:"w-6 h-6","aria-hidden":"true"})}),e.jsx("div",{children:e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold",children:i==null?void 0:i.name})})]}),e.jsx("p",{className:"text-sm sm:text-base text-purple-200 mb-4 px-2 text-center",children:"Escolha e vote nas músicas que vão animar seu momento!"}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-3 sm:gap-4 text-sm sm:text-base",children:[s&&e.jsxs("div",{className:"flex items-center gap-1 px-2 py-1 bg-white/10 rounded-full",title:`Mesa ${s}`,children:[e.jsx("span",{className:"text-lg",children:"🪑"}),e.jsxs("span",{className:"font-medium",children:["Mesa ",s]})]}),e.jsxs("div",{className:"flex items-center gap-1 px-2 py-1 bg-white/10 rounded-full",title:"Votos realizados",children:[e.jsx(As,{className:"w-4 h-4 text-blue-400","aria-hidden":"true"}),e.jsx("span",{className:"font-medium",children:xe.votesCount})]}),e.jsxs("button",{onClick:()=>Se(!0),className:"flex items-center gap-1 px-3 py-1.5 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 hover:scale-105 font-medium","aria-label":"Ver perfil",children:[e.jsx(Wt,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Perfil"})]}),e.jsxs("button",{onClick:()=>dt(!0),className:"flex items-center gap-1 px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full hover:from-purple-600 hover:to-pink-600 transition-transform hover:scale-105","aria-label":"Ver ranking",children:[e.jsx(vr,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Ranking"})]})]}),xe.badges.length>0&&e.jsxs("div",{className:"flex flex-wrap justify-center gap-2 mt-3",children:[xe.badges.slice(0,3).map((y,E)=>e.jsx("span",{className:"px-2 py-1 bg-white/10 rounded-full text-xs border border-white/20",title:y,children:y},E)),xe.badges.length>3&&e.jsxs("span",{className:"px-2 py-1 bg-white/10 rounded-full text-xs text-purple-200 border border-white/20",title:`${xe.badges.length-3} badges adicionais`,children:["+",xe.badges.length-3," mais"]})]}),e.jsxs("div",{className:"mt-4 pt-3 border-t border-white/10 flex items-center justify-center gap-2",children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${Te?"bg-green-400 animate-pulse":"bg-red-400"}`}),e.jsx("span",{className:"text-xs sm:text-sm text-purple-200",children:Te?"Conectado ao restaurante":"Reconectando..."})]})]})}),e.jsxs("main",{className:"max-w-4xl mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 space-y-4 sm:space-y-6",children:[N&&e.jsxs(B.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-md rounded-xl p-4 sm:p-6 border border-white/20","aria-labelledby":"now-playing",children:[e.jsxs("h2",{id:"now-playing",className:"flex items-center gap-2 text-lg sm:text-xl font-bold mb-3 sm:mb-4",children:[e.jsx(ot,{className:"w-5 h-5 text-green-400","aria-hidden":"true"}),"Tocando Agora"]}),e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4",children:[e.jsx("img",{src:N.thumbnailUrl||`https://img.youtube.com/vi/${N.youtubeVideoId}/mqdefault.jpg`,alt:`Capa de ${N.title}`,className:"w-14 h-14 sm:w-16 sm:h-16 rounded-lg object-cover flex-shrink-0",loading:"lazy"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-sm sm:text-base font-semibold truncate",children:N.title}),e.jsx("p",{className:"text-xs sm:text-sm text-purple-200 truncate",children:N.artist})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-xl font-bold text-green-400",children:[N.score>0?"+":"",N.score]}),e.jsx("div",{className:"text-xs text-purple-200",children:"votos"})]})]})]}),e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"search",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{id:"search",className:"flex items-center gap-2 text-xl font-bold",children:[e.jsx(qs,{className:"w-5 h-5","aria-hidden":"true"}),"Buscar Músicas"]}),e.jsx("button",{onClick:()=>st(!Me),className:"px-3 py-2 bg-white/10 rounded-lg border border-white/20 text-xs hover:bg-white/20 inline-flex items-center gap-1","aria-label":"Alternar seção de busca",children:Me?e.jsxs(e.Fragment,{children:[e.jsx(cr,{className:"w-4 h-4"}),e.jsx("span",{children:"Expandir"})]}):e.jsxs(e.Fragment,{children:[e.jsx(as,{className:"w-4 h-4"}),e.jsx("span",{children:"Recolher"})]})})]}),!Me&&e.jsxs("div",{className:"flex gap-3 mb-4",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx("input",{type:"text",value:o,onChange:y=>l(y.target.value),onKeyPress:y=>y.key==="Enter"&&ue(),placeholder:"Busque por música ou artista...",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500","aria-label":"Buscar músicas por título ou artista"}),o&&e.jsx("button",{onClick:()=>l(""),className:"absolute right-12 top-1/2 -translate-y-1/2 text-white/60 hover:text-white","aria-label":"Limpar busca",children:e.jsx(jt,{className:"w-4 h-4"})})]}),e.jsxs("button",{onClick:ue,disabled:p||!o.trim(),className:"px-4 sm:px-6 py-2.5 sm:py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 whitespace-nowrap text-sm sm:text-base font-medium","aria-label":"Buscar músicas",children:[p?e.jsx(Xe,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(qs,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:"Buscar"})]})]})]}),e.jsxs("section",{className:"bg-gradient-to-br from-purple-500/10 via-blue-500/10 to-indigo-500/10 backdrop-blur-md rounded-xl p-4 sm:p-6 border border-purple-400/30 shadow-lg","aria-labelledby":"karaoke-section",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4 sm:mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[e.jsx("div",{className:"p-1.5 sm:p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg",children:e.jsx(Bs,{className:"w-5 h-5 sm:w-6 sm:h-6 text-white","aria-hidden":"true"})}),e.jsxs("div",{children:[e.jsx("h2",{id:"karaoke-section",className:"text-lg sm:text-xl font-bold text-white",children:"Cante Comigo (Karaokê)"}),e.jsx("p",{className:"text-purple-200 text-xs sm:text-sm",children:"Letras sincronizadas em tempo real"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1 px-2 py-1 bg-green-500/20 rounded-full border border-green-400/30",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-green-300 text-xs font-medium",children:"Melhorado"})]}),e.jsx("button",{onClick:()=>ut(!vt),className:"px-3 py-2 bg-white/10 rounded-lg border border-white/20 text-xs hover:bg-white/20 inline-flex items-center gap-1 transition-colors","aria-label":"Alternar seção de karaokê",children:vt?e.jsxs(e.Fragment,{children:[e.jsx(cr,{className:"w-4 h-4"}),e.jsx("span",{children:"Expandir"})]}):e.jsxs(e.Fragment,{children:[e.jsx(as,{className:"w-4 h-4"}),e.jsx("span",{children:"Recolher"})]})})]})]}),!vt&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-white/5 rounded-lg p-4 border border-white/10",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex-1 min-w-0",children:N?e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:N.thumbnailUrl||`https://img.youtube.com/vi/${N.youtubeVideoId}/mqdefault.jpg`,alt:`Capa de ${N.title}`,className:"w-16 h-16 rounded-lg object-cover shadow-md",loading:"lazy"}),e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"font-semibold truncate text-white",children:N.title}),e.jsx("p",{className:"text-purple-200 text-sm truncate",children:N.artist}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:e.jsxs("div",{className:"flex items-center gap-1 text-xs text-green-300",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),e.jsx("span",{children:"Tocando agora"})]})})]})]}):e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-16 h-16 bg-white/10 rounded-lg flex items-center justify-center",children:e.jsx(Ce,{className:"w-6 h-6 text-white/50"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-white/70 font-medium",children:"Aguardando música"}),e.jsx("p",{className:"text-purple-200 text-sm",children:"Nenhuma música tocando no momento"})]})]})}),e.jsxs("div",{className:"flex flex-col gap-2",children:[e.jsxs("button",{onClick:()=>{console.log("🎤 Botão karaokê clicado",{currentlyPlaying:N}),ct(N?{id:N.id,title:N.title,artist:N.artist,thumbnailUrl:N.thumbnailUrl,duration:N.duration,youtubeVideoId:N.youtubeVideoId,status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString()}:{id:"demo-karaoke",title:"Música de Demonstração",artist:"Artista Demo",thumbnailUrl:"https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",duration:180,youtubeVideoId:"dQw4w9WgXcQ",status:"approved",upvotes:0,downvotes:0,score:0,createdAt:new Date().toISOString()}),je(!0),v.success("🎤 Abrindo karaokê!")},className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 inline-flex items-center gap-2 font-medium transition-all transform hover:scale-105","aria-label":"Abrir karaokê",children:[e.jsx(Bs,{className:"w-5 h-5","aria-hidden":"true"}),e.jsx("span",{children:N?"Iniciar Karaokê":"Demo Karaokê"})]}),!N&&e.jsx("p",{className:"text-xs text-purple-300 text-center",children:"Clique para testar o karaokê com música demo"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:[e.jsxs("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:e.jsx(Ce,{className:"w-4 h-4 text-blue-400"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Sincronização"})]}),e.jsx("p",{className:"text-xs text-purple-200",children:"Letras sincronizadas em tempo real com a música"})]}),e.jsxs("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center",children:e.jsx(Yt,{className:"w-4 h-4 text-green-400"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Personalização"})]}),e.jsx("p",{className:"text-xs text-purple-200",children:"Ajuste tamanho da fonte e cores do destaque"})]}),e.jsxs("div",{className:"bg-white/5 rounded-lg p-3 border border-white/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:e.jsx(sa,{className:"w-4 h-4 text-purple-400"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Tela Cheia"})]}),e.jsx("p",{className:"text-xs text-purple-200",children:"Modo fullscreen para melhor experiência"})]})]})]})]}),e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"playlist",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{id:"playlist",className:"flex items-center gap-2 text-xl font-bold",children:[e.jsx(Ce,{className:"w-5 h-5","aria-hidden":"true"}),o?"Resultados da Busca":"Playlist do Restaurante"]}),Z>0&&e.jsxs("div",{className:"text-sm text-purple-200",children:[h.length," de ",Z," músicas"]})]}),b&&h.length===0?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(Xe,{className:"w-8 h-8 text-purple-400 animate-spin","aria-hidden":"true"}),e.jsx("span",{className:"ml-3 text-purple-200",children:"Carregando músicas..."})]}):h.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(_i,{className:"w-12 h-12 text-purple-400 mx-auto mb-3","aria-hidden":"true"}),e.jsx("p",{className:"text-purple-200",children:"Nenhuma música encontrada."}),e.jsx("p",{className:"text-sm text-purple-300",children:"Use a busca ou filtros para encontrar músicas."})]}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:h.map(y=>e.jsxs(B.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},whileHover:{scale:y.isInCooldown?1:1.03},className:`bg-white/5 rounded-lg p-4 border border-white/10 hover:bg-white/10 transition-all ${y.isInCooldown?"opacity-60 border-orange-400/30 bg-orange-500/5":""}`,role:"article","aria-labelledby":`song-${y.id}`,children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:y.thumbnailUrl,alt:`Capa de ${y.title}`,className:`w-full h-28 sm:h-32 object-cover rounded-lg mb-2 sm:mb-3 ${y.isInCooldown?"grayscale":""}`,loading:"lazy"}),y.isInCooldown&&e.jsx("div",{className:"absolute inset-0 bg-orange-500/20 rounded-lg flex items-center justify-center",children:e.jsx("div",{className:"bg-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold",children:"⏰ Cooldown"})})]}),e.jsx("h3",{id:`song-${y.id}`,className:`font-semibold text-xs sm:text-sm truncate ${y.isInCooldown?"text-orange-300":""}`,children:y.title}),e.jsx("p",{className:`text-xs truncate mb-1 sm:mb-2 ${y.isInCooldown?"text-orange-400":"text-purple-200"}`,children:y.artist}),y.isInCooldown&&y.cooldownTimeLeft&&e.jsxs("p",{className:"text-orange-400 text-xs mb-2 font-medium",children:["🚫 Disponível em ",Math.ceil(y.cooldownTimeLeft/60)," min"]}),e.jsxs("div",{className:"flex justify-between items-center text-xs text-purple-300 mb-2 sm:mb-3",children:[e.jsx("span",{className:"text-xs sm:text-sm",children:y.formattedDuration}),e.jsxs("button",{onClick:()=>de(y),disabled:Ue,className:"inline-flex items-center gap-1 text-pink-300 hover:text-pink-400 disabled:opacity-50 transition-colors p-1","aria-label":`Curtir ${y.title}`,title:"Curtir",children:[e.jsx(Jr,{className:"w-3 h-3 sm:w-4 sm:h-4","aria-hidden":"true"}),e.jsx("span",{className:"sr-only",children:"Curtir"})]})]}),e.jsx("div",{className:"flex gap-1 sm:gap-2",children:y.isInCooldown?e.jsx("div",{className:"px-3 py-2 bg-orange-500/20 border border-orange-400/30 rounded-lg flex items-center justify-center gap-1 text-xs text-orange-300",children:e.jsx("span",{children:"⏰ Em Cooldown"})}):e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>ae(y),disabled:Ue,className:"flex-1 px-2 sm:px-3 py-1.5 sm:py-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs sm:text-sm font-medium","aria-label":`Registrar voto para ${y.title}`,children:[e.jsx(Ce,{className:"w-3 h-3 sm:w-4 sm:h-4","aria-hidden":"true"}),e.jsx("span",{className:"hidden xs:inline",children:"Voto"}),e.jsx("span",{className:"xs:hidden",children:"👍"})]}),e.jsxs("button",{onClick:()=>V(y),disabled:Ue,className:"flex-1 px-2 sm:px-3 py-1.5 sm:py-2 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 flex items-center justify-center gap-1 text-xs sm:text-sm font-medium","aria-label":`Adicionar ${y.title} com SuperVoto`,children:[e.jsx(zt,{className:"w-3 h-3 sm:w-4 sm:h-4","aria-hidden":"true"}),e.jsx("span",{className:"hidden xs:inline",children:"SuperVoto"}),e.jsx("span",{className:"xs:hidden",children:"💳"})]})]})})]},y.id))}),S&&e.jsx("div",{className:"text-center",children:e.jsxs("button",{onClick:()=>Q(g+1,!0),disabled:b,className:"px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 flex items-center gap-2 mx-auto","aria-label":"Carregar mais músicas da playlist",children:[b?e.jsx(Xe,{className:"w-4 h-4 animate-spin","aria-hidden":"true"}):e.jsx(Ce,{className:"w-4 h-4","aria-hidden":"true"}),e.jsx("span",{children:b?"Carregando...":"Carregar Mais Músicas"})]})}),!S&&Z>24&&e.jsxs("p",{className:"text-center text-sm text-purple-300",children:["🎵 Todas as ",Z," músicas foram carregadas!"]})]})]}),W&&e.jsxs("section",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20","aria-labelledby":"playback-queue",children:[e.jsx("div",{className:"p-6 bg-gradient-to-r from-purple-500/20 to-indigo-500/20 cursor-pointer hover:from-purple-500/30 hover:to-indigo-500/30 transition-all rounded-lg mb-4",onClick:()=>re(!H),children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center shadow-lg",children:e.jsx(Ce,{className:"w-6 h-6 text-white"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Sequência (Fila)"}),e.jsx("div",{className:"flex items-center gap-4 mt-1 text-sm",children:e.jsxs("span",{className:"text-purple-200",children:[A.length," música",A.length!==1?"s":""]})})]})]}),e.jsx("div",{className:"flex items-center space-x-3",children:e.jsx("button",{className:"p-2 bg-white/10 rounded-lg text-purple-300 hover:text-white hover:bg-white/20 transition-all",children:H?e.jsx(cr,{className:"w-5 h-5"}):e.jsx(as,{className:"w-5 h-5"})})})]})}),e.jsx(et,{children:!H&&e.jsx(B.div,{initial:{height:0},animate:{height:"auto"},exit:{height:0},className:"overflow-hidden",children:A.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400 text-lg font-medium",children:"Fila vazia"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Seja o primeiro a sugerir uma música!"})]}):e.jsx("div",{className:"p-4 space-y-3 max-h-96 overflow-y-auto",children:A.map((y,E)=>e.jsx(B.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:E*.05},className:"p-4 rounded-lg border transition-all hover:shadow-lg bg-white/5 border-white/10 hover:bg-white/10",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsxs("div",{className:`w-10 h-10 rounded-full flex items-center justify-center font-bold ${y.isPaid?"bg-yellow-500 text-black":"bg-purple-500/30 text-purple-300"}`,children:["#",y.position]})}),e.jsx("img",{src:y.thumbnailUrl||`https://img.youtube.com/vi/${y.youtubeVideoId}/mqdefault.jpg`,alt:y.title,className:"w-16 h-12 rounded-lg object-cover shadow-md"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-white font-semibold truncate",children:y.title}),e.jsx("p",{className:"text-gray-300 text-sm truncate",children:y.artist}),e.jsxs("div",{className:"flex items-center gap-3 mt-2 text-xs",children:[e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(Pt,{className:"w-3 h-3"}),xu(y.duration)]}),y.isPaid&&y.paymentAmount&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(zt,{className:"w-3 h-3"}),"R$ ",Number(y.paymentAmount).toFixed(2)]})]})]})]})},y.id))})})})]}),e.jsxs("footer",{className:"text-center py-6",children:[e.jsx("p",{className:"text-sm text-purple-300",children:"🎵 Powered by Sistema de Playlist Interativa"}),e.jsx("p",{className:"text-xs text-purple-400 mt-1",children:"Sugestões são moderadas e podem levar alguns minutos para aparecer na fila"})]})]}),e.jsxs(et,{children:[ie&&e.jsx(B.div,{initial:{opacity:0,scale:.5,y:50},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:-50},className:"fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",role:"alert","aria-live":"polite",children:e.jsxs("div",{className:"bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🎉"}),e.jsx("h3",{className:"text-xl font-bold mb-1",children:"LEVEL UP!"}),e.jsxs("p",{className:"text-sm",children:["Você alcançou o nível ",xe.level,"!"]})]})}),fe&&e.jsx(B.div,{initial:{opacity:0,scale:.5,y:50},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.5,y:-50},className:"fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-50",role:"alert","aria-live":"polite",children:e.jsxs("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-6 rounded-xl shadow-2xl text-center",children:[e.jsx("div",{className:"text-4xl mb-2",children:"🏆"}),e.jsx("h3",{className:"text-xl font-bold mb-1",children:"NOVA CONQUISTA!"}),e.jsx("p",{className:"text-sm",children:fe})]})}),lt&&e.jsx(B.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",role:"dialog","aria-modal":"true","aria-labelledby":"name-modal-title",children:e.jsxs(B.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"bg-white dark:bg-gray-900 rounded-xl p-6 max-w-md w-full shadow-2xl",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(Wt,{className:"w-12 h-12 text-purple-500 mx-auto mb-3","aria-hidden":"true"}),e.jsx("h3",{id:"name-modal-title",className:"text-xl font-bold text-gray-800 dark:text-white mb-2",children:"Bem-vindo! 🎵"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Como podemos te chamar? (Opcional)"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("input",{type:"text",value:Ve,onChange:y=>Qe(y.target.value),placeholder:"Digite seu nome...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100",onKeyPress:y=>y.key==="Enter"&&O(),autoFocus:!0,"aria-label":"Digite seu nome"}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:O,disabled:!Ve.trim(),className:"flex-1 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50","aria-label":"Confirmar nome",children:"Continuar"}),e.jsx("button",{onClick:()=>ze(!1),className:"flex-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors","aria-label":"Pular entrada de nome",children:"Pular"})]})]})]})})]}),$e&&ke&&e.jsx(Rd,{isOpen:$e,onClose:()=>{qe(!1),tt(null)},suggestion:{id:ke.id,title:ke.title,artist:ke.artist,thumbnailUrl:ke.thumbnailUrl,duration:ke.duration,youtubeVideoId:ke.youtubeVideoId},sessionId:(te==null?void 0:te.sessionToken)||"",restaurantId:r||"",onPaymentSuccess:j}),Pe&&e.jsx(hu,{isOpen:Be,onClose:()=>{je(!1),ct(null)},suggestion:Pe,sessionId:(te==null?void 0:te.id)||"",onVoteRequest:()=>v.success("Votação solicitada! 🗳️",{duration:4e3})}),Ae&&te&&e.jsx(mu,{isOpen:Ae,onClose:()=>Se(!1),restaurantId:r||"",sessionId:te.id}),e.jsx(pu,{restaurantId:r||"",currentTableNumber:s||void 0,isVisible:Ye,onClose:()=>dt(!1)})]})},Xa="reorder-timer",Is=5*60*1e3,yu=(r,t)=>{const[s,a]=d.useState(null),[n,i]=d.useState(Date.now()),c=d.useRef(0);d.useEffect(()=>{const p=`${Xa}-${r}`,f=localStorage.getItem(p);if(f)try{const m=JSON.parse(f),x=Date.now();if(m.nextReorderAt&&m.nextReorderAt>x)a(m.nextReorderAt);else{const g=x+Is;a(g),localStorage.setItem(p,JSON.stringify({nextReorderAt:g,lastUpdated:x}))}}catch(m){console.warn("Erro ao carregar timer do localStorage:",m);const g=Date.now()+Is;a(g)}else{const x=Date.now()+Is;a(x)}},[r]),d.useEffect(()=>{if(s&&r){const p=`${Xa}-${r}`,f={nextReorderAt:s,lastUpdated:Date.now()};localStorage.setItem(p,JSON.stringify(f))}},[s,r]),d.useEffect(()=>{const p=setInterval(()=>i(Date.now()),1e3);return()=>clearInterval(p)},[]);const o=p=>{const f=Date.now();(!s||p>s+2e3)&&(a(p),c.current=f)},l=()=>{const f=Date.now()+Is;a(f),t&&setTimeout(t,100)},h=(()=>{if(!s)return"05:00";const p=s-n;if(p<=0)return setTimeout(()=>{l()},100),"00:00";const f=Math.floor(p/6e4),m=Math.floor(p%6e4/1e3);return`${String(f).padStart(2,"0")}:${String(m).padStart(2,"0")}`})(),u=s?s-n<=15e3:!1;return{nextReorderAt:s,countdown:h,isGracePeriod:u,updateTimer:o,resetTimer:l,lastSnapshotRef:c}},yr=r=>{const t=Math.floor(r/60),s=r%60;return`${t}:${String(s).padStart(2,"0")}`},Za=()=>{const{restaurantId:r}=At(),{on:t,off:s,joinRestaurant:a,status:n}=qt(),i=d.useCallback(()=>{console.log("⏰ Timer expirou - executando refresh automático"),refetchData(),$()},[]),{countdown:c,isGracePeriod:o,updateTimer:l,resetTimer:h,lastSnapshotRef:u}=yu(r||"",i),[p,f]=d.useState(null),[m,x]=d.useState([]),[g,w]=d.useState([]),b=2*60*1e3,[k,S]=d.useState([]),[J,Z]=d.useState(!1),[se,N]=d.useState(null);d.useRef(0);const $=async()=>{if(!r)return;Z(!0),N(null);const P=Kt();let H=0;const re=3;for(;H<re;)try{const A=await fetch(ge(`/restaurants/${r}/playlist`),{headers:P});if(!A.ok){if((A.status===502||A.status===500)&&H<re-1){H++,await new Promise(X=>setTimeout(X,500*H));continue}throw new Error(`HTTP ${A.status}`)}const z=await A.json(),le=Array.isArray(z==null?void 0:z.results)?z.results:[];le.sort((X,U)=>{if(X.playlistId!==U.playlistId)return(X.playlistId||"").localeCompare(U.playlistId||"");const I=X.position&&X.position>0?X.position:Number.MAX_SAFE_INTEGER,ee=U.position&&U.position>0?U.position:Number.MAX_SAFE_INTEGER;return I-ee}),S(le),Z(!1);return}catch(A){if(H<re-1){H++,await new Promise(z=>setTimeout(z,500*H));continue}N((A==null?void 0:A.message)||"Falha ao carregar playlist"),Z(!1);return}},W=async()=>{if(r){Z(!0);try{const P=Kt(),[H]=await Promise.all([fetch(ge(`/playback-queue/${r}`),{headers:P})]);if(H.ok){const re=await H.json();x(Array.isArray(re.queue)?re.queue:[]),f(re.currentlyPlaying||null)}await $()}catch{}Z(!1)}};return d.useEffect(()=>{if(W(),r){try{localStorage.setItem("currentRestaurantId",r)}catch{}a(r)}},[r,a]),d.useEffect(()=>{const P=U=>{Array.isArray(U==null?void 0:U.queue)&&x(U.queue),U!=null&&U.currentlyPlaying&&f(U.currentlyPlaying)},H=U=>{if(U!=null&&U.deadlineAt){const I=new Date(U.deadlineAt).getTime();l(I)}},re=U=>{if(U!=null&&U.suggestion){const I=U.suggestion;f({id:I.id||I.youtubeVideoId,suggestionId:I.youtubeVideoId,youtubeVideoId:I.youtubeVideoId,title:I.title,artist:I.artist,duration:I.duration||0,thumbnailUrl:I.thumbnailUrl,isPaid:!!I.isPaid,paymentAmount:I.paymentAmount,position:0})}},A=U=>{var Ee,xe,Ze,Ve,Qe,Ue,K,ie,Re,fe,Fe,lt;const I=U&&typeof U=="object"&&"data"in U?U.data:U;console.info("[Cover] superVoteReceived:",U);const ee=((Ee=I==null?void 0:I.payment)==null?void 0:Ee.message)??((xe=I==null?void 0:I.payment)==null?void 0:xe.clientMessage)??((Ze=I==null?void 0:I.suggestion)==null?void 0:Ze.clientMessage)??(I==null?void 0:I.message)??null,Y=((Ve=I==null?void 0:I.payment)==null?void 0:Ve.clientName)??(I==null?void 0:I.clientName)??((Qe=I==null?void 0:I.suggestion)==null?void 0:Qe.clientName)??null,ye=((Ue=I==null?void 0:I.payment)==null?void 0:Ue.tableNumber)??(I==null?void 0:I.tableNumber)??((K=I==null?void 0:I.suggestion)==null?void 0:K.tableNumber)??null,te={id:`${Date.now()}_${Math.random()}`,at:(I==null?void 0:I.timestamp)||(U==null?void 0:U.timestamp)||new Date().toISOString(),amount:Number((ie=I==null?void 0:I.payment)==null?void 0:ie.amount)||0,voteWeight:Number((Re=I==null?void 0:I.payment)==null?void 0:Re.voteWeight)||0,title:(fe=I==null?void 0:I.suggestion)==null?void 0:fe.title,artist:(Fe=I==null?void 0:I.suggestion)==null?void 0:Fe.artist,youtubeVideoId:(lt=I==null?void 0:I.suggestion)==null?void 0:lt.youtubeVideoId,clientName:Y||void 0,tableNumber:typeof ye=="number"?ye:void 0,message:typeof ee=="string"?ee:void 0};w(ze=>{const $e=Date.now(),qe=ze.filter(tt=>{const Be=tt.at?new Date(tt.at).getTime():$e;return $e-Be<b}),ke=[te,...qe].slice(0,50);return console.info("[Cover] notes atualizado:",ke[0]),ke});try{const ze=te.at?new Date(te.at).getTime():Date.now(),$e=Math.max(0,b-(Date.now()-ze));setTimeout(()=>{w(qe=>qe.filter(ke=>ke.id!==te.id))},$e)}catch{}},z=U=>{var I;try{let ee=null;const Y=(I=U==null?void 0:U.adminDetails)==null?void 0:I.nextReorderTime;Y?(ee=new Date(Y).getTime(),l(ee)):h()}catch{h()}$()},le=U=>{console.log("🎵 Música terminou (WebSocket - CoverPage):",U),setTimeout(()=>{refetchData(),$()},500)};t("queue-update",P),t("now-playing",re),t("superVoteReceived",A),t("song-ended",le);const X=setInterval(()=>{const U=Date.now();w(I=>I.filter(ee=>{const Y=ee.at?new Date(ee.at).getTime():U;return U-Y<b}))},15e3);return t("playlistReordered",z),t("ranking-snapshot",H),()=>{s("queue-update",P),s("now-playing",re),s("superVoteReceived",A),s("song-ended",le),clearInterval(X),s("playlistReordered",z),s("ranking-snapshot",H)}},[r]),d.useMemo(()=>[],[p,m]),e.jsx("div",{className:"min-h-screen px-4 py-6 md:px-8 md:py-10 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsxs(B.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between flex-wrap gap-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center",children:e.jsx(Ce,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold bg-gradient-to-r from-pink-400 to-violet-400 bg-clip-text text-transparent",children:"Painel do Músico"}),e.jsx("p",{className:"text-purple-200 text-sm",children:"Couvert Musical Interativo"})]})]}),e.jsx("p",{className:"text-purple-300 text-sm max-w-2xl",children:"Acompanhe o que está tocando, a fila e os recados dos fãs em tempo real. Sistema de SuperVotos ativo."})]}),e.jsxs("div",{className:"flex gap-4 text-sm items-center",children:[e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-green-400",children:m.length}),e.jsx("div",{className:"text-purple-200 text-xs",children:"Na fila"})]}),e.jsxs("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2 text-center",children:[e.jsx("div",{className:"text-lg font-bold text-yellow-400",children:g.length}),e.jsx("div",{className:"text-purple-200 text-xs",children:"SuperVotos"})]})]})]}),e.jsxs(B.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},className:`mt-4 inline-flex items-center gap-3 text-sm backdrop-blur-sm border rounded-lg px-4 py-2 ${o?"bg-gradient-to-r from-orange-500/20 to-red-500/20 border-orange-400/30":"bg-gradient-to-r from-indigo-500/20 to-purple-500/20 border-indigo-400/30"}`,children:[e.jsx(Ti,{className:`w-5 h-5 ${o?"text-orange-300 animate-bounce":"text-indigo-300 animate-pulse"}`}),e.jsx("span",{className:o?"text-orange-200":"text-indigo-200",children:o?"Janela de graça! Últimos votos":"Próxima reordenação automática em"}),e.jsx("span",{className:`font-mono font-bold text-xl px-2 py-1 rounded ${o?"text-orange-100 bg-orange-500/30":"text-indigo-100 bg-indigo-500/30"}`,children:c})]})]}),n!=="connected"&&e.jsx("div",{className:"mb-4 p-3 bg-yellow-500/20 border border-yellow-400/30 rounded-lg text-yellow-200 text-sm",children:n==="connecting"?"Conectando ao servidor em tempo real...":"Desconectado do servidor. Tentando reconectar..."}),e.jsx(et,{children:p&&e.jsxs(B.section,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.95},className:"mb-6 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-xl p-6",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(er,{className:"w-5 h-5 text-green-400 animate-pulse"}),e.jsx("h2",{className:"text-lg font-semibold text-green-300",children:"Tocando Agora"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:p.thumbnailUrl||`https://img.youtube.com/vi/${p.youtubeVideoId}/mqdefault.jpg`,alt:p.title,className:"w-20 h-16 rounded-lg object-cover shadow-lg"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-2xl md:text-3xl font-bold text-white truncate",children:p.title}),e.jsx("p",{className:"text-lg md:text-xl text-green-200 truncate",children:p.artist}),e.jsxs("div",{className:"flex items-center gap-4 mt-2 text-base",children:[e.jsxs("span",{className:"flex items-center gap-1 text-green-300",children:[e.jsx(Pt,{className:"w-4 h-4"}),yr(p.duration)]}),p.isPaid&&p.paymentAmount&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(zt,{className:"w-4 h-4"}),"SuperVoto R$ ",Number(p.paymentAmount).toFixed(2)]})]})]})]})]})}),e.jsxs("div",{className:"space-y-6 mb-6",children:[e.jsxs(B.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{className:"text-xl font-semibold flex items-center gap-2",children:[e.jsx(ot,{className:"w-5 h-5 text-purple-400"}),"Sequência (Fila)"]}),e.jsxs("div",{className:"text-sm text-purple-300",children:[m.length," música",m.length!==1?"s":""]})]}),e.jsx("div",{className:"max-h-[500px] overflow-y-auto space-y-2",children:m.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ce,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Fila vazia"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Aguardando sugestões dos clientes"})]}):m.map((P,H)=>e.jsx(B.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:H*.05},className:"p-4 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center",children:e.jsxs("span",{className:"text-lg font-bold text-purple-300",children:["#",P.position]})}),e.jsx("img",{src:P.thumbnailUrl||`https://img.youtube.com/vi/${P.youtubeVideoId}/mqdefault.jpg`,alt:P.title,className:"w-16 h-12 rounded-lg object-cover shadow-md"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-lg md:text-xl font-bold text-white truncate",children:P.title}),e.jsx("div",{className:"text-base md:text-lg text-purple-200 truncate",children:P.artist}),e.jsxs("div",{className:"flex items-center gap-3 mt-2 text-sm",children:[e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(Pt,{className:"w-3 h-3"}),yr(P.duration)]}),P.isPaid&&P.paymentAmount&&e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(Sa,{className:"w-3 h-3"}),"R$ ",Number(P.paymentAmount).toFixed(2)]})]})]})]})},P.id))})]}),e.jsxs(B.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h2",{className:"text-xl font-semibold flex items-center gap-2",children:[e.jsx(Ce,{className:"w-5 h-5 text-blue-400"}),"Catálogo Completo"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("span",{className:"text-sm text-purple-300",children:[k.length," música",k.length!==1?"s":""]}),e.jsxs("button",{onClick:$,disabled:J,className:"flex items-center gap-1 text-xs text-blue-300 hover:text-blue-200 transition-colors disabled:opacity-50",children:[e.jsx(Xe,{className:`w-3 h-3 ${J?"animate-spin":""}`}),J?"Carregando...":"Atualizar"]})]})]}),se&&e.jsx("div",{className:"mb-4 p-3 bg-red-500/20 border border-red-400/30 rounded-lg text-red-300 text-sm",children:se}),e.jsx("div",{className:"max-h-[500px] overflow-y-auto space-y-2",children:k.length===0&&!J?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(Ce,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Nenhuma música cadastrada"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Configure a playlist no painel administrativo"})]}):k.map((P,H)=>e.jsx(B.div,{initial:{opacity:0,y:5},animate:{opacity:1,y:0},transition:{delay:H*.02},className:"p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-all duration-200 border border-white/10",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center",children:e.jsxs("span",{className:"text-base font-bold text-blue-300",children:["#",H+1]})}),e.jsx("img",{src:P.thumbnailUrl||`https://img.youtube.com/vi/${P.youtubeVideoId}/mqdefault.jpg`,alt:P.title,className:"w-14 h-10 rounded object-cover shadow-md"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("div",{className:"text-base md:text-lg font-semibold text-white truncate",children:P.title}),e.jsx("div",{className:"text-sm md:text-base text-purple-200 truncate",children:P.artist}),e.jsxs("div",{className:"flex items-center gap-3 mt-1 text-sm",children:[e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(Pt,{className:"w-3 h-3"}),P.formattedDuration||yr(Math.max(0,Math.floor(P.duration||0)))]}),P.isAvailable===!1&&e.jsxs("span",{className:"flex items-center gap-1 text-amber-300 bg-amber-500/20 px-2 py-1 rounded",children:[e.jsx(xs,{className:"w-3 h-3"}),"Indisponível"]})]})]})]})},P.id||P.youtubeVideoId||H))})]}),e.jsxs(B.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden",children:[e.jsx("div",{className:"p-6 border-b border-white/20 bg-gradient-to-r from-emerald-500/10 to-green-500/10",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h2",{className:"text-xl font-semibold flex items-center gap-2",children:[e.jsx(Ca,{className:"w-5 h-5 text-emerald-400"}),"SuperVotos & Recados"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-emerald-400 rounded-full animate-pulse"}),e.jsx("span",{className:"text-xs text-emerald-300",children:"Tempo real"})]})]})}),e.jsx("div",{className:"max-h-[500px] overflow-y-auto",children:g.length===0?e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(Ca,{className:"w-12 h-12 text-gray-500 mx-auto mb-3"}),e.jsx("p",{className:"text-gray-400",children:"Sem SuperVotos ainda"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Mensagens e pagamentos aparecem aqui quando clientes fazem SuperVotos"})]}):e.jsx("div",{className:"p-4 space-y-4",children:e.jsx(et,{children:g.map((P,H)=>e.jsx(B.div,{initial:{opacity:0,x:-20,scale:.95},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:20,scale:.95},transition:{delay:H*.05},className:"p-4 bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-400/20 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-gradient-to-r from-emerald-500 to-green-500 flex items-center justify-center text-white font-bold shadow-lg",children:e.jsx(Sa,{className:"w-5 h-5"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("div",{className:"text-lg font-bold text-white truncate",children:P.title||P.youtubeVideoId||"Música"}),e.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-500/20 px-2 py-1 rounded",children:new Date(P.at).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})})]}),P.artist&&e.jsx("div",{className:"text-base text-emerald-200 mb-2 truncate",children:P.artist}),P.message&&e.jsxs("div",{className:"text-base text-gray-200 mb-3 p-3 bg-white/5 rounded border-l-2 border-emerald-400",children:[e.jsxs("span",{className:"text-emerald-300 font-medium",children:[P.clientName?`De: ${P.clientName}`:P.tableNumber?`Mesa ${P.tableNumber}`:"Recado",":"]})," ",'"',P.message,'"']}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("span",{className:"flex items-center gap-1 text-yellow-300 bg-yellow-500/20 px-2 py-1 rounded",children:[e.jsx(zt,{className:"w-3 h-3"}),"R$ ",P.amount.toFixed(2)]}),e.jsxs("span",{className:"flex items-center gap-1 text-indigo-300 bg-indigo-500/20 px-2 py-1 rounded",children:[e.jsx(Zs,{className:"w-3 h-3"}),"+",P.voteWeight," votos"]}),(P.clientName||P.tableNumber)&&e.jsxs("span",{className:"flex items-center gap-1 text-purple-300",children:[e.jsx(yt,{className:"w-3 h-3"}),P.clientName||`Mesa ${P.tableNumber}`]})]})]})]})},P.id))})})})]})]})]})})},Vt=r=>{var a;if(!r)return null;let t=String(r).trim();try{if(/^https?:\/\//i.test(t)||t.includes("youtu")){const n=/[?&#]v=([a-zA-Z0-9_-]{11})/.exec(t);if(n)return n[1];const i=/(?:embed|shorts)\/([a-zA-Z0-9_-]{11})/.exec(t);if(i)return i[1];const c=/youtu\.be\/([a-zA-Z0-9_-]{11})/.exec(t);if(c)return c[1]}}catch{}const s=((a=/([a-zA-Z0-9_-]{11})/.exec(t))==null?void 0:a[1])||null;return s||(/^[a-zA-Z0-9_-]{11}$/.test(t)?t:null)},bu=()=>{const{restaurantId:r=""}=At(),[t]=Kr(),s=d.useMemo(()=>`display_state_${r}`,[r]),a=t.get("theme")||"client",n=(t.get("popups")??"on")!=="off",[i,c]=d.useState(null),[o,l]=d.useState(null),[h,u]=d.useState([]),[p,f]=d.useState([]),[m,x]=d.useState([]),[g,w]=d.useState([]),[b,k]=d.useState(0),[S,J]=d.useState(null),Z=d.useRef(0),[se,N]=d.useState([]),[$,W]=d.useState(!0),[P,H]=d.useState(null),[re,A]=d.useState(!0),[z,le]=d.useState(!1),[X,U]=d.useState(!1),I=d.useRef(!1),{on:ee,off:Y,joinRestaurant:ye,isConnected:te}=qt();d.useEffect(()=>{r&&Dt.startCooldownTimer()},[r]);const Ee=d.useRef(null),xe=d.useRef(null),Ze=d.useRef(null),Ve=d.useRef(0),Qe=d.useRef(!1),Ue=d.useRef(null),K=d.useRef(!1),ie=d.useRef(null),Re=d.useRef(null),fe=d.useRef(null),Fe=d.useRef(0),lt=d.useRef(null),ze=d.useMemo(()=>"yt_player_container",[]),$e=d.useRef(()=>{}),qe=d.useRef(null),ke=d.useRef(null),tt=d.useRef(!1),Be=d.useCallback((C=0)=>{try{const T=Ee.current;if(!T||!Qe.current)return;typeof T.unMute=="function"&&T.unMute(),typeof T.setVolume=="function"&&T.setVolume(80),typeof T.playVideo=="function"&&T.playVideo()}catch{}C<2&&setTimeout(()=>Be(C+1),C===0?100:400)},[]),je=d.useCallback(()=>{if(I.current=!0,!z)try{const C=Ee.current,T=Qe.current;C&&T&&(Be(),le(!0))}catch{}},[z,Be]),Pe=d.useCallback(async C=>{const T=ge(C);try{const _=await fetch(T,{method:"GET",headers:{"Content-Type":"application/json"}});if(!_.ok)throw new Error(`Erro HTTP: ${_.status}`);return await _.json()}catch(_){throw console.warn(`Falha ao carregar ${C}: ${_ instanceof Error?_.message:"Erro desconhecido"}`),_}},[]),ct=d.useCallback(async()=>{const C=await Pe(`/restaurants/${r}`);C.success&&C.restaurant&&c(C.restaurant)},[r,Pe]),Ae=d.useCallback(async()=>{var C;try{const T=await Pe(`/playback/${r}/state`),_=(C=T==null?void 0:T.playbackState)==null?void 0:C.currentSong;if(T.success&&_){l(Me({id:_.id||_.youtubeId,title:_.title,artist:_.artist,youtubeVideoId:_.youtubeId,thumbnailUrl:_.thumbnailUrl,duration:_.duration}));return}}catch(T){console.warn("/playback/state indisponível",T)}},[r,Pe]),Se=d.useCallback(async()=>{try{console.log("🔄 Carregando ranking colaborativo...");const C=await Pe(`/collaborative-playlist/${r}/ranking?limit=20`);if(C.success&&C.data){const T=C.data.map(_=>({...Me(_),isInCooldown:!1}));u(T),console.log("✅ Ranking carregado com sucesso:",T.length,"músicas"),console.log("🎯 Músicas com votos:",T.filter(_=>(_.score||0)>0).map(_=>({title:_.title,score:_.score,voteCount:_.voteCount,isPaid:_.isPaid})))}}catch(C){console.error("❌ Falha ao carregar ranking colaborativo:",C)}},[r,Pe]),Ye=d.useCallback(async()=>{try{const C=await Pe(`/playback-queue/${r}`);if(C&&(Array.isArray(C.queue)||C.currentlyPlaying)){const T=(C.queue||[]).map(Me);try{const G=(C.queue||[]).map(ue=>Vt(ue.youtubeVideoId||ue.suggestionId||ue.id)).filter(ue=>!!ue);f(G)}catch{}const _=C.currentlyPlaying?Me({id:C.currentlyPlaying.youtubeVideoId||C.currentlyPlaying.id,title:C.currentlyPlaying.title,artist:C.currentlyPlaying.artist,youtubeVideoId:C.currentlyPlaying.youtubeVideoId||C.currentlyPlaying.suggestionId,thumbnailUrl:C.currentlyPlaying.thumbnailUrl,duration:C.currentlyPlaying.duration,isPaid:C.currentlyPlaying.isPaid,paymentAmount:C.currentlyPlaying.paymentAmount}):T[0]||null;_&&l(_);const Q=T[1]||null;J(Q)}}catch(C){console.warn("Falha ao carregar playback-queue:",C)}},[r,Pe]),dt=d.useCallback(async()=>{const C=new URLSearchParams({page:"1",limit:"50"}),T=await Pe(`/restaurants/${r}/playlist?${C.toString()}`);let _=[];return Array.isArray(T.results)&&(_=T.results.map(Me).filter(Q=>!!Q.youtubeVideoId),w(_)),_},[r,Pe]),Me=C=>{const _=Vt(C.youtubeVideoId||C.youtubeId||C.videoId||C.id||C.url||C.link)||"",Q=C.voteCount||0;C.isPaid&&(C.paymentAmount>=50||C.paymentAmount>=20||C.paymentAmount>=5);const G=Q;return{id:_,title:C.title||"Música Sem Título",artist:C.artist||"Artista Desconhecido",duration:C.duration||0,formattedDuration:C.formattedDuration||"0:00",thumbnailUrl:C.thumbnailUrl||(_?`https://img.youtube.com/vi/${_}/mqdefault.jpg`:""),channelName:C.channelName||C.artist||"Canal Desconhecido",youtubeVideoId:_,upvotes:C.upvotes||0,downvotes:C.downvotes||0,voteCount:Q,score:G,isPaid:C.isPaid||!1,paymentAmount:C.paymentAmount||0}},st=d.useCallback(C=>{const T=Vt(C);if(!T){console.warn("ID de vídeo inválido, ignorando:",C);return}K.current=!1;const _=Ee.current;if(!_||!Qe.current){Ue.current=T;return}if(_){try{const Q=Date.now(),G=typeof _.getVideoData=="function"?_.getVideoData():null,ue=(G==null?void 0:G.video_id)||Ze.current,me=typeof _.getPlayerState=="function"?_.getPlayerState():void 0;if(ue===T&&(Q-Ve.current<2e3||me===1||me===3))return}catch{}console.log("🎵 Tocando vídeo:",T),typeof _.loadVideoById=="function"?_.loadVideoById({videoId:T}):typeof _.cueVideoById=="function"&&(_.cueVideoById({videoId:T}),typeof _.playVideo=="function"&&_.playVideo()),Ze.current=T,Ve.current=Date.now()}},[]),vt=d.useCallback(()=>{var _;if(console.log("🎯 Escolhendo próxima música..."),console.log("📊 Estado atual:",{playlistSongs:h.length,currentlyPlaying:o==null?void 0:o.title,scheduledNext:S==null?void 0:S.title,basePlaylist:g.length}),S&&S.youtubeVideoId!==(o==null?void 0:o.youtubeVideoId))return console.log("📅 Usando música agendada pelo snapshot:",S.title,"Score:",S.score),S;S?console.log("⚠️ Música agendada é a mesma que está tocando:",S.title):console.log("ℹ️ Nenhuma música agendada pelo snapshot");const C=[...h].filter(Q=>Q.youtubeVideoId!==(o==null?void 0:o.youtubeVideoId)).sort((Q,G)=>(G.score||0)-(Q.score||0));console.log("🗳️ Músicas votadas disponíveis:",C.slice(0,3).map(Q=>({title:Q.title,score:Q.score,voteCount:Q.voteCount,isPaid:Q.isPaid})));const T=C.find(Q=>(Q.score||0)>0);if(T)return console.log("🗳️ Usando música mais votada:",T.title,"Score:",T.score),T;if(console.log("⚠️ Nenhuma música com votos encontrada, usando playlist base..."),g.length>0){if(!o)return console.log("🎵 Primeira música da playlist base:",(_=g[0])==null?void 0:_.title),g[0];const Q=(b+1)%g.length,G=g[Q];if(G)return console.log("🎵 Próxima da playlist base:",G.title,"Índice:",Q),G}if(h.length>0){const Q=h.findIndex(me=>me.youtubeVideoId===(o==null?void 0:o.youtubeVideoId)),G=Q>=0?(Q+1)%h.length:0,ue=h[G];if(ue)return console.log("🎵 Próxima do ranking:",ue.title),ue}return console.warn("❌ Nenhuma música disponível para tocar!"),null},[h,o==null?void 0:o.youtubeVideoId,g,b,S]),ut=d.useCallback(()=>{const C=vt();if(C){console.log("▶️ Tocando próxima música:",C.title),l(C);const T=g.findIndex(_=>_.youtubeVideoId===C.youtubeVideoId);T>=0&&k(T),J(_=>_&&_.youtubeVideoId===C.youtubeVideoId?null:_),st(C.youtubeVideoId),setTimeout(()=>{Se(),Ye()},1e3)}else{console.warn("❌ Nenhuma próxima música disponível - pausando reprodução");try{const T=Ee.current;T&&typeof T.pauseVideo=="function"&&T.pauseVideo(),l(null)}catch(T){console.error("Erro ao pausar player:",T)}setTimeout(()=>{Se(),Ye()},2e3)}},[vt,g,st,Se,Ye]),xt=d.useCallback(()=>{var me;const C=Date.now();if(C-Z.current<5*60*1e3)return;let Q=[...h].filter(we=>we.youtubeVideoId!==(o==null?void 0:o.youtubeVideoId)).sort((we,V)=>(V.score||0)-(we.score||0)).find(we=>(we.score||0)>0)||null;if(!Q){if(g.length>0){const we=(b+1)%g.length;Q=g[we]||null}else if(h.length>0){const we=h.findIndex(ae=>ae.youtubeVideoId===(o==null?void 0:o.youtubeVideoId)),V=we>=0?(we+1)%h.length:0;Q=h[V]||null}}Q&&Q.youtubeVideoId===(o==null?void 0:o.youtubeVideoId)&&(Q=null);const G=((me=ke.current)==null?void 0:me.youtubeVideoId)||null,ue=(Q==null?void 0:Q.youtubeVideoId)||null;if(G===ue){Z.current=C;return}J(Q||null),Z.current=C,Q&&console.log("⏭️ Próxima música agendada pelo snapshot:",Q.title)},[h,o==null?void 0:o.youtubeVideoId,g,b]),Te=d.useRef(()=>{});d.useEffect(()=>{$e.current=()=>ut()},[ut]),d.useEffect(()=>{qe.current=(o==null?void 0:o.youtubeVideoId)||null},[o==null?void 0:o.youtubeVideoId]),d.useEffect(()=>{ke.current=S},[S]),d.useEffect(()=>{Te.current=xt},[xt]),d.useEffect(()=>{const C=()=>je();return z||(window.addEventListener("click",C),window.addEventListener("keydown",C),window.addEventListener("touchstart",C,{passive:!0})),()=>{window.removeEventListener("click",C),window.removeEventListener("keydown",C),window.removeEventListener("touchstart",C)}},[je,z]),d.useEffect(()=>{if(!r)return;(async()=>{var _,Q;W(!0);try{if(!tt.current)try{const ae=localStorage.getItem(s);if(ae){const de=JSON.parse(ae),j=Date.now()-((de==null?void 0:de.ts)||0);j>=0&&j<=30*60*1e3&&((_=de.currentlyPlaying)!=null&&_.youtubeVideoId&&l(Me(de.currentlyPlaying)),Number.isInteger(de.baseIndex)&&k(de.baseIndex),(Q=de.scheduledNext)!=null&&Q.youtubeVideoId&&J(Me(de.scheduledNext)),tt.current=!0)}}catch{}const[G,ue,me,we]=await Promise.all([ct(),Ae(),Se(),dt()]);if(await Ye(),!(tt.current&&!!qe.current)&&!o){const de=we&&we[0]||null||vt();de&&de.youtubeVideoId&&(l(de),k(()=>{const j=we.findIndex(O=>O.youtubeVideoId===de.youtubeVideoId);return j>=0?j:0}),st(de.youtubeVideoId))}}catch{H("Erro ao carregar dados do restaurante")}finally{W(!1)}})(),xt();const T=setInterval(()=>{Se().finally(()=>{var _;(_=Te.current)==null||_.call(Te)})},3e5);return()=>clearInterval(T)},[r]),d.useEffect(()=>{if(!r)return;const C=()=>{Se().finally(()=>{var _;(_=Te.current)==null||_.call(Te)})},T=()=>{document.visibilityState==="visible"&&C()};return window.addEventListener("focus",C),document.addEventListener("visibilitychange",T),()=>{window.removeEventListener("focus",C),document.removeEventListener("visibilitychange",T)}},[r,Se]),d.useEffect(()=>{if(!r)return;const C=V=>{try{const ae=Array.isArray(V==null?void 0:V.queue)?V.queue:null;if(ae&&ae.length>0){const de=ae.map(Me),j=de[0],O=de[1]||null;j&&l(j),J(O);try{const M=ae.map(y=>Vt(y.youtubeVideoId||y.suggestionId||y.id)).filter(y=>!!y);f(M)}catch{}}else Ye()}catch(ae){console.warn("Falha ao aplicar queue-update no Display:",ae)}finally{Se().finally(()=>{var ae;(ae=Te.current)==null||ae.call(Te)})}},T=V=>{var ae,de,j,O,M,y,E,R;try{const q=Vt((ae=V==null?void 0:V.video)==null?void 0:ae.youtubeVideoId);if(!q)return;const ne=qe.current,L=Date.now()-(Ve.current||0)<1500;if(ne===q&&L)return;const oe=Me({id:((de=V==null?void 0:V.video)==null?void 0:de.suggestionId)||q,title:(j=V==null?void 0:V.video)==null?void 0:j.title,artist:(O=V==null?void 0:V.video)==null?void 0:O.artist,youtubeVideoId:q,thumbnailUrl:(M=V==null?void 0:V.video)==null?void 0:M.thumbnailUrl,duration:(y=V==null?void 0:V.video)==null?void 0:y.duration,isPaid:(E=V==null?void 0:V.video)==null?void 0:E.isPaid,paymentAmount:(R=V==null?void 0:V.video)==null?void 0:R.paymentAmount});l(D=>(D==null?void 0:D.youtubeVideoId)===q&&L?D:oe),st(q);const ve=g.findIndex(D=>D.youtubeVideoId===q);ve>=0&&k(ve),J(D=>(D==null?void 0:D.youtubeVideoId)===q?null:D)}catch(q){console.warn("Falha ao processar reorderSelected:",q)}},_=V=>{console.log("🗳️ Voto recebido via WebSocket:",V);const ae={id:`${Date.now()}-${Math.random()}`,type:V.isPaid?"supervote":"vote",songTitle:V.title||"Música",clientName:V.clientName,tableNumber:V.tableNumber,amount:V.amount,timestamp:Date.now()};x(de=>[ae,...de.slice(0,9)]),n&&Tt(`Novo ${V.isPaid?"SuperVoto":"voto"} em "${V.title}"`,ae.type),console.log("🔄 Recarregando ranking devido a voto..."),Se().finally(()=>{var de;(de=Te.current)==null||de.call(Te)})},Q=V=>{const ae={id:`${Date.now()}-${Math.random()}`,type:"suggestion",songTitle:V.title||"Nova música",timestamp:Date.now()};x(de=>[ae,...de.slice(0,9)]),n&&Tt(`Nova sugestão: "${V.title}"`,"suggestion"),Se().finally(()=>{xt()})},G=V=>{const ae=Me({id:(V==null?void 0:V.id)||(V==null?void 0:V.youtubeId),title:V==null?void 0:V.title,artist:V==null?void 0:V.artist,youtubeVideoId:V==null?void 0:V.youtubeId,thumbnailUrl:V==null?void 0:V.thumbnailUrl,duration:V==null?void 0:V.duration});l(de=>de&&de.youtubeVideoId===ae.youtubeVideoId?de:ae)},ue=V=>{console.log("🔄 Playlist reordenada - recarregando ranking e fila:",V),Promise.all([Ye(),Se()]).finally(()=>{var ae;(ae=Te.current)==null||ae.call(Te)})},me=V=>{try{console.log("📸 Recebido snapshot de ranking:",V);const ae=V!=null&&V.nextTrack?Me(V.nextTrack):null;ae&&ae.youtubeVideoId!==qe.current?(console.log("📅 Agendando próxima música do snapshot:",ae.title,"Score:",ae.score),J(ae)):ae?console.log("⚠️ Próxima música do snapshot é a mesma que está tocando:",ae.title):console.log("⚠️ Snapshot não contém próxima música válida")}catch(ae){console.error("❌ Erro ao processar snapshot:",ae)}},we=V=>{console.log("🎵 Música terminou (WebSocket):",V),setTimeout(()=>{Se(),Ye()},500)};return Re.current!==r&&(ye(r),Re.current=r),ee("queue-update",C),ee("reorderSelected",T),ee("vote-update",_),ee("new-suggestion",Q),ee("now-playing",G),ee("playlistReordered",ue),ee("ranking-snapshot",me),ee("song-ended",we),ee("ranking-update",ue),ee("normalVoteReceived",ue),()=>{Y("queue-update",C),Y("reorderSelected",T),Y("vote-update",_),Y("new-suggestion",Q),Y("now-playing",G),Y("playlistReordered",ue),Y("ranking-snapshot",me),Y("song-ended",we),Y("ranking-update",ue),Y("normalVoteReceived",ue)}},[r,ye,ee,Y,Se,Ye,n,st,xt]);let ft=window.__ytApiPromise||null;const at=()=>typeof window<"u"&&window.YT&&window.YT.Player?Promise.resolve():(ft||(ft=new Promise(C=>{const T=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=()=>{try{typeof T=="function"&&T()}catch{}C()};const _=document.createElement("script");_.src="https://www.youtube.com/iframe_api",document.body.appendChild(_)}),window.__ytApiPromise=ft),ft);d.useEffect(()=>{let C=!1;return at().then(()=>{if(C)return;const T=window.YT;if(!T||!T.Player||Ee.current)return;const _=xe.current||document.getElementById(ze);if(!_){console.warn("⚠️ Container do player não encontrado, adiando criação");return}Ee.current=new T.Player(_,{height:"100%",width:"100%",host:"https://www.youtube-nocookie.com",playerVars:{autoplay:1,controls:0,rel:0,modestbranding:1,fs:1,enablejsapi:1,origin:window.location.protocol+"//"+window.location.host,playsinline:1},events:{onReady:()=>{var me,we,V,ae,de;console.log("🎵 Player pronto");try{Qe.current=!0}catch{}try{!I.current&&!z&&((we=(me=Ee.current)==null?void 0:me.mute)==null||we.call(me));const j=((ae=(V=Ee.current)==null?void 0:V.getIframe)==null?void 0:ae.call(V))||null;j&&j.setAttribute("allow","autoplay; fullscreen; encrypted-media; picture-in-picture"),(I.current||z)&&Be()}catch{}const Q=Ue.current&&Vt(Ue.current);if(Q){Ue.current=null,setTimeout(()=>st(Q),0);return}const G=qe.current,ue=ke.current;if(!G&&ue)(de=$e.current)==null||de.call($e);else if(G){const j=Vt(G);j&&setTimeout(()=>st(j),0)}},onStateChange:Q=>{var G;if(ie.current=(Q==null?void 0:Q.data)??null,Q.data===0){const ue=qe.current;if(console.log("🎵 Música terminou:",ue),fe.current===ue){if(Fe.current+=1,Fe.current>=3){console.error("🚨 Loop infinito detectado! Pausando reprodução para:",ue);try{const me=Ee.current;me&&typeof me.pauseVideo=="function"&&me.pauseVideo()}catch{}return}}else fe.current=ue,Fe.current=1;K.current=!0,(G=$e.current)==null||G.call($e)}if(Q.data===1&&(I.current||z)){try{Be()}catch{}z||le(!0)}},onError:Q=>{var me;const G=Q==null?void 0:Q.data;console.error("❌ Erro no player:",G,"-",G===2?"Parâmetro inválido (verifique videoId ou formato)":G===5?"Erro HTML5 (tente recarregar)":G===100?"Vídeo removido/privado":G===101||G===150?"Embed desativado pelo proprietário":"Erro desconhecido"),(me=$e.current)==null||me.call($e)}}})}),()=>{var T,_;C=!0;try{(_=(T=Ee.current)==null?void 0:T.destroy)==null||_.call(T)}catch{}}},[ze]),d.useEffect(()=>{if(!(o!=null&&o.youtubeVideoId))return;if(K.current=!1,!Qe.current){Ue.current=o.youtubeVideoId;return}const T=setTimeout(()=>st(o.youtubeVideoId),50);return()=>clearTimeout(T)},[o==null?void 0:o.youtubeVideoId,st]),d.useEffect(()=>{if(r)try{const C={ts:Date.now(),currentlyPlaying:o?{id:o.id,title:o.title,artist:o.artist,duration:o.duration,formattedDuration:o.formattedDuration,thumbnailUrl:o.thumbnailUrl,channelName:o.channelName,youtubeVideoId:o.youtubeVideoId,upvotes:o.upvotes,downvotes:o.downvotes,voteCount:o.voteCount,isPaid:o.isPaid,paymentAmount:o.paymentAmount,clientName:o.clientName,tableNumber:o.tableNumber,createdAt:o.createdAt,score:o.score}:null,baseIndex:b,scheduledNext:S?{id:S.id,title:S.title,artist:S.artist,duration:S.duration,formattedDuration:S.formattedDuration,thumbnailUrl:S.thumbnailUrl,channelName:S.channelName,youtubeVideoId:S.youtubeVideoId,upvotes:S.upvotes,downvotes:S.downvotes,voteCount:S.voteCount,isPaid:S.isPaid,paymentAmount:S.paymentAmount,clientName:S.clientName,tableNumber:S.tableNumber,createdAt:S.createdAt,score:S.score}:null};localStorage.setItem(s,JSON.stringify(C))}catch{}},[r,o==null?void 0:o.youtubeVideoId,b,S==null?void 0:S.youtubeVideoId]),d.useEffect(()=>{if(Qe.current&&!o&&S){try{r&&S.youtubeVideoId&&Dt.markSongInCooldown(r,S.youtubeVideoId)}catch{}ut()}},[S,o,ut]),d.useEffect(()=>{let C;return C=setInterval(()=>{var _,Q;try{const G=Ee.current;if(!G||!Qe.current)return;const ue=typeof G.getPlayerState=="function"?G.getPlayerState():void 0;if(ue!==1&&ue!==2&&ue!==3)return;if(I.current)try{(typeof G.isMuted=="function"?G.isMuted():!1)&&((_=G.unMute)==null||_.call(G),(Q=G.setVolume)==null||Q.call(G,80))}catch{}const me=typeof G.getDuration=="function"?G.getDuration():0,we=typeof G.getCurrentTime=="function"?G.getCurrentTime():0;me&&we&&me-we<=1&&(K.current||(K.current=!0,console.log("⏭️ Avançando pelo watcher (fim detectado)"),ut()))}catch{}},500),()=>clearInterval(C)},[ut]);const rt=d.useCallback(()=>{var C,T,_;try{document.fullscreenElement?(_=document.exitFullscreen)==null||_.call(document):(T=(C=lt.current)==null?void 0:C.requestFullscreen)==null||T.call(C)}catch{}},[]);d.useEffect(()=>{const C=()=>U(!!document.fullscreenElement);return document.addEventListener("fullscreenchange",C),()=>document.removeEventListener("fullscreenchange",C)},[]),d.useEffect(()=>{let C;const T=()=>{A(!0),clearTimeout(C),C=setTimeout(()=>A(!1),8e3)};return document.addEventListener("mousemove",T),document.addEventListener("keypress",T),T(),()=>{document.removeEventListener("mousemove",T),document.removeEventListener("keypress",T),clearTimeout(C)}},[]);const Tt=(C,T)=>{const _=`${Date.now()}-${Math.random()}`;N(Q=>[...Q,{id:_,text:C,type:T}]),setTimeout(()=>N(Q=>Q.filter(G=>G.id!==_)),5e3)};return $?e.jsx(vu,{}):P&&h.length===0&&g.length===0?e.jsx(wu,{error:P}):e.jsxs("div",{ref:lt,className:`relative w-screen h-screen ${a==="client"?"bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900":"bg-black"} text-white overflow-hidden`,style:{cursor:re?"default":"none"},children:[e.jsx("div",{id:ze,ref:xe,className:"absolute inset-0 z-10"}),!z&&e.jsx("div",{className:"absolute inset-0 z-30 flex items-center justify-center pointer-events-none",children:e.jsxs("button",{onClick:je,className:"pointer-events-auto inline-flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-xl shadow-xl border border-white/20 backdrop-blur-md focus:outline-none focus-visible:ring-4 focus-visible:ring-emerald-300/40",children:[e.jsx(er,{className:"w-5 h-5"}),e.jsx("span",{className:"font-semibold",children:"Ativar áudio"})]})}),e.jsx(et,{children:re&&e.jsx(B.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 z-20 pointer-events-none",children:e.jsx(ju,{currentlyPlaying:o})})}),re&&e.jsx("div",{className:"absolute top-4 right-4 z-30 pointer-events-auto",children:e.jsxs("button",{onClick:rt,className:"inline-flex items-center gap-2 bg-white/15 hover:bg-white/25 text-white px-3 py-2 rounded-lg border border-white/30 backdrop-blur-md focus:outline-none focus-visible:ring-4 focus-visible:ring-white/30","aria-label":X?"Sair da tela cheia":"Entrar em tela cheia",title:X?"Sair da tela cheia":"Tela cheia",children:[X?e.jsx(mn,{size:18}):e.jsx(sa,{size:18}),e.jsx("span",{className:"text-sm font-medium hidden sm:inline",children:X?"Sair":"Tela cheia"})]})}),e.jsx("div",{className:"absolute top-20 right-4 z-30 space-y-2",children:e.jsx(et,{children:se.map(C=>e.jsx(B.div,{initial:{opacity:0,x:100},animate:{opacity:1,x:0},exit:{opacity:0,x:100},className:`bg-white/20 backdrop-blur-md rounded-lg p-3 border border-white/30 flex items-center space-x-3 text-sm ${C.type==="supervote"?"text-yellow-300":C.type==="vote"?"text-green-300":"text-blue-300"}`,children:e.jsx("span",{children:C.text})},C.id))})})]})},vu=()=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-white mb-4"}),e.jsx("p",{className:"text-white text-xl",children:"Carregando playlist..."})]})}),wu=({error:r})=>e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-900 via-purple-900 to-indigo-900 flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-red-400 text-6xl mb-4",children:"⚠️"}),e.jsx("p",{className:"text-white text-xl mb-4",children:r}),e.jsx("button",{onClick:()=>window.location.reload(),className:"bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors",children:"Tentar Novamente"})]})}),ju=ce.memo(({currentlyPlaying:r})=>e.jsx(B.div,{initial:{y:100},animate:{y:0},exit:{y:100},className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-6 pointer-events-auto",children:e.jsx("div",{className:"grid grid-cols-1 gap-6",children:e.jsx(Nu,{song:r})})})),Nu=({song:r})=>{const t=d.useMemo(()=>r?!!r.isPaid||Number(r.voteCount||0)>0||Number(r.score||0)>0?"Votação":"Playlist":null,[r]);return e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center gap-2",children:[e.jsx(Ce,{className:"",size:20}),e.jsx("span",{children:"Tocando Agora"}),t&&e.jsx("span",{className:`text-xs px-2 py-1 rounded font-bold ${t==="Votação"?"bg-green-500 text-black":"bg-blue-500 text-white"}`,children:t})]}),r?e.jsx("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("img",{src:r.thumbnailUrl,alt:r.title,className:"w-16 h-16 rounded-lg object-cover"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:"text-white font-semibold truncate",children:r.title}),e.jsx("p",{className:"text-gray-300 text-sm truncate",children:r.artist})]})]})}):e.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10 text-center text-gray-400",children:[e.jsx(Ce,{size:32,className:"mx-auto mb-2 opacity-50"}),e.jsx("p",{children:"Aguardando próxima música..."})]})]})},ku=()=>{var o;const{notifications:r,removeNotification:t,addNotification:s}=Bl(),{settings:a}=Vn();d.useEffect(()=>{let l;return pt(()=>Promise.resolve().then(()=>ed),void 0).then(({wsService:h})=>{const u=p=>{const f=p.notification||p;s({type:f.type||"info",title:f.title||"Notificação",message:f.message||String((f==null?void 0:f.message)??""),duration:f.duration||5e3})};h.on("notification",u),l=()=>h.off("notification",u)}).catch(()=>{}),()=>{l&&l()}},[s]);const n=l=>{switch(l){case"success":return{icon:hn,bgColor:"bg-green-50 dark:bg-green-900/20",borderColor:"border-green-200 dark:border-green-800",iconColor:"text-green-600 dark:text-green-400",titleColor:"text-green-900 dark:text-green-100",messageColor:"text-green-700 dark:text-green-300"};case"error":return{icon:ls,bgColor:"bg-red-50 dark:bg-red-900/20",borderColor:"border-red-200 dark:border-red-800",iconColor:"text-red-600 dark:text-red-400",titleColor:"text-red-900 dark:text-red-100",messageColor:"text-red-700 dark:text-red-300"};case"warning":return{icon:ta,bgColor:"bg-yellow-50 dark:bg-yellow-900/20",borderColor:"border-yellow-200 dark:border-yellow-800",iconColor:"text-yellow-600 dark:text-yellow-400",titleColor:"text-yellow-900 dark:text-yellow-100",messageColor:"text-yellow-700 dark:text-yellow-300"};case"info":default:return{icon:Oi,bgColor:"bg-blue-50 dark:bg-blue-900/20",borderColor:"border-blue-200 dark:border-blue-800",iconColor:"text-blue-600 dark:text-blue-400",titleColor:"text-blue-900 dark:text-blue-100",messageColor:"text-blue-700 dark:text-blue-300"}}},i=(()=>{switch(a.notificationPosition){case"top-left":return"top-4 left-4";case"bottom-right":return"bottom-4 right-4";case"bottom-left":return"bottom-4 left-4";case"top-right":default:return"top-4 right-4"}})(),c=(o=a.notificationPosition)==null?void 0:o.includes("left");return e.jsx("div",{className:`fixed ${i} z-50 space-y-2 max-w-sm w-full`,children:e.jsx(et,{children:r.map(l=>{const h=n(l.type),u=h.icon;return e.jsxs(B.div,{initial:{opacity:0,x:c?-300:300,scale:.9},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:c?-300:300,scale:.9},transition:{type:"spring",stiffness:500,damping:30},className:`
                ${h.bgColor} ${h.borderColor}
                border rounded-lg shadow-lg p-4 backdrop-blur-sm
              `,children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(u,{className:`w-5 h-5 ${h.iconColor}`})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h4",{className:`text-sm font-medium ${h.titleColor}`,children:l.title}),e.jsx("p",{className:`text-sm mt-1 ${h.messageColor}`,children:l.message}),l.action&&e.jsx("button",{onClick:l.action.onClick,className:`
                        text-sm font-medium mt-2 hover:underline
                        ${h.iconColor}
                      `,children:l.action.label})]}),e.jsx("button",{onClick:()=>t(l.id),className:`
                    flex-shrink-0 p-1 rounded-md hover:bg-black/5 dark:hover:bg-white/5
                    ${h.iconColor} transition-colors
                  `,children:e.jsx(jt,{className:"w-4 h-4"})})]}),l.duration&&l.duration>0&&e.jsx(B.div,{initial:{width:"100%"},animate:{width:"0%"},transition:{duration:l.duration/1e3,ease:"linear"},className:`
                    h-1 mt-3 rounded-full
                    ${h.iconColor.replace("text-","bg-")}
                    opacity-30
                  `})]},l.id)})})})},Su=({children:r})=>{const{isAuthenticated:t,user:s,authToken:a}=ys();return At(),console.log("🔐 ProtectedRoute - Estado:",{isAuthenticated:t,user:!!s,authToken:!!a}),a&&!s?(console.log("🔐 Carregando autenticação..."),e.jsx("div",{className:"min-h-screen flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Zt,{size:"lg"}),e.jsx("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Verificando autenticação..."})]})})):!t||!s||!a?(console.log("🔐 Não autenticado, redirecionando para login"),e.jsx(Ft,{to:"/admin/login",replace:!0})):["admin","moderator","staff"].includes(s.role)?(console.log("🔐 Acesso autorizado ao dashboard"),e.jsx(e.Fragment,{children:r})):(console.log("🔐 Sem permissão, redirecionando para home"),e.jsx(Ft,{to:"/",replace:!0}))},Cu=()=>{const{authToken:r,setUser:t,setAuthToken:s,isAuthenticated:a}=ys();d.useEffect(()=>{(()=>{const i=localStorage.getItem("authToken");if(!i){r&&(s(null),t(null));return}r!==i&&(Ge.setAuthToken(i),s(i),t({id:"admin-user",name:"Admin",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}),console.log("🔐 Token de autenticação restaurado"))})()},[])};const Eu=new oo({defaultOptions:{queries:{retry:3,retryDelay:r=>Math.min(1e3*2**r,3e4),staleTime:5*60*1e3,cacheTime:10*60*1e3,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}}),Ru=({children:r})=>{const{isAuthenticated:t,user:s}=Xt();return!t||!s?e.jsx(Ft,{to:"/admin/login",replace:!0}):["admin","moderator","super_admin"].includes(s.role)?e.jsx(e.Fragment,{children:r}):e.jsx(Ft,{to:"/",replace:!0})},Pu=()=>{const{isOnline:r,connectionStatus:t,setOnlineStatus:s,setConnectionStatus:a}=Xt(),{settings:n}=Vn();return Cu(),d.useEffect(()=>{Ql();const i=De.onConnectionStatusChange(l=>{a(l)}),c=()=>s(!0),o=()=>s(!1);return window.addEventListener("online",c),window.addEventListener("offline",o),()=>{i(),window.removeEventListener("online",c),window.removeEventListener("offline",o)}},[s,a]),e.jsxs(po,{client:Eu,children:[e.jsx(di,{children:e.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200",children:[e.jsxs(Hr,{children:[e.jsx(be,{path:"/",element:e.jsx(Wl,{})}),e.jsx(be,{path:"/restaurant/:restaurantId/dashboard/*",element:e.jsx(Su,{children:e.jsx(ud,{})})}),e.jsx(be,{path:"/restaurant/dashboard/*",element:e.jsx(Ft,{to:"/restaurant/demo-restaurant/dashboard",replace:!0})}),e.jsx(be,{path:"/test",element:e.jsx("div",{style:{padding:"20px",fontSize:"24px"},children:"Teste de Rota Funcionando!"})}),!1,e.jsx(be,{path:"/restaurant/public/:restaurantId",element:e.jsx(Ja,{})}),e.jsx(be,{path:"/couvert/:restaurantId",element:e.jsx(Za,{})}),e.jsx(be,{path:"/cover/:restaurantId",element:e.jsx(Za,{})}),e.jsx(be,{path:"/display/:restaurantId",element:e.jsx(bu,{})}),e.jsx(be,{path:"/admin/login",element:e.jsx(Yl,{})}),e.jsx(be,{path:"/admin/*",element:e.jsx(Ru,{children:e.jsx(tc,{})})}),e.jsx(be,{path:"/login",element:e.jsx(Gl,{})}),e.jsx(be,{path:"/analytics",element:e.jsx(Nd,{})}),e.jsx(be,{path:"/client/:restaurantId",element:e.jsx(Ja,{})}),e.jsx(be,{path:"*",element:e.jsx(kd,{})})]}),e.jsx(ku,{}),e.jsx(In,{position:n.notificationPosition||"top-left",toastOptions:{duration:4e3,style:{background:"var(--toast-bg)",color:"var(--toast-color)",border:"1px solid var(--toast-border)"},success:{iconTheme:{primary:"#10B981",secondary:"#FFFFFF"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FFFFFF"}}}})]})}),!1]})};"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(r=>{console.log("SW registered: ",r)}).catch(r=>{console.log("SW registration failed: ",r)})});const ri=()=>{const r=document.documentElement;r.style.setProperty("--toast-bg","rgb(255 255 255)"),r.style.setProperty("--toast-color","rgb(17 24 39)"),r.style.setProperty("--toast-border","rgb(229 231 235)"),window.matchMedia("(prefers-color-scheme: dark)").matches&&(r.style.setProperty("--toast-bg","rgb(31 41 55)"),r.style.setProperty("--toast-color","rgb(243 244 246)"),r.style.setProperty("--toast-border","rgb(75 85 99)"))};ri();window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",ri);wr.createRoot(document.getElementById("root")).render(e.jsx(ce.StrictMode,{children:e.jsx(Pu,{})}));export{$n as A,Vu as V,Ps as Y,he as _,Gt as a,ge as b,v as c,Ge as d,Vn as e,Kt as g,e as j,Xn as u};
//# sourceMappingURL=index-de865c15.js.map
