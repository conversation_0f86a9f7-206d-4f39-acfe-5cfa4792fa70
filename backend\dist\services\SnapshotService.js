"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.snapshotService = exports.SnapshotService = void 0;
const database_1 = require("../config/database");
const Suggestion_1 = require("../models/Suggestion");
const Payment_1 = require("../models/Payment");
const PlaylistTrack_1 = require("../models/PlaylistTrack");
const WebSocketService_1 = require("./WebSocketService");
class SnapshotService {
    constructor() {
        this.snapshots = new Map();
        this.suggestionRepository = database_1.AppDataSource.getRepository(Suggestion_1.Suggestion);
        this.paymentRepository = database_1.AppDataSource.getRepository(Payment_1.Payment);
        this.playlistTrackRepository = database_1.AppDataSource.getRepository(PlaylistTrack_1.PlaylistTrack);
        this.webSocketService = WebSocketService_1.WebSocketService.getInstance();
    }
    async generateSnapshot(restaurantId) {
        try {
            // Get all active suggestions with votes and payments
            const suggestions = await this.suggestionRepository
                .createQueryBuilder("suggestion")
                .leftJoin("payments", "payment", "payment.suggestion_id = suggestion.id AND payment.status = 'paid'")
                .addSelect([
                "COALESCE(SUM(payment.amount), 0) as payment_amount",
                "COUNT(CASE WHEN payment.id IS NOT NULL THEN 1 END) > 0 as is_paid"
            ])
                .where("suggestion.restaurant_id = :restaurantId", { restaurantId })
                .andWhere("suggestion.status IN ('pending', 'approved')")
                .groupBy("suggestion.id")
                .orderBy("is_paid", "DESC")
                .addOrderBy("payment_amount", "DESC")
                .addOrderBy("suggestion.vote_count", "DESC")
                .addOrderBy("COALESCE(suggestion.\"createdAt\", suggestion.created_at)", "ASC")
                .getRawAndEntities();
            const paidQueue = [];
            const freeQueue = [];
            let nextTrack = null;
            for (const suggestion of suggestions.entities) {
                const raw = suggestions.raw.find(r => r.suggestion_id === suggestion.id);
                const paymentAmount = parseFloat(raw?.payment_amount || '0');
                const isPaid = raw?.is_paid === true || paymentAmount > 0;
                const item = {
                    id: suggestion.id,
                    youtubeVideoId: suggestion.youtubeVideoId,
                    title: suggestion.title,
                    artist: suggestion.artist || 'Artista Desconhecido',
                    voteCount: suggestion.voteCount || 0,
                    score: this.calculateScore(suggestion, paymentAmount),
                    isPaid,
                    paymentAmount
                };
                if (isPaid) {
                    paidQueue.push({
                        ...item,
                        paymentAmount
                    });
                }
                else {
                    freeQueue.push(item);
                }
                // First item (highest priority) becomes next track
                if (!nextTrack) {
                    nextTrack = item;
                }
            }
            // If no suggestions with votes, get from base playlist
            if (!nextTrack) {
                const baseTrack = await this.playlistTrackRepository
                    .createQueryBuilder("track")
                    .innerJoin("track.playlist", "playlist")
                    .where("playlist.restaurant_id = :restaurantId", { restaurantId })
                    .andWhere("track.is_active = true")
                    .orderBy("track.position", "ASC")
                    .getOne();
                if (baseTrack) {
                    nextTrack = {
                        id: baseTrack.id,
                        youtubeVideoId: baseTrack.youtubeVideoId,
                        title: baseTrack.title,
                        artist: baseTrack.artist,
                        score: 0,
                        isPaid: false
                    };
                }
            }
            const snapshot = {
                restaurantId,
                nextTrack,
                paidQueue,
                freeQueue,
                timestamp: new Date()
            };
            // Store snapshot
            this.snapshots.set(restaurantId, snapshot);
            // Broadcast to connected clients (se disponível)
            this.webSocketService?.broadcastToRestaurant(restaurantId, 'ranking-snapshot', {
                nextTrack: snapshot.nextTrack,
                paidQueue: snapshot.paidQueue.slice(0, 10),
                freeQueue: snapshot.freeQueue.slice(0, 20),
                timestamp: snapshot.timestamp
            });
            console.log(`📸 Generated snapshot for restaurant ${restaurantId}:`, {
                nextTrack: nextTrack?.title || 'None',
                paidCount: paidQueue.length,
                freeCount: freeQueue.length
            });
            return snapshot;
        }
        catch (error) {
            console.error(`Error generating snapshot for restaurant ${restaurantId}:`, error);
            throw error;
        }
    }
    calculateScore(suggestion, paymentAmount) {
        // Paid suggestions get massive boost
        if (paymentAmount > 0) {
            return 10000 + paymentAmount * 100 + (suggestion.voteCount || 0);
        }
        // Free suggestions based on votes with time decay
        const baseScore = suggestion.voteCount || 0;
        // createdAt pode ser nulo em dados legados
        const created = suggestion.createdAt ?? suggestion.updatedAt ?? new Date();
        let createdMs;
        try {
            const t = new Date(created).getTime();
            createdMs = isFinite(t) ? t : Date.now();
        }
        catch {
            createdMs = Date.now();
        }
        const hoursOld = (Date.now() - createdMs) / (1000 * 60 * 60);
        const timeDecay = Math.exp(-hoursOld / 24); // Decay over 24 hours
        return baseScore * timeDecay;
    }
    getSnapshot(restaurantId) {
        return this.snapshots.get(restaurantId) || null;
    }
    async startPeriodicSnapshots() {
        console.log('🔄 Starting periodic snapshot generation (every 5 minutes)');
        setInterval(async () => {
            try {
                // Get all active restaurants
                const restaurants = await database_1.AppDataSource.query(`
          SELECT DISTINCT r.id
          FROM restaurants r
          INNER JOIN suggestions s ON s.restaurant_id = r.id
          WHERE r."isActive" = true
          AND s.status IN ('pending', 'approved')
          AND COALESCE(s."createdAt", s.created_at) > NOW() - INTERVAL '24 hours'
        `);
                for (const restaurant of restaurants) {
                    await this.generateSnapshot(restaurant.id);
                }
            }
            catch (error) {
                console.error('Error in periodic snapshot generation:', error);
            }
        }, 5 * 60 * 1000); // 5 minutes
    }
    async generateSnapshotForAllActiveRestaurants() {
        try {
            const restaurants = await database_1.AppDataSource.query(`
        SELECT DISTINCT r.id
        FROM restaurants r
        WHERE r."isActive" = true
      `);
            for (const restaurant of restaurants) {
                await this.generateSnapshot(restaurant.id);
            }
        }
        catch (error) {
            console.error('Error generating snapshots for all restaurants:', error);
        }
    }
}
exports.SnapshotService = SnapshotService;
// Singleton instance
exports.snapshotService = new SnapshotService();
//# sourceMappingURL=SnapshotService.js.map